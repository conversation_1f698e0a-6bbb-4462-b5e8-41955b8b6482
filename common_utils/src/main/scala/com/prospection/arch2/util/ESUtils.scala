package com.prospection.arch2.util

import com.prospection.arch2.model.FieldConstants
import org.apache.http.HttpHost
import org.apache.http.entity.ContentType
import org.apache.http.nio.entity.NStringEntity
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.types._
import org.elasticsearch.client.{Request, ResponseException, RestClient}

import scala.collection.JavaConverters._
import scala.collection.mutable.ListBuffer


class ESUtils(es_host: String, es_port: String, es_ssl: Boolean) {
    val client: RestClient = RestClient.builder(new HttpHost(es_host, es_port.toInt, if (es_ssl) "https" else "http")).build()

    def deleteIndex(index_name: String): Unit = {
        // delete index
        try {
            client.performRequest(new Request("DELETE", s"/$index_name"))
        } catch {
            case _: ResponseException => println("No existing index to drop")
        }
    }

    def createIndex(index_name: String, mapping: String): Unit = {
        val body_create_index = new NStringEntity(mapping, ContentType.APPLICATION_JSON)
        val rq = new Request("PUT", s"/$index_name")
        rq.setEntity(body_create_index)
        client.performRequest(rq)
    }

    def bulkInsert(data: List[String], index_name: String): Unit = {
        val bulkRq = new ListBuffer[String]
        val bulkRequest = new Request("POST", s"/$index_name/subject/_bulk")
        for (elem <- data) {
            val metadataLine = s""" { "index" : {} } """
            val payload = elem.mkString("")
            val requestLine = s"$metadataLine\n$payload\n"
            bulkRq += requestLine
        }
        val requestBody = bulkRq.mkString("")
        bulkRequest.setJsonEntity(requestBody)
        client.performRequest(bulkRequest)
    }

    def updateIndexSettings(index_name: String, settings: String): Unit = {
        val body_update_index = new NStringEntity(settings, ContentType.APPLICATION_JSON)
        val rq = new Request("PUT", s"/$index_name/_settings")
        rq.setEntity(body_update_index)
        client.performRequest(rq)
    }

    def dfToEs(index_name: String, df: DataFrame, shards: Int, replicas: Int, writers: Int): Unit = {

        // delete index
        deleteIndex(index_name)

        val json_create_index = getMappingFromDataFrame(df.schema, shards, replicas)
        createIndex(index_name, json_create_index)
        // TODO: revert it back once we upgrade ES to 7
        //        df.repartition(writers)
        //            .write
        //            .format("org.elasticsearch.spark.sql")
        //            .option("es.nodes", es_host)
        //            .option("es.port", es_port)
        //            //      .option("es.index.auto.create", "true")
        //            .option("es.net.ssl", es_ssl)
        //            .option("es.http.timeout", "30s")
        //            .option("es.nodes.wan.only", "true")
        //            //      .option("es.resouce.auto.create", index_name)
        //            .option("es.mapping.id", FieldConstants.subjectColumn)
        //            //      .option("es.batch.size.entries", "5")
        //            .option("es.batch.size.bytes", "5000000")
        //            .option("es.batch.write.refresh", "false")
        //            //      .mode(SaveMode.Overwrite)
        //            .save(s"$index_name/subject")
        val jsonList = df.toJSON.collectAsList.asScala.toList
        bulkInsert(jsonList, index_name)


        // update the index by setting "refresh" to the default value
        val json_update_index =
            s"""
               |{
               |  "index" : {
               |    "refresh_interval" : "1s"
               |  }
               |}
      """.stripMargin

        updateIndexSettings(index_name, json_update_index)
    }

    def getMappingFromDataFrame(schema: StructType, shards: Int, replicas: Int): String = {
        //create index mapping
        def translateType(n: String, t: DataType): String = t match {
            case _: IntegerType => s""" "$n": { "type": "integer" } """
            case _: LongType => s""" "$n": { "type": "long" } """
            case _: FloatType => s""" "$n": { "type": "float" } """
            case _: DoubleType => s""" "$n": { "type": "double" } """
            case _: StringType => s""" "$n": { "type": "keyword" } """
            case _: TimestampType => s""" "$n": { "type": "date" } """
            case _: BooleanType => s""" "$n": { "type": "boolean" } """
            case StructType(fields) =>
                val prop = fields
                    .map(f => {
                        f.dataType match {
                            case x: ArrayType => translateType(f.name, x.elementType)
                            case y: DataType => translateType(f.name, y)
                        }
                    })
                    .mkString(",")
                s""" "$n": { "type": "nested", "properties": { $prop} } """
        }

        val root_prop = schema.toList
            .filter(_.name != FieldConstants.snapshotColumn)
            .map(f => {
                f.dataType match {
                    case x: ArrayType => translateType(f.name, x.elementType)
                    case y: DataType => translateType(f.name, y)
                }
            })
            .mkString(",")

        val prop = schema.toList
            .filter(_.name == FieldConstants.snapshotColumn)
            .flatMap(_.dataType.asInstanceOf[ArrayType].elementType.asInstanceOf[StructType].toList)
            .map(f => {
                f.dataType match {
                    case x: ArrayType => translateType(f.name, x.elementType)
                    case y: DataType => translateType(f.name, y)
                }
            })
            .mkString(",")


        val es_mapping = s""" $root_prop ,"snapshots": {"type": "nested","properties": { $prop} } """

        s"""
           |{
           |    "settings" : {
           |        "index" : {
           |            "number_of_shards" : $shards,
           |            "number_of_replicas" : $replicas,
           |            "refresh_interval" : "-1"
           |        }
           |    },
           |    "mappings" : {
           |        "subject" : {
           |            "_source":{
           |              "enabled": true
           |            },
           |            "properties" : {
           |                $es_mapping
           |            }
           |        }
           |    }
           |}
      """.stripMargin

    }

}
