package com.prospection.arch2.util

import org.apache.hadoop.fs.{FileSystem, Path}

trait DataVersionValidator {

    def validate(fs: FileSystem, path: String, schemaVersion: String): Unit

    def validate(fs: FileSystem, path: String): Unit = {
        val p = new Path(path)

        if (fs.isDirectory(p)) {
            FileSystemUtils.listFilesWithMetadata(fs, path)
                .foreach(tuple => validate(fs, tuple._1.toString, tuple._2.getProperty(MetadataConstants.SCHEMA_VERSION_KEY)))
        } else {
            validate(fs, path.toString, FileSystemUtils.getMetadata(fs, path).getProperty(MetadataConstants.SCHEMA_VERSION_KEY))
        }
    }

}
