package com.prospection.arch2.etl.pbs

import com.prospection.arch2.util.DataVersionValidator
import org.apache.commons.lang3.StringUtils
import org.apache.hadoop.fs.{FileSystem, Path}

import java.io.{BufferedReader, InputStreamReader}

object PBSClaimValidator extends DataVersionValidator {
    val EXPECTED_HEADERS = "PAT_ID;ITEM_CODE;AUTH_FORM_ID;PHARMACY_STATE;SUPP_DATE;PRESC_DATE;PRESC_ID;DERIVED_SPECIALTY;FORM_CAT;CONCESSIONAL_STATUS;QTY;REPEATS;PREV_SUPP_NUM;NUMBER_OF_SCRIPTS;CoPayment_Indicator;STREAMLINE_AUTHORITY_CODE;REG25_IND"

    override def validate(fs: FileSystem, path: String, schemaVersion: String): Unit = {
        val fis = fs.open(new Path(path))
        val bufferReader = new BufferedReader(new InputStreamReader(fs.open(new Path(path))))
        try {
            val headerLine = bufferReader.readLine()

            if (StringUtils.isBlank(headerLine)) {
                throw new IllegalArgumentException(s"${getClass.getSimpleName} - $path file is empty or header is blank")
            }

            if (headerLine != EXPECTED_HEADERS) {
                throw new IllegalArgumentException(s"${getClass.getSimpleName} - $path file's headers doesn't match expected:\n$headerLine vs $EXPECTED_HEADERS")
            }

        } finally {
            if (bufferReader != null) {
                bufferReader.close()
            }
            fis.close()
        }
    }

}
