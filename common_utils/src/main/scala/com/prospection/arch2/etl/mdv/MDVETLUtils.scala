package com.prospection.arch2.etl.mdv

import org.apache.spark.sql.types.StructType
import org.apache.spark.sql.{DataFrame, SparkSession}

object MDVETLUtils {

    val DELIMITER = "\t"

    def read(spark: SparkSession, inputSrc: String): DataFrame = {

        spark
            .read
            .option("sep", DELIMITER)
            .option("header", "true")
            .csv(inputSrc)
    }

    def read(spark: SparkSession, inputSrc: String, structType: StructType): DataFrame = {

        spark
            .read
            .option("sep", DELIMITER)
            .option("header", "true")
            .schema(structType)
            .csv(inputSrc)

    }

}
