package com.prospection.arch2.etl.pbs

import com.prospection.arch2.util.DataVersionValidator
import org.apache.commons.lang3.StringUtils
import org.apache.hadoop.fs.{FileSystem, Path}

import java.io.{BufferedReader, InputStreamReader}

object PBSPatientValidator extends DataVersionValidator {
    val EXPECTED_HEADERS = "PAT_ID;PATIENT_SEX;PATIENT_YEAR_OF_BIRTH;PATIENT_YEAR_OF_DEATH"

    override def validate(fs: FileSystem, path: String, schemaVersion: String): Unit = {
        val fis = fs.open(new Path(path))
        val bufferReader = new BufferedReader(new InputStreamReader(fs.open(new Path(path))))
        try {
            val headerLine = bufferReader.readLine()

            if (StringUtils.isBlank(headerLine)) {
                throw new IllegalArgumentException(s"${getClass.getSimpleName} - $path file is empty or header is blank")
            }

            if (headerLine != EXPECTED_HEADERS) {
                throw new IllegalArgumentException(s"${getClass.getSimpleName} - $path file's headers doesn't match expected:\n$headerLine vs $EXPECTED_HEADERS")
            }

        } finally {
            if (bufferReader != null) {
                bufferReader.close()
            }
            fis.close()
        }
    }

}
