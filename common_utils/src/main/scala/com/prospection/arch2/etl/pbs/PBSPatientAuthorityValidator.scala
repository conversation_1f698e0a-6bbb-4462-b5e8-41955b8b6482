package com.prospection.arch2.etl.pbs

import com.prospection.arch2.util.DataVersionValidator
import org.apache.commons.lang3.StringUtils
import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.logging.log4j.scala.Logging

import java.io.{BufferedReader, InputStreamReader}

object PBSPatientAuthorityValidator extends DataVersionValidator with Logging {

    val FULL_HEADERS_WITH_SEMICOLON = "PAT_ID;AUTH_ITEM_CODE;AUTH_APPR_DTE;PRESC_ID;AUTH_RPT_NUM;RSTR_NUM;ORIG_SUPP_IND"
    val HEADER_MISSING_FIELD_WITH_SEMICOLON = "PAT_ID;AUTH_ITEM_CODE;AUTH_APPR_DTE;PRESC_ID;AUTH_RPT_NUM;RSTR_NUM"
    val HEADER_MISSING_FIELD_WITH_COLON = "PAT_ID,AUTH_ITEM_CODE,AUTH_APPR_DTE,PRESC_ID,AUTH_RPT_NUM,RSTR_NUM"

    override def validate(fs: FileSystem, path: String, schemaVersion: String): Unit = {

        val fis = fs.open(new Path(path))
        val bufferReader = new BufferedReader(new InputStreamReader(fs.open(new Path(path))))
        try {
            val headerLine = bufferReader.readLine()
            val dataLine = bufferReader.readLine()

            if (StringUtils.isBlank(headerLine)) {
                throw new IllegalArgumentException(s"${getClass.getSimpleName} - $path file is empty or header is blank")
            }

            schemaVersion match {

                // Current version
                case "1.0" =>
                    if (headerLine != FULL_HEADERS_WITH_SEMICOLON) {
                        throw new IllegalArgumentException(s"${getClass.getSimpleName} - $path file's headers doesn't match expected:\n"
                            + s"$headerLine vs $FULL_HEADERS_WITH_SEMICOLON")
                    }

                    if (dataLine != null && dataLine.split(";", -1).length != 7) {
                        throw new IllegalArgumentException(s"${getClass.getSimpleName} - $path second line doesn't have 7 fields")
                    }

                // version 0.4: with header and ; separated in the header, comma separated for actual data
                case "0.4" =>
                    if (headerLine != FULL_HEADERS_WITH_SEMICOLON) {
                        throw new IllegalArgumentException(s"${getClass.getSimpleName} - $path file's headers doesn't match expected:\n"
                            + s"$headerLine vs $FULL_HEADERS_WITH_SEMICOLON")
                    }

                    if (dataLine != null && dataLine.split(",", -1).length != 7) {
                        throw new IllegalArgumentException(s"${getClass.getSimpleName} - $path second line doesn't have 7 fields with comma separated")
                    }

                // version 0.3: ORIG_SUPP_IND is missing, with header and ; separated
                case "0.3" =>
                    if (headerLine != HEADER_MISSING_FIELD_WITH_SEMICOLON) {
                        throw new IllegalArgumentException(s"${getClass.getSimpleName} - $path file's headers doesn't match expected:\n"
                            + s"$headerLine vs PAT_ID;AUTH_ITEM_CODE;AUTH_APPR_DTE;PRESC_ID;AUTH_RPT_NUM;RSTR_NUM")
                    }

                    if (dataLine != null && dataLine.split(";", -1).length != 6) {
                        throw new IllegalArgumentException(s"${getClass.getSimpleName} - $path second line doesn't have 6 fields")
                    }

                // version 0.2: ORIG_SUPP_IND is missing, with header and comma separated
                case "0.2" =>
                    if (headerLine != HEADER_MISSING_FIELD_WITH_COLON) {
                        throw new IllegalArgumentException(s"${getClass.getSimpleName} - $path file's headers doesn't match expected:\n"
                            + s"$headerLine vs PAT_ID;AUTH_ITEM_CODE;AUTH_APPR_DTE;PRESC_ID;AUTH_RPT_NUM;RSTR_NUM")
                    }

                    if (dataLine != null && dataLine.split(",", -1).length != 6) {
                        throw new IllegalArgumentException(s"${getClass.getSimpleName} - $path second line doesn't have 6 fields")
                    }

                // version 0.1: ORIG_SUPP_IND is missing, no header and comma separated
                case "0.1" =>
                    if (headerLine.contains("PAT_ID")) {
                        throw new IllegalArgumentException(s"${getClass.getSimpleName} - $path file doesn't expect headers")
                    }

                    if (headerLine != null && dataLine.split(",", -1).length != 6) {
                        throw new IllegalArgumentException(s"${getClass.getSimpleName} - $path second line doesn't have 6 fields")
                    }

                case _ =>
                    if (headerLine != FULL_HEADERS_WITH_SEMICOLON) {
                        throw new IllegalArgumentException(s"${getClass.getSimpleName} - $path file's headers doesn't match expected:\n"
                            + s"$headerLine vs $FULL_HEADERS_WITH_SEMICOLON")
                    }

                    if (dataLine != null && dataLine.split(";", -1).length != 7) {
                        throw new IllegalArgumentException(s"${getClass.getSimpleName} - $path second line doesn't have 7 fields")
                    }
            }

        } finally {
            if (bufferReader != null) {
                bufferReader.close()
            }
            fis.close()
        }
    }
}
