package com.prospection.arch2.etl.pbs

import com.prospection.arch2.util.{MetadataConstants, VersionDataFrameReader}
import org.apache.logging.log4j.scala.Logging
import org.apache.spark.sql.functions.{lit, split}
import org.apache.spark.sql.types.{StringType, StructField, StructType}
import org.apache.spark.sql.{DataFrame, SparkSession}

object PBSPatientAuthorityReader extends VersionDataFrameReader with Logging {

    override def read(spark: SparkSession, path: String, schemaVersion: String): DataFrame = {

        import spark.implicits._

        schemaVersion match {

            // Current version
            case "1.0" =>
                spark
                    .read
                    .option("sep", ";")
                    .option("header", "true")
                    .csv(path)

            // version 0.4: with header and ; separated in the header, comma separated for actual data
            case "0.4" =>
                spark
                    .read
                    .option("sep", ";")
                    .option("header", "true")
                    .csv(path)
                    .withColumn("tmp", split($"PAT_ID", ","))
                    .withColumn("PAT_ID", $"tmp"(0))
                    .withColumn("AUTH_ITEM_CODE", $"tmp"(1))
                    .withColumn("AUTH_APPR_DTE", $"tmp"(2))
                    .withColumn("PRESC_ID", $"tmp"(3))
                    .withColumn("AUTH_RPT_NUM", $"tmp"(4))
                    .withColumn("RSTR_NUM", $"tmp"(5))
                    .withColumn("ORIG_SUPP_IND", $"tmp"(6))
                    .drop("tmp")

            // version 0.3: ORIG_SUPP_IND is missing, with header and ; separated
            case "0.3" =>
                spark
                    .read
                    .option("sep", ";")
                    .option("header", "true")
                    .csv(path)
                    .withColumn("ORIG_SUPP_IND", lit(" "))

            // version 0.2: ORIG_SUPP_IND is missing, with header and comma separated
            case "0.2" =>
                spark
                    .read
                    .option("sep", ",")
                    .option("header", "true")
                    .csv(path)
                    .withColumn("ORIG_SUPP_IND", lit(" "))

            // version 0.1: ORIG_SUPP_IND is missing, no header and comma separated
            case "0.1" =>
                spark
                    .read
                    .option("sep", ",")
                    .option("header", "false")
                    .schema(StructType(Array(
                        StructField("PAT_ID", StringType, nullable = false),
                        StructField("AUTH_ITEM_CODE", StringType, nullable = false),
                        StructField("AUTH_APPR_DTE", StringType, nullable = false),
                        StructField("PRESC_ID", StringType, nullable = false),
                        StructField("AUTH_RPT_NUM", StringType, nullable = false),
                        StructField("RSTR_NUM", StringType, nullable = false))))
                    .csv(path)
                    .withColumn("ORIG_SUPP_IND", lit(" "))

            case _ =>
                logger.warn(s"$path has no ${MetadataConstants.SCHEMA_VERSION_KEY} metadata, try to use current version 1.0.")
                spark
                    .read
                    .option("sep", ";")
                    .option("header", "true")
                    .csv(path)
        }
    }

}
