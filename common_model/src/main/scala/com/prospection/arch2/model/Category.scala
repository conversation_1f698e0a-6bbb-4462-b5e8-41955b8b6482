package com.prospection.arch2.model

// do we actually need to know about categories past ETL?
object Category {
    val Intervention: String = "intervention"
    val Dispensing: String = "dispensing"
    val Authority: String = "authority"
    val Pathology: String = "pathology"
    val Diagnosis: String = "diagnosis"
    val Mortality: String = "mortality"
    val Material: String = "material"
    val Procedure: String = "procedure"
    val Hospitalisation: String = "hospitalisation"
}
