locals {
  enable_datadog_apm                  = true
  enable_datadog_logging              = true
  enable_ecs_service_monitoring       = true
  slack_channel_alerting_arn          = "arn:aws:sns:ap-southeast-2:102782770593:aws-chatbot-infra-alerting-ap-southeast-2-non-prod"
  is_feature_toggle_service_available = true
  service_cpu                         = 3850
}

terraform {
  source = "../../..//src"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  env_template                        = "uat"
  legacy_prefix                       = "pd-uat"
  enable_datadog_apm                  = local.enable_datadog_apm
  enable_datadog_logging              = local.enable_datadog_logging
  enable_ecs_service_monitoring       = local.enable_ecs_service_monitoring
  is_feature_toggle_service_available = local.is_feature_toggle_service_available
  slack_channel_alerting_arn          = local.slack_channel_alerting_arn
  service_cpu                         = local.service_cpu
}
