locals {
  enable_datadog_apm                  = true
  enable_datadog_logging              = true
  enable_ecs_service_monitoring       = true
  slack_channel_alerting_arn          = "arn:aws:sns:us-west-2:456133896526:aws-chatbot-infra-alerting-us-west-2-prod"
  emr_key_pair_public_key             = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCpMCMf4gZCRV+RLH8rCffYviItk5VHrp1lBQ9ITQjpK6zwtfZcHURYVXWhRd3XEaGKgIjBtjhNm+egDQkvwLxGi5BosSxrxgo1V5BtWRrNE20/L6om76I8dOhFvflv4t9wxDu8SfOTMo2YOFZGsBFdR/Wib4OCSrcwuTmmqyQWlzZZGVyTH2IcP2A8mv0IqXIcX5M4sI7Vf315jAfxMy/j1p0RSdPSYNEis6FNw5gz9gMQFUJt60OB2b8ePLfvCLHcLJ5gaEkPxAXbHvG8/xDU3RUFwAG+r0C+0pbNaLQRfQKJlTwx1F3Jp3E5EMhT8FDLkTkS5KWnwpVmkX3ZPGs/"
  service_cpu                         = 3850
  is_feature_toggle_service_available = true
}

terraform {
  source = "../../..//src"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  env_template                        = "prd"
  enable_datadog_apm                  = local.enable_datadog_apm
  enable_datadog_logging              = local.enable_datadog_logging
  enable_ecs_service_monitoring       = local.enable_ecs_service_monitoring
  slack_channel_alerting_arn          = local.slack_channel_alerting_arn
  emr_key_pair_public_key             = local.emr_key_pair_public_key
  service_cpu                         = local.service_cpu
  is_feature_toggle_service_available = local.is_feature_toggle_service_available
}
