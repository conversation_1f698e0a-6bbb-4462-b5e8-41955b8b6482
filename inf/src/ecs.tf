locals {
  command_datadog_apm_enabled = <<-EOT
  [
    "/bin/sh",
    "-c",
    ". ./set-datadog-config.sh; java -javaagent:dd-java-agent.jar -jar -Dapplication.env=v2-aws -Xmx${var.service_max_heap_size}M application.jar"
  ]
  EOT

  command_datadog_apm_disabled = <<-EOT
  [
    "java",
    "-jar",
    "-Dapplication.env=v2-aws",
    "-Xmx${var.service_max_heap_size}M",
    "application.jar"
  ]
  EOT

  log_config_datadog_logging_enabled = <<-EOT
  {
    "logDriver": "json-file"
  }
  EOT

  log_config_datadog_logging_disabled = <<-EOT
  {
    "logDriver": "awslogs",
    "options": {
      "awslogs-group": "${local.service_name}",
      "awslogs-region": "${var.aws_region}",
      "awslogs-stream-prefix": "app"
    }
  }
  EOT

  clickhouse_endpoint = lookup(data.terraform_remote_state.data.outputs.ch_endpoint, "report", "")
  is_clickhouse_available = local.clickhouse_endpoint != ""

  ecs_secrets = flatten([
    var.is_feature_toggle_service_available ? [
      {
        name      = "FEATURE_TOGGLE_SERVICE_API_TOKEN"
        valueFrom = data.terraform_remote_state.feature_toggle_service[0].outputs.api_token_arn
      }
    ] : [],
      local.is_clickhouse_available ? [
      {
        name      = "CLICKHOUSE_USERNAME"
        valueFrom = data.terraform_remote_state.data.outputs.clickhouse_username_secret_arn
      },
      {
        name      = "CLICKHOUSE_PASSWORD"
        valueFrom = data.terraform_remote_state.data.outputs.clickhouse_password_secret_arn
      }
    ] : []
  ])
}

resource "aws_ecs_task_definition" "service_task_definition" {
  family = local.service_name

  task_role_arn      = aws_iam_role.service_task_role.arn
  execution_role_arn = data.terraform_remote_state.platform.outputs.ecs_task_execution_role_arn
  network_mode       = "awsvpc"

  memory = var.service_memory

  requires_compatibilities = ["EC2"]

  container_definitions = templatefile("${path.module}/container-definitions.json.tpl", {
    cpu                                                    = var.service_cpu
    user                                                   = var.docker_user
    name                                                   = local.service_name
    image                                                  = var.service_image
    aws_region                                             = var.aws_region
    spring_profiles_active                                 = var.env_template
    country_prefix                                         = var.country_prefix
    tag_version                                            = var.tag_version
    service_prefix                                         = local.service_name
    workflow_event_queue_name                              = aws_sqs_queue.workflow_event_queue.name
    workflow_es_event_queue_name                           = aws_sqs_queue.workflow_es_event_queue.name
    workflow_clickhouse_event_queue_name                   = aws_sqs_queue.workflow_clickhouse_event_queue.name
    jobs_env                                               = local.resource_prefix
    jobs_bucket                                            = data.terraform_remote_state.data.outputs.common_bucket_name
    warehouse_bucket_url                                   = "s3://${data.terraform_remote_state.data.outputs.common_bucket_name}"
    es_bucket                                              = data.terraform_remote_state.data.outputs.elastic_search_bucket_name
    reports_es_snapshot_role_arn                           = data.terraform_remote_state.data.outputs.reports_es_snapshot_role_arn
    env_name                                               = var.env_name
    aws_account_id                                         = var.aws_account_id
    es_host                                                = var.override_es_endpoint != "" ? var.override_es_endpoint : data.terraform_remote_state.data.outputs.reports_es_domain_endpoint
    is_clickhouse_available                                = local.is_clickhouse_available
    clickhouse_endpoint                                    = local.clickhouse_endpoint
    clickhouse_database                                    = "pd_${var.country_prefix}_${var.env_name}_report_service"
    legacy_prefix                                          = var.legacy_prefix
    command                                                = var.enable_datadog_apm ? local.command_datadog_apm_enabled : local.command_datadog_apm_disabled
    log_configuration                                      = var.enable_datadog_logging ? local.log_config_datadog_logging_enabled : local.log_config_datadog_logging_disabled
    emr_default_subnets                                    = join(",", data.terraform_remote_state.platform.outputs.emr_subnets),
    emr_ssh_security_group                                 = aws_security_group.emr_allow_ssh.id
    emr_imds_v2_security_conf                              = aws_emr_security_configuration.emr_ec2_imds_v2.id
    feature_toggle_service_url                             = "https://pd-feature-toggle-service.${local.feature_toggles_resource_prefix}.prospection-internal.net"
    is_feature_toggle_service_available                    = var.is_feature_toggle_service_available
    workflow_trigger_event_sns_topic_arn                   = "arn:aws:sns:ap-southeast-2:${var.aws_account_id}:pd-au-${var.env_name}-workflow-triggering-events"
    ecs_secrets                                            = local.ecs_secrets
    reference_data_bucket                                  = data.terraform_remote_state.ref_data_service.outputs.ref_data_bucket
    lambda_data_processor_validator_small                  = aws_lambda_function.groovy_validator_lambda_small.function_name
    lambda_data_processor_validator_big                    = aws_lambda_function.groovy_validator_lambda_big.function_name
  })

  volume {
    name      = "tmp"
    docker_volume_configuration {
      scope         = "task"
      driver        = "local"
    }
  }

  tags = local.common_tags
}

resource "aws_ecs_service" "service" {
  name                  = local.service_name
  cluster               = data.terraform_remote_state.platform.outputs.ecs_cluster_id
  task_definition       = aws_ecs_task_definition.service_task_definition.arn
  desired_count         = var.service_desired_count
  wait_for_steady_state = true

  capacity_provider_strategy {
    capacity_provider = data.terraform_remote_state.platform.outputs.ecs_capacity_provider
    weight            = 1
    base              = 0
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.service_target_group.arn
    container_name   = local.service_name
    container_port   = 8080
  }

  network_configuration {
    subnets          = data.terraform_remote_state.platform.outputs.private_subnets
    security_groups  = [data.terraform_remote_state.platform.outputs.vpc_internal_sg_id]
    assign_public_ip = false
  }

  service_registries {
    registry_arn   = aws_service_discovery_service.service_discovery_service.arn
    container_name = local.service_name
  }
}

resource "aws_service_discovery_service" "service_discovery_service" {
  name = var.project_name

  dns_config {
    namespace_id = data.terraform_remote_state.platform.outputs.local_dns_namespace_id

    dns_records {
      ttl  = 60
      type = "A"
    }
  }

  health_check_custom_config {
    failure_threshold = 1
  }
}
