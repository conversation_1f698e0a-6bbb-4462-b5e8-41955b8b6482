resource "aws_iam_role" "service_task_role" {
  name               = "${local.service_name}-task-role"
  path               = "/tasks/"
  assume_role_policy = data.aws_iam_policy_document.service_task_role_assume_role_policy_document.json

  tags = local.common_tags
}

data "aws_iam_policy_document" "service_task_role_assume_role_policy_document" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
  }
}

resource "aws_iam_role_policy_attachment" "service_task_role_policy_attachment" {
  policy_arn = aws_iam_policy.service_task_role_policy.arn
  role       = aws_iam_role.service_task_role.id
}

resource "aws_iam_policy" "service_task_role_policy" {
  name   = "${local.service_name}-task-role-policy"
  policy = data.aws_iam_policy_document.service_task_role_policy_document.json
}

resource "aws_iam_role_policy_attachment" "lambda_logs_policy_attachment" {
  role       = aws_iam_role.groovy_validator_lambda_execution_role.id
  policy_arn = aws_iam_policy.lambda_logging_policy.arn
}

resource "aws_iam_policy" "lambda_logging_policy" {
  name        = "${local.resource_prefix}-lambda-logging-policy"
  policy      = data.aws_iam_policy_document.lambda_logging_policy_document.json
}

resource "aws_iam_role" "groovy_validator_lambda_execution_role" {
  name               = local.groovy_validator_lambda_role
  path               = "/lambda/"
  assume_role_policy = data.aws_iam_policy_document.lambda_assume_role_policy_document.json
}

data "aws_iam_policy_document" "lambda_logging_policy_document" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["arn:aws:logs:${var.aws_region}:${var.aws_account_id}:log-group:/aws/lambda/*:log-stream:*",]
  }
}

data "aws_iam_policy_document" "lambda_assume_role_policy_document" {
  statement {

    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}


data "aws_iam_policy_document" "service_task_role_policy_document" {
  statement {
    effect = "Allow"

    actions = [
      "s3:ListBucket"
    ]

    resources = concat(
      [
        data.terraform_remote_state.data.outputs.common_bucket_arn,
        data.terraform_remote_state.data.outputs.elastic_search_bucket_arn
      ],
      var.extra_common_output_bucket_arns_to_read
    )
  }

  statement {
    effect = "Allow"

    actions = [
      "s3:ListObjects",
      "s3:GetObject"
    ]

    resources = concat(
      [
        "${data.terraform_remote_state.data.outputs.common_bucket_arn}/*"
      ],
      formatlist("%s/*", var.extra_common_output_bucket_arns_to_read)
    )
  }

  statement {
    effect = "Allow"

    actions = [
      "s3:PutObject",
      "s3:DeleteObject"
    ]

    resources = [
      "${data.terraform_remote_state.data.outputs.common_bucket_arn}/*"
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "s3:ListObjects",
      "s3:GetObject"
    ]

    resources = [
      "${data.terraform_remote_state.data.outputs.elastic_search_bucket_arn}/*"
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "s3:PutObject",
      "s3:DeleteObject"
    ]

    resources = [
      "${data.terraform_remote_state.data.outputs.elastic_search_bucket_arn}/*"
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "batch:*"
    ]

    resources = ["*"]
  }

  statement {
    effect = "Allow"

    actions = [
      "dynamodb:BatchGet*",
      "dynamodb:DescribeStream",
      "dynamodb:DescribeTable",
      "dynamodb:Get*",
      "dynamodb:Query",
      "dynamodb:Scan",
      "dynamodb:BatchWrite*",
      "dynamodb:Delete*",
      "dynamodb:Update*",
      "dynamodb:PutItem"
    ]

    resources = [
      aws_dynamodb_table.node_status_table.arn,
      aws_dynamodb_table.workflow_status_table.arn,
      aws_dynamodb_table.generating_checkpoint_table.arn,
      aws_dynamodb_table.resumable_checkpoint_table.arn,
      "${aws_dynamodb_table.workflow_status_table.arn}/index/*"
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "sqs:SendMessage",
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage",
      "sqs:ChangeMessageVisibility",
      "sqs:GetQueueAttributes",
      "sqs:GetQueueUrl",
    ]

    resources = [
      aws_sqs_queue.workflow_event_queue.arn,
      aws_sqs_queue.workflow_es_event_queue.arn,
      aws_sqs_queue.workflow_clickhouse_event_queue.arn
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "sns:Publish"
    ]

    resources = ["arn:aws:sns:ap-southeast-2:${var.aws_account_id}:pd-au-${var.env_name}-workflow-triggering-events"]
  }

  statement {
    effect = "Allow"

    actions = [
      "iam:PassRole"
    ]

    resources = [
      data.terraform_remote_state.data.outputs.reports_es_snapshot_role_arn,
      "arn:aws:iam::${var.aws_account_id}:role/service/pd-${var.country_prefix}-${var.env_name}-emr-instance-profile-job-flow-role",
      "arn:aws:iam::${var.aws_account_id}:role/service/pd-${var.country_prefix}-${var.env_name}-emr-service-role"
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "elasticmapreduce:RunJobFlow",
      "elasticmapreduce:TerminateJobFlows",
      "elasticmapreduce:AddTags",
      "elasticmapreduce:DescribeCluster",
      "elasticmapreduce:ListInstances",
      "elasticmapreduce:ListClusters",
      "elasticmapreduce:ListInstanceFleets"
    ]

    resources = ["*"]
  }

  statement {
    effect = "Allow"

    actions = [
      "kms:Decrypt"
    ]

    resources = [data.terraform_remote_state.platform.outputs.kms_key_sqs_arn]
  }

  statement {
    effect = "Allow"

    actions = [
      "sqs:ReceiveMessage"
    ]

    resources = [aws_sqs_queue.workflow_event_queue.arn]
  }

  statement {
    effect = "Allow"

    actions = [
      "ec2:DescribeSpotPriceHistory"
    ]

    resources = ["*"]
  }

  statement {
    effect = "Allow"

    actions = [
      "pricing:GetProducts"
    ]

    resources = ["*"]
  }

  statement {
    effect = "Allow"

    actions = [
      "lambda:InvokeFunction"
    ]

    resources = [
      "arn:aws:lambda:ap-southeast-2:${var.aws_account_id}:function:pd-au-${var.env_name}-prospection-notification-service-main",
      "arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:${aws_lambda_function.groovy_validator_lambda_small.function_name}",
      "arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:${aws_lambda_function.groovy_validator_lambda_big.function_name}"
    ]
  }
}
