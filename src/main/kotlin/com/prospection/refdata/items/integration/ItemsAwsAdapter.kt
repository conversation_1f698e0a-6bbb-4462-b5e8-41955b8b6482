package com.prospection.refdata.items.integration

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.common.collect.Sets
import com.prospection.refdata.common.consts.SourceAttribute.CODE_COLUMN_NAME
import com.prospection.refdata.common.consts.SourceAttribute.CODING_SYSTEM_ATTRIBUTE_NAME
import com.prospection.refdata.common.consts.SourceAttribute.PREFIX_SOURCE_ATTRIBUTE
import com.prospection.refdata.common.domain.GeneratePublicUrlPort
import com.prospection.refdata.common.domain.ImportExportHelper
import com.prospection.refdata.common.domain.SparkImportExportHelper
import com.prospection.refdata.common.integration.ApplyRuleHelper
import com.prospection.refdata.common.integration.ApplyRuleHelper.escapeAsSparkColumnName
import com.prospection.refdata.common.integration.ApplyRuleHelper.translateFromRuleToSparkColumn
import com.prospection.refdata.common.integration.ChangeSummaryHelper.compare
import com.prospection.refdata.common.integration.LogSupport.lazyLogger
import com.prospection.refdata.common.integration.S3PathUtils.createVersionPath
import com.prospection.refdata.config.S3Path.Items
import com.prospection.refdata.config.S3Path.RAW_UPLOAD
import com.prospection.refdata.config.S3Path.RAW_UPLOAD_CSV
import com.prospection.refdata.config.S3Path.SNAPSHOTS
import com.prospection.refdata.itemgroups.integration.ItemGroupExportColumns
import com.prospection.refdata.items.ScalaSparkItemsFunctions
import com.prospection.refdata.items.domain.ItemsPort
import com.prospection.refdata.items.domain.ItemsSparkPort
import com.prospection.refdata.rules.domain.EnrichmentRule
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.apache.spark.sql.functions.array
import org.apache.spark.sql.functions.array_except
import org.apache.spark.sql.functions.broadcast
import org.apache.spark.sql.functions.coalesce
import org.apache.spark.sql.functions.col
import org.apache.spark.sql.functions.lit
import org.apache.spark.sql.functions.`when`
import org.apache.spark.sql.types.ArrayType
import org.apache.spark.sql.types.StringType
import org.jetbrains.kotlinx.spark.api.asKotlinList
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import scala.jdk.CollectionConverters
import scala.collection.Seq
import software.amazon.awssdk.core.ResponseInputStream
import software.amazon.awssdk.services.s3.model.GetObjectResponse
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*

@Component
class ItemsAwsAdapter(
    @Autowired private val importExportHelper: ImportExportHelper<ResponseInputStream<GetObjectResponse>>,
    @Autowired private val sparkImportExportHelper: SparkImportExportHelper,
    @Autowired private val itemsSparkPort: ItemsSparkPort,
    @Autowired private val generatePublicUrlPort: GeneratePublicUrlPort,
    @Autowired private val om: ObjectMapper,
) : ItemsPort {

    companion object {
        private val logger by lazyLogger()
    }

    override fun cleanupTemporaryFolder() {
        importExportHelper.deleteAll("$RAW_UPLOAD_CSV/")
    }

    override fun uploadTemporaryItems(csv: String, fileName: String) {
        importExportHelper.writeString("${RAW_UPLOAD_CSV}/${fileName}", csv)
        //Convert from csv to parquet
        sparkImportExportHelper.readCsv(RAW_UPLOAD_CSV).let {
            //Convert string array to array
            val convertedDS = ScalaSparkItemsFunctions.splitAllColumns(it)
            sparkImportExportHelper.writeParquet(RAW_UPLOAD, convertedDS)
        }
    }

    override fun getRawUpload(): Dataset<Row> {
        return sparkImportExportHelper.readParquet(RAW_UPLOAD)!!
    }

    override fun writeDraftItems(codingSystem: String, rawUpload: Dataset<Row>) {
        var ds = rawUpload
        ds.columns().forEach {
            val columnName = it as String
            ds = ds.withColumnRenamed(
                columnName,
                PREFIX_SOURCE_ATTRIBUTE + escapeAsSparkColumnName(columnName)
            )
        }
        sparkImportExportHelper.writeParquet("${Items.Draft.RAW_ITEMS}/${codingSystem}", ds)
    }

    override fun getChangeSummariesUrl(
        latestVersion: String,
        codingSystems: Set<String>,
        now: LocalDateTime
    ): String? {
        val classificationToChangeSummaryMap = codingSystems.mapNotNull { codingSystem ->
            val publishedItemDs = latestVersion.let {
                sparkImportExportHelper.readParquet(
                    "${Items.Published.ENRICHED_ITEMS_PARQUET}/${codingSystem}/${
                        createVersionPath(
                            it
                        )
                    }"
                )
            }
            val draftItemsDs =
                sparkImportExportHelper.readParquet("${Items.Draft.ENRICHED_ITEMS_PARQUET}/${codingSystem}/")

            if (draftItemsDs != null && publishedItemDs != null) {
                Pair(codingSystem, compare(draftItemsDs, publishedItemDs))
            } else {
                logger.error(
                    "Can not generate change summary for classification={}, draft/published items is missing",
                    codingSystem
                )
                null
            }
        }.associate { it }

        val classificationToChangeSummaryFileMap = classificationToChangeSummaryMap.mapNotNull { (codingSystem, cs) ->
            //write to csv, only if there is difference
            if (cs.metaData.addedRowCount != 0L && cs.metaData.deletedRowCount != 0L) {
                val changeSummaryPath = "${Items.Draft.CHANGE_SUMMARY}/${codingSystem}"
                sparkImportExportHelper.writeCsv(changeSummaryPath, cs.diffData)
                importExportHelper.findFirst(changeSummaryPath, ".csv")
                    ?.let { Pair(it, "${codingSystem}.csv") }
            } else {
                logger.info("No difference for classification={}", codingSystem)
                null
            }
        }.associate { it }

        val metadata = om.writerWithDefaultPrettyPrinter()
            .writeValueAsBytes(classificationToChangeSummaryMap.mapValues { (_, v) -> v.metaData })

        return if (classificationToChangeSummaryFileMap.isEmpty()) {
            return null
        } else {
            val zipPath = importExportHelper.writeZip(
                "${Items.Draft.CHANGE_SUMMARY}/change-summary.zip",
                classificationToChangeSummaryFileMap, metadata
            )
            generatePublicUrlPort.generatePublicUrl(
                zipPath,
                Date.from(now.plusHours(1).atZone(ZoneId.systemDefault()).toInstant())
            )
        }
    }

    override fun getEnrichedItemsDownloadUrl(codingSystem: String, now: LocalDateTime): String? {
        val csvPath = importExportHelper.findFirst("${Items.Draft.ENRICHED_ITEMS_CSV}/${codingSystem}/", ".csv")

        return if (csvPath == null) {
            null
        } else {
            generatePublicUrlPort.generatePublicUrl(
                csvPath,
                Date.from(now.plusHours(1).atZone(ZoneId.systemDefault()).toInstant())
            )
        }
    }

    override fun doesRawItemsExist(codingSystem: String): Boolean {
        return importExportHelper.findFirst(
            "${Items.Draft.RAW_ITEMS}/${codingSystem}",
            ".parquet"
        ) != null
    }

    override fun filterMatchedRowsOnly(enrichedItems: Dataset<Row>, enrichmentRule: EnrichmentRule): Dataset<Row> {
        return enrichedItems.filter(
            ScalaSparkItemsFunctions.arrayContains(
                escapeAsSparkColumnName(enrichmentRule.enrichedAttributeValue.enrichedAttribute.name),
                enrichmentRule.enrichedAttributeValue.value
            )
        )
    }

    override fun getPublishedItems(codingSystem: String, version: String): Dataset<Row>? {
        return itemsSparkPort.getPublishedItems(codingSystem, version)
    }

    override fun getRawItems(codingSystem: String): Dataset<Row>? {
        return sparkImportExportHelper.readParquet("${Items.Draft.RAW_ITEMS}/${codingSystem}")
    }

    override fun getSecondLatestSnapshotRawItems(codingSystem: String): Dataset<Row>? {
        val twoLatestKeys = importExportHelper.getTwoLatestKeys("${SNAPSHOTS}/${codingSystem}")
        if (twoLatestKeys.size < 2) {
            return null
        }

        val secondLatestKey = twoLatestKeys.first()
        var secondLatestSnapshotRawItems = sparkImportExportHelper.readParquet(secondLatestKey)!!

        secondLatestSnapshotRawItems.columns().forEach {
            secondLatestSnapshotRawItems = secondLatestSnapshotRawItems.withColumnRenamed(
                it,
                PREFIX_SOURCE_ATTRIBUTE + escapeAsSparkColumnName(it)
            )
        }

        return secondLatestSnapshotRawItems
    }

    override fun getDiffRawItems(
        currentRawItems: Dataset<Row>,
        secondLatestSnapshotRawItems: Dataset<Row>
    ): Dataset<Row> {
        val commonCols = currentRawItems.columns().toSet()
            .intersect(secondLatestSnapshotRawItems.columns().toSet())
            .map { col(it) }
            .toTypedArray()

        // Normalize datasets to have the same schema
        val normalizedCurrentDs = currentRawItems.select(*commonCols)
        val normalizedSecondLatestDs = secondLatestSnapshotRawItems.select(*commonCols)

        return normalizedCurrentDs
            .join(
                normalizedSecondLatestDs,
                CollectionConverters.SetHasAsScala(setOf(CODE_COLUMN_NAME)).asScala().toSeq(),
                "left_anti"
            )
            .withColumn("status", lit("New"))
    }

    private fun fillNullsInCols(df: Dataset<Row>, cols: Seq<String>): Dataset<Row> {
        val schema = df.schema()
        val stringCols = cols.filter { colName -> schema.apply(colName).dataType() is StringType }.asKotlinList()
        val arrayCols = cols.filter { colName -> schema.apply(colName).dataType() is ArrayType }.asKotlinList()

        val selectedColumns = df.columns().map { colName ->
            when {
                stringCols.contains(colName) -> coalesce(col(colName), lit("")).alias(colName)
                arrayCols.contains(colName) -> coalesce(col(colName), array()).alias(colName)
                else -> col(colName)
            }
        }

        return df.select(*selectedColumns.toTypedArray())
    }

    override fun getDataUpdate(
        publishedItems: Dataset<Row>,
        diffRawItems: Dataset<Row>
    ): Dataset<Row> {
        val diffRawItemsCols = diffRawItems.columns().toSet()
        val publishedItemsCols = publishedItems.columns().toSet()
        val enrichedAttributesCols = Sets.difference(publishedItemsCols, diffRawItemsCols)

        val intersectionCols = Sets.intersection(diffRawItemsCols, publishedItemsCols).sorted()
        val joinCols = CollectionConverters.SetHasAsScala(intersectionCols.toSet()).asScala().toSeq()

        val diffRawItemsFilled = fillNullsInCols(diffRawItems, joinCols)
        val publishedItemsFilled = fillNullsInCols(publishedItems, joinCols)

        // Use broadcast here since diffRawItemsFilled is always expected to be a small dataset with only new items
        return publishedItemsFilled.join(broadcast(diffRawItemsFilled), joinCols)
            .select(
                *diffRawItemsFilled.columns().map { diffRawItemsFilled.col(it) }.toTypedArray(),
                *enrichedAttributesCols.map { publishedItemsFilled.col(it) }.toTypedArray()
            )
    }

    override fun getDataUpdateBeforePublishingNewDraft(
        publishedItems: Dataset<Row>,
        diffRawItems: Dataset<Row>
    ): Dataset<Row> {
        val diffRawItemsCols = diffRawItems.columns().toSet() as Set<String>
        val publishedItemsCols = publishedItems.columns().toSet() as Set<String>
        val enrichedAttributesCols = Sets.difference(publishedItemsCols, diffRawItemsCols)

        return diffRawItems.select(
            *diffRawItemsCols.map { col(it) }.toTypedArray(),
            // Add enriched attributes
            *enrichedAttributesCols.map { lit("").`as`(it) }.toTypedArray(),
            ScalaSparkItemsFunctions.emptyArray().`as`(ItemGroupExportColumns.ITEM_GROUP_CODE.columnName)
        )
    }

    override fun createDataUpdateReportAndGetUrl(
        codingSystem: String,
        dataUpdateReport: Dataset<Row>,
        itemGroupsWithTopicsAndSubscriptions: Dataset<Row>,
        now: LocalDateTime,
        version: String
    ): String? {
        val dataUpdateReportPath =
            "${Items.Draft.DATA_UPDATE_REPORT}/${codingSystem}/DataUpdateReport-${codingSystem}-${version}.xlsx"

        sparkImportExportHelper.writeExcel(dataUpdateReport, dataUpdateReportPath, "New Items", 0)

        sparkImportExportHelper.writeExcel(
            itemGroupsWithTopicsAndSubscriptions,
            dataUpdateReportPath,
            "Topics & Subscriptions",
            1
        )

        return importExportHelper.findFirst(dataUpdateReportPath, ".xlsx")?.let {
            generatePublicUrlPort.generatePublicUrlExpirationInOneHours(it, now)
        }
    }

    override fun writeEnrichedItems(codingSystem: String, enrichedItems: Dataset<Row>) {
        sparkImportExportHelper.writeParquet("${Items.Draft.ENRICHED_ITEMS_PARQUET}/${codingSystem}", enrichedItems)
    }

    override fun writeDownloadableEnrichedItems(codingSystem: String) {
        sparkImportExportHelper.readParquet("${Items.Draft.ENRICHED_ITEMS_PARQUET}/${codingSystem}")?.let {
            sparkImportExportHelper.writeCsv("${Items.Draft.ENRICHED_ITEMS_CSV}/${codingSystem}", it)
        }
    }

    override fun applyRules(
        codingSystem: String,
        enrichmentRules: List<EnrichmentRule>
    ): Dataset<Row> {
        val rawItems = sparkImportExportHelper.readParquet("${Items.Draft.RAW_ITEMS}/${codingSystem}/")!!

        // In v2, coding system can be also evaluated by rule, therefore here we tag all rows with coding system first
        val rawItemsWithCodingSystem = rawItems.withColumn(CODING_SYSTEM_ATTRIBUTE_NAME, lit(codingSystem))

        val nonExistingSourceAttributeColumns =
            ApplyRuleHelper.getNonExistingSourceAttributeColumns(rawItems, enrichmentRules)

        val rawItemsWithAllSourceAttributeColumns =
            ApplyRuleHelper.populateNonExistingSourceAttributeColumns(
                rawItemsWithCodingSystem,
                nonExistingSourceAttributeColumns
            )

        val enrichedItems = enrichItems(enrichmentRules, rawItemsWithAllSourceAttributeColumns)

        return removeNonExistingSourceAttributeColumns(enrichedItems, nonExistingSourceAttributeColumns)
    }

    override fun writePreview(codingSystem: String, enrichedItems: Dataset<Row>): String {
        sparkImportExportHelper.writeCsv("${Items.Preview.TEMP}/${codingSystem}", enrichedItems)
        return importExportHelper.findFirst("${Items.Preview.TEMP}/${codingSystem}", ".csv")!!
    }

    override fun publishItems(version: String, codingSystems: Set<String>) {
        codingSystems.forEach { codingSystem ->
            logger.info("Publishing items of $codingSystem")

            val draftPathsToPublish = listOf(
                Pair(Items.Draft.RAW_ITEMS, Items.Published.RAW_ITEMS),
                Pair(Items.Draft.ENRICHED_ITEMS_PARQUET, Items.Published.ENRICHED_ITEMS_PARQUET)
            )

            draftPathsToPublish.forEach {
                importExportHelper.copyAll(
                    "${it.first}/${codingSystem}",
                    "${it.second}/${codingSystem}/${createVersionPath(version)}"
                )
            }
        }
    }

    override fun writeSnapshot(rawUpload: Dataset<Row>, version: String, codingSystem: String) {
        val path = "${SNAPSHOTS}/${codingSystem}/timestamp=${version}"
        sparkImportExportHelper.writeParquet(path, rawUpload)
    }

    private fun enrichItems(
        enrichmentRules: List<EnrichmentRule>,
        rawItemsWithAllSourceAttributeColumns: Dataset<Row>
    ): Dataset<Row> {
        val allSourceAttributeColumns = rawItemsWithAllSourceAttributeColumns.columns().toList()
            .map { col(it) }.toTypedArray()

        val enrichedColumns = enrichmentRules.map {
            `when`(
                translateFromRuleToSparkColumn(rawItemsWithAllSourceAttributeColumns, it.deserialisedRule),
                it.enrichedAttributeValue.value
            )
                .otherwise(null)
                .`as`(getSparkEnrichedAttributeColumnName(it))
        }.toTypedArray()

        val itemsEnrichedWithEnrichmentRules = rawItemsWithAllSourceAttributeColumns.select(
            *allSourceAttributeColumns,
            *enrichedColumns,
        )

        val enrichedAttributeToValues = enrichmentRules
            .groupBy(
                { escapeAsSparkColumnName(it.enrichedAttributeValue.enrichedAttribute.name) },
                { getSparkEnrichedAttributeColumnName(it) }
            )

        val enrichedAttributeWithoutNullValueColumns = enrichedAttributeToValues.map {
            array_except(
                array(
                    *it.value
                        .map { enrichedAttributeAndValue ->
                            itemsEnrichedWithEnrichmentRules.col(
                                enrichedAttributeAndValue
                            )
                        }
                        .toTypedArray()
                ),
                lit(listOf<String?>(null).toTypedArray())
            ).`as`(it.key)
        }
            .toTypedArray()

        return itemsEnrichedWithEnrichmentRules.select(
            *allSourceAttributeColumns,
            *enrichedAttributeWithoutNullValueColumns
        )
    }

    private fun getSparkEnrichedAttributeColumnName(enrichmentRule: EnrichmentRule) =
        escapeAsSparkColumnName(enrichmentRule.enrichedAttributeValue.enrichedAttribute.name + " " + enrichmentRule.enrichedAttributeValue.value)

    private fun removeNonExistingSourceAttributeColumns(
        enrichedItems: Dataset<Row>,
        nonExistingSourceAttributeColumns: Set<String>
    ): Dataset<Row> {
        return enrichedItems.drop(*nonExistingSourceAttributeColumns.toTypedArray())
    }
}