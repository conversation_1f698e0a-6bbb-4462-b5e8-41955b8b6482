spring:
    application:
        name: ${APPLICATION_NAME}

aws:
    region: ${AWS_REGION}

application:
    servicePrefix: ${SERVICE_PREFIX}
    country: ${COUNTRY}
    workflowEventQueueName: ${WORKFLOW_EVENT_QUEUE_NAME}
    workflowESEventQueueName: ${WORKFLOW_ES_EVENT_QUEUE_NAME}
    workflowESEventQueueMaxGroups: 1
    reportsEsSnapshotRoleArn: ${REPORTS_ES_SNAPSHOT_ROLE_ARN}
    workflowClickhouseEventQueueName: ${WORKFLOW_CLICKHOUSE_EVENT_QUEUE_NAME}
    workflowClickhouseEventQueueMaxGroups: 1
    esBucket: ${ES_BUCKET}
    integration:
        customerServiceUrl: ${CUSTOMER_SERVICE_URL}
        dashXUrl: ${DASHX_SERVICE_URL}
        refDataServiceUrl: ${REF_DATA_SERVICE_URL}
        featureToggleService:
            enabled: ${FEATURE_TOGGLE_SERVICE_AVAILABLE}
            url: ${FEATURE_TOGGLE_SERVICE_URL}
            apiToken: ${FEATURE_TOGGLE_SERVICE_API_TOKEN:unused}
        snsTopicArn:
            workflowTriggeringEvent: ${WORKFLOW_TRIGGER_EVENT_SNS_TOPIC_ARN}
    clickhouse:
        enabled: ${IS_CLICKHOUSE_AVAILABLE:false}
        endpoint: ${CLICKHOUSE_ENDPOINT:}
        username: ${CLICKHOUSE_USERNAME:}
        password: ${CLICKHOUSE_PASSWORD:}
        database: ${CLICKHOUSE_DATABASE:}
        # 15 minutes timeout for now assuming clickhouse is fast
        ingestionTimeOutInSec: 900

# Configure execution jobs.
jobs:

    local: false

    environment: ${JOBS_ENV}

    # Where will workflow items be stored prior to starting EMR job.
    bucket: ${JOBS_BUCKET}

workflow:
    default:
        subnet:
            ids: ${EMR_DEFAULT_SUBNETS}

        security-group:
            ids: ${EMR_SSH_SECURITY_GROUP}

        security-config: ${EMR_IMDS_V2_SECURITY_CONFIGURATION}

lambda:
    dataProcessorValidatorSmall: ${LAMBDA_DATA_PROCESSOR_VALIDATOR_SMALL}
    dataProcessorValidatorBig: ${LAMBDA_DATA_PROCESSOR_VALIDATOR_BIG}
    dataProcessorValidatorBigNumCores: 4
