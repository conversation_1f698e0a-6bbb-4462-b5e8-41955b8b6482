package com.pharmdash.workflow.meta.client.jobs;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.pharmdash.workflow.dp.meta.processorannotations.ReportType;
import lombok.Builder;
import lombok.Data;
import lombok.With;

@Builder(toBuilder = true)
@Data
@JsonDeserialize(builder = JobStatusDto.JobStatusDtoBuilder.class)
public class JobStatusDto {
    private Long workflowId;
    private String workflowType;
    private Long nodeId;
    private ReportType nodeReportType;
    private String jobId;
    private Long workflowSubmittedOn;
    private Long workflowStartedOn;
    private Long workflowCompletedOn;
    private Long workflowCancelledOn;
    private Long workflowCancellingStartedOn;
    private Long workflowQueuedOn;
    private Long indexingSubmittedOn;
    private Long indexingStartedOn;
    private Long indexingCompletedOn;
    private Long publishingSubmittedOn;
    private Long publishingStartedOn;
    private Long publishingCompletedOn;
    private Long ingestionStartedOn;
    private Long ingestionCompletedOn;
    private Double estimatedCost;
    @With
    private Status workflowStatus;
    @Builder.Default
    private String messages = "";
    private Long erroredOn;

    @JsonPOJOBuilder(withPrefix = "")
    public static class JobStatusDtoBuilder {
    }
}
