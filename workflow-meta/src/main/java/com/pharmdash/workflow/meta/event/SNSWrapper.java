package com.pharmdash.workflow.meta.event;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class SNSWrapper {

    @JsonProperty("MessageId")
    String messageId;

    @JsonProperty("TopicArn")
    String topicArn;

    @JsonProperty("Message")
    String message;

    @JsonProperty("Timestamp")
    String timestamp;

}
