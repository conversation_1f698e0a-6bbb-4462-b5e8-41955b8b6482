package com.pharmdash.workflow.meta.service.notification;

import com.amazonaws.services.lambda.AWSLambda;
import com.amazonaws.services.lambda.model.InvocationType;
import com.amazonaws.services.lambda.model.InvokeRequest;
import com.amazonaws.services.lambda.model.InvokeResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pharmdash.workflow.meta.client.jobs.Status;
import com.prospection.arch2.util.ConfigurationFactory;
import com.typesafe.config.Config;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;

import static com.pharmdash.workflow.meta.client.jobs.Status.CANCELLED;
import static com.pharmdash.workflow.meta.client.jobs.Status.FAILED;
import static com.pharmdash.workflow.meta.client.jobs.Status.SUCCEEDED;

@Slf4j
@Service
public class NotificationServiceImpl implements NotificationService {

    private final AWSLambda amazonLambda;
    private final ObjectMapper objectMapper;
    private final Config appConfig = ConfigurationFactory.getInstance();
    private final String notificationsSlackChannel;

    public NotificationServiceImpl(@Qualifier("awsLambdaForApSouthEast2") AWSLambda amazonLambda, ObjectMapper objectMapper, @Value("${application.notification-slack-channel}") String notificationsSlackChannel) {
        this.amazonLambda = amazonLambda;
        this.objectMapper = objectMapper;
        this.notificationsSlackChannel = notificationsSlackChannel;
    }

    @Override
    public void sendWorkflowCostNotification(Long topicId, Long workflowId, String userEmail, double cost, long runningTimeMilliseconds) throws NotificationException {
        String workflowLink = buildWorkflowLink(topicId, workflowId);
        String runningTime = convertMillisToHoursMinutes(runningTimeMilliseconds);
        String emailMessage = String.format("<p>The workflow job you triggered is still processing.</p><p>Workflow Link: %s<br/>Time: %s<br/>Approx Cost: %.2f USD</p>", workflowLink, runningTime, cost);
        String slackMessage = String.format("The workflow job you triggered is still processing.\\nWorkflow Link: %s\\nTime: %s\\nApprox Cost: %.2f USD", workflowLink, runningTime, cost);
        String title = "Workflow Cost Notification";
        EmailNotificationDto email = new EmailNotificationDto(
            emailMessage,
            userEmail,
            title,
            String.format("Workflow %s Cost Notification", workflowId));
        SlackNotificationDto slack = new SlackNotificationDto(
            userEmail,
            notificationsSlackChannel,
            title,
            slackMessage
        );
        PlatformNotificationDto platformNotificationDto = new PlatformNotificationDto(email, slack);
        sendNotification(platformNotificationDto);
    }

    private String convertMillisToHoursMinutes(long runningTimeMilliseconds) {
        Duration duration = Duration.ofMillis(runningTimeMilliseconds);
        long hours = duration.toHours();
        long minutes = duration.toMinutes() % 60;
        return String.format("%dh %dm", hours, minutes);
    }

    @Override
    public void sendWorkflowCompletionNotification(long topicId, long workflowId, String userEmail, Status workflowStatus) throws NotificationException {
        String message = getEmailMessageBasedOnWorkflowStatus(workflowStatus, buildWorkflowLink(topicId, workflowId));
        String title = "Workflow Completion Notification";

        EmailNotificationDto emailNotificationDto = EmailNotificationDto.builder()
            .message(message)
            .destination(userEmail)
            .title(title)
            .subject(String.format("Workflow %d Completion Notification", workflowId))
            .build();
        SlackNotificationDto slackNotificationDto = SlackNotificationDto.builder()
            .userId(userEmail)
            .channel(notificationsSlackChannel)
            .title(title)
            .message(message)
            .build();
        PlatformNotificationDto platformNotificationDto = new PlatformNotificationDto(emailNotificationDto, slackNotificationDto);
        sendNotification(platformNotificationDto);
    }

    private static String getEmailMessageBasedOnWorkflowStatus(Status workflowStatus, String workflowLink) {
        if (SUCCEEDED.equals(workflowStatus)) {
            return String.format("A workflow job you triggered has generated successfully - %s", workflowLink);
        } else if (FAILED.equals(workflowStatus)) {
            return String.format("A workflow job you triggered has failed - %s", workflowLink);
        } else if (CANCELLED.equals(workflowStatus)) {
            return String.format("A workflow job you triggered has been cancelled - %s", workflowLink);
        } else {
            return String.format("A workflow job you triggered has been completed with status %s - %s", workflowStatus, workflowLink);
        }
    }

    @Override
    public void sendNotification(PlatformNotificationDto notification) throws NotificationException {
        String payload;
        try {
            payload = objectMapper.writeValueAsString(notification);
        } catch (Exception e) {
            throw new NotificationException("Error preparing payload for notification", e);
        }

        int statusCode;
        try {
            InvokeRequest invokeRequest = new InvokeRequest()
                .withFunctionName(resolveNotificationFunctionName())
                .withPayload(payload)
                .withInvocationType(InvocationType.Event);

            InvokeResult invokeResult = amazonLambda.invoke(invokeRequest);
            statusCode = invokeResult.getStatusCode();
        } catch (Exception e) {
            throw new NotificationException("Unexpected error occurred while calling lambda function", e);
        }

        if (statusCode == 202) {
            log.info("Notification sent successfully");
        } else {
            throw new NotificationException("Failed to send notification. Status code: " + statusCode);
        }
    }

    private String buildWorkflowLink(Long topicId, Long workflowId) {
        return String.format("https://pd-frontend-admin.pd-au-%s.prospection-internal.net/#/topics/%s/workflow/%s", appConfig.getString("env.name"), topicId, workflowId);
    }

    private String resolveNotificationFunctionName() {
        return String.format("pd-au-%s-prospection-notification-service-main", appConfig.getString("env.name"));
    }

}
