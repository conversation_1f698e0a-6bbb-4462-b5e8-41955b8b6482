package com.pharmdash.workflow.meta.service.annotation.service.impl;

import com.pharmdash.workflow.dp.meta.processorannotations.Processor;
import com.pharmdash.workflow.dp.meta.processorannotations.ProcessorCategory;
import com.pharmdash.workflow.meta.config.CacheConfiguration;
import com.pharmdash.workflow.meta.service.annotation.service.AnnotationScannerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.stereotype.Service;
import org.springframework.web.context.support.StandardServletEnvironment;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AnnotationSpringScannerService implements AnnotationScannerService {

    @Cacheable(cacheNames = CacheConfiguration.CACHE_ANNOTATION_PROCESSORS)
    public List<String> getProcessorClassesWithGivenProcessorCategory(ProcessorCategory processorCategory) {
        ClassPathScanningCandidateComponentProvider scanner =
            new ClassPathScanningCandidateComponentProvider(false, new StandardServletEnvironment());
        scanner.addIncludeFilter(new AnnotationTypeFilter(Processor.class));

        return scanner.findCandidateComponents("com.pharmdash.workflow.dp")
            .stream()
            .map(candidateComponent -> {
                try {
                    return Class.forName(candidateComponent.getBeanClassName());
                } catch (ClassNotFoundException e) {
                    log.error("error while loading class {} to find processor classes", candidateComponent.getBeanClassName(), e);
                    return null;
                }
            })
            .filter(Objects::nonNull)
            .filter(clazz -> {
                Processor processor = clazz.getAnnotation(Processor.class);
                return processor != null && processor.value() == processorCategory;
            })
            .map(Class::getName)
            .collect(Collectors.toList());
    }
}
