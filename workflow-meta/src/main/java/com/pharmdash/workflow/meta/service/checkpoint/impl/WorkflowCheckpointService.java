package com.pharmdash.workflow.meta.service.checkpoint.impl;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.pharmdash.workflow.dp.javadto.NodeDTO;
import com.pharmdash.workflow.dp.javadto.WorkflowDTO;
import com.pharmdash.workflow.meta.service.checkpoint.CheckpointService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@RequiredArgsConstructor
public class WorkflowCheckpointService implements CheckpointService {

    @Override
    public List<String> evaluateCheckpointUpstreamNodeHashes(WorkflowDTO workflowDTO, List<String> resumableCheckpointNodeHashes, Map<Long, List<String>> s3ObjectModifiedDateNodeMap) {
        List<String> executingCheckpointUpstreamNodeHashes = new ArrayList<>();
        workflowDTO.getNodes().forEach(node -> {
            if (node.isCheckpointNode()) {
                String currentUpstreamNodesHash = generateCheckpointNodeHashMap(workflowDTO, s3ObjectModifiedDateNodeMap).get(node.getId());
                boolean doesUpstreamNodeHashAlreadyExist = resumableCheckpointNodeHashes.contains(currentUpstreamNodesHash);
                boolean shouldResume = workflowDTO.isDryRun() && doesUpstreamNodeHashAlreadyExist && !haveExternalDataFolderUpstreamNodes(workflowDTO, node, s3ObjectModifiedDateNodeMap) && !isOverrideCheckpointNotResumable(node);
                node.setShouldResume(shouldResume);
                if (!shouldResume) {
                    executingCheckpointUpstreamNodeHashes.add(currentUpstreamNodesHash);
                }
                node.setUpstreamNodesHash(currentUpstreamNodesHash);
            }
        });
        return executingCheckpointUpstreamNodeHashes;
    }

    @Override
    public Map<Long, String> generateCheckpointNodeHashMap(WorkflowDTO workflow, Map<Long, List<String>> s3ObjectModifiedDateNodeMap) {
        return workflow.getNodes().stream()
            .filter(NodeDTO::isCheckpointNode)
            .collect(Collectors.toMap(NodeDTO::getId, node -> calculateUpstreamNodesHash(node, workflow, s3ObjectModifiedDateNodeMap)));
    }

    private String calculateUpstreamNodesHash(NodeDTO targetNode, WorkflowDTO workflow, Map<Long, List<String>> s3ObjectModifiedDateNodeMap) {
        return findUpstreamNodes(targetNode, workflow)
            .map(node -> getNodeConfigWithRefData(node, workflow, s3ObjectModifiedDateNodeMap))
            .reduce((nodeValue1, nodeValue2) -> nodeValue1 + nodeValue2)
            .map(DigestUtils::md5Hex)
            .orElse(null);
    }

    private Stream<NodeDTO> findUpstreamNodes(NodeDTO node, WorkflowDTO workflow) {
        Map<Long, NodeDTO> nodeByNodeIdMap = workflow != null ? workflow.getNodeByNodeIdMap() : new HashMap<>();
        assert workflow != null;
        return workflow.getLinks().stream()
            .filter(link -> Objects.equals(link.getSinkNodeId(), node.getId()))
            .map(link -> nodeByNodeIdMap.get(link.getSourceNodeId()))
            .flatMap(upstreamNode ->
                Stream.of(findUpstreamNodes(upstreamNode, workflow), Stream.of(upstreamNode))
                    .flatMap(Function.identity())
            );
    }

    private String getNodeConfigWithRefData(NodeDTO node, WorkflowDTO workflow, Map<Long, List<String>> s3ObjectModifiedDateNodeMap) {
        Map<Long, List<Long>> sourceNodesByNodeIdMap = workflow.getSourceNodesByNodeIdMap();
        List<String> externalDataModifiedDates = s3ObjectModifiedDateNodeMap.getOrDefault(node.getId(), Collections.emptyList());
        String s3ObjectModifiedDate = externalDataModifiedDates.stream()
            .filter(Objects::nonNull)
            .collect(Collectors.joining(", "));
        String refDataVersionId = Optional.ofNullable(workflow.getConfig().getRefDataVersionId()).orElse("");
        return node.getResource()
            + node.getProperties().getValue()
            + (sourceNodesByNodeIdMap.get(node.getId()) != null ? sourceNodesByNodeIdMap.get(node.getId()) : "")
            + refDataVersionId
            + s3ObjectModifiedDate;
    }

    private boolean haveExternalDataFolderUpstreamNodes(WorkflowDTO workflow, NodeDTO targetNode, Map<Long, List<String>> s3ObjectModifiedDateNodeMap) {
        return findUpstreamNodes(targetNode, workflow)
            .anyMatch(node -> s3ObjectModifiedDateNodeMap.containsKey(node.getId()) && s3ObjectModifiedDateNodeMap.get(node.getId()).contains(null));
    }

    private boolean isOverrideCheckpointNotResumable(NodeDTO node) {
        JsonElement overrideCheckpointNotResumable = new Gson().fromJson(node.getProperties().getValue(), JsonObject.class)
            .get("overrideCheckpointNotResumable");
        return overrideCheckpointNotResumable != null && overrideCheckpointNotResumable.getAsBoolean();
    }
}
