package com.pharmdash.workflow.meta.service.impl;

import com.pharmdash.workflow.WorkflowGlobalProps;
import com.pharmdash.workflow.meta.client.util.S3FileManager;
import com.pharmdash.workflow.meta.service.WorkflowSampleDataService;
import com.prospection.arch2.util.ConfigurationFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.net.URI;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@ConditionalOnProperty(
    value = "jobs.local",
    havingValue = "false"
)
@RequiredArgsConstructor
public class WorkflowSampleDataS3ServiceImpl implements WorkflowSampleDataService {
    private final String commonBucket = URI.create(ConfigurationFactory.getInstance().getString(WorkflowGlobalProps.PropWarehouseBasePath())).getHost();
    private final S3FileManager s3FileManager;

    @Override
    public String readSampleData(Long workflowId, Long nodeId) {
        return getSampleDataAsString(commonBucket, getSampleDataFilePath(workflowId, nodeId));
    }

    @Override
    public void writeSampleData(Long workflowId, Long nodeId, String sampleData) {
        String filePath = getSampleDataFilePath(workflowId, nodeId);
        s3FileManager.writeContent(commonBucket, filePath, sampleData);
    }

    private String getSampleDataAsString(String s3BucketName, String sampleDataFilePath) {
        List<String> keys = s3FileManager.listObjectKeys(s3BucketName, sampleDataFilePath)
            .stream()
            .filter((String key) -> key.endsWith(".json"))
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(keys)) {
            return "";
        }

        return Arrays.stream(s3FileManager.readFileContents(s3BucketName, keys.get(0)).split("\\n"))
            .collect(Collectors.joining(",", "[", "]"));
    }

    private String getSampleDataFilePath(Long workflowId, Long nodeId) {
        return String.format(SAMPLE_DATA_FILE_PATH_PATTERN, workflowId, nodeId);
    }
}
