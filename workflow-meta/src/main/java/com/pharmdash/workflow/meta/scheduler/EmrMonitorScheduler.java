package com.pharmdash.workflow.meta.scheduler;

import com.amazonaws.services.elasticmapreduce.AmazonElasticMapReduce;
import com.amazonaws.services.elasticmapreduce.model.AddTagsRequest;
import com.amazonaws.services.elasticmapreduce.model.ClusterState;
import com.amazonaws.services.elasticmapreduce.model.ClusterSummary;
import com.amazonaws.services.elasticmapreduce.model.DescribeClusterRequest;
import com.amazonaws.services.elasticmapreduce.model.DescribeClusterResult;
import com.amazonaws.services.elasticmapreduce.model.InstanceFleet;
import com.amazonaws.services.elasticmapreduce.model.ListClustersRequest;
import com.amazonaws.services.elasticmapreduce.model.ListClustersResult;
import com.amazonaws.services.elasticmapreduce.model.ListInstanceFleetsRequest;
import com.amazonaws.services.elasticmapreduce.model.ListInstanceFleetsResult;
import com.amazonaws.services.elasticmapreduce.model.Tag;
import com.amazonaws.services.elasticmapreduce.model.TerminateJobFlowsRequest;
import com.pharmdash.workflow.meta.config.ApplicationConfiguration;
import com.pharmdash.workflow.meta.service.awspricing.PricingService;
import com.pharmdash.workflow.meta.service.featuretoggles.service.FeatureTogglesService;
import com.pharmdash.workflow.meta.service.featuretoggles.service.FeaturesToggles;
import com.pharmdash.workflow.meta.service.notification.NotificationException;
import com.pharmdash.workflow.meta.service.notification.NotificationService;
import com.prospection.arch2.util.ConfigurationFactory;
import com.typesafe.config.Config;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.pharmdash.workflow.meta.client.WorkflowTags.COMPANY_ID;
import static com.pharmdash.workflow.meta.client.WorkflowTags.DATASET_NAME;
import static com.pharmdash.workflow.meta.client.WorkflowTags.RUN_BY;
import static com.pharmdash.workflow.meta.client.WorkflowTags.TOPIC_ID;
import static com.pharmdash.workflow.meta.client.WorkflowTags.WORKFLOW_ID;
import static com.pharmdash.workflow.meta.jms.WorkflowEventJmsListener.EMR_TAG_TERMINATION_MESSAGE;
import static java.lang.String.format;
import static java.util.stream.Collectors.toList;

@Slf4j
@Component
public class EmrMonitorScheduler {

    private final Config appConfig = ConfigurationFactory.getInstance();

    private static final int THRESHOLD_IN_HOURS = 24;

    private static final long TIME_THRESHOLD_IN_MILLIS = TimeUnit.HOURS.toMillis(THRESHOLD_IN_HOURS);

    private final AmazonElasticMapReduce emr;

    private final PricingService pricingService;

    private final NotificationService notificationService;

    private final FeatureTogglesService featureTogglesService;

    private final double costThreshold;

    public EmrMonitorScheduler(ApplicationConfiguration applicationConfiguration,
                               AmazonElasticMapReduce emr,
                               PricingService pricingService,
                               NotificationService notificationService,
                               FeatureTogglesService featureTogglesService) {
        this.emr = emr;
        this.pricingService = pricingService;
        this.notificationService = notificationService;
        this.featureTogglesService = featureTogglesService;
        this.costThreshold = applicationConfiguration.getAmazon().getEmr().getCostThreshold();
    }

    // Runs every 30 minutes
    @Scheduled(fixedRate = 30 * 60 * 1000)
    public void monitorAndTerminateLongRunningEmrCluster() {
        log.info("Running a scheduler to monitor and terminate long running EMR clusters");
        try {

            List<ClusterDetail> allClusters = getAllActiveClusters();

            List<ClusterSummary> clustersToTerminate = findAndTagClustersToTerminate(allClusters);

            if (!clustersToTerminate.isEmpty()) {
                log.warn("Terminating {} clusters as they are running longer than threshold of " +
                    "{} hours or incurred cost more than {} USD", clustersToTerminate, THRESHOLD_IN_HOURS, costThreshold);
                emr.terminateJobFlows(new TerminateJobFlowsRequest()
                    .withJobFlowIds(clustersToTerminate.stream().map(ClusterSummary::getId).collect(toList()))
                );
            }
            allClusters.forEach(cluster -> {
                String runBy = getClusterTagValue(cluster.describeClusterResult, RUN_BY).orElse("");
                String companyId = getClusterTagValue(cluster.describeClusterResult, COMPANY_ID).orElse("");
                boolean notificationFeatureEnabled = featureTogglesService.isEnable(runBy, companyId, FeaturesToggles.ENABLE_WORKFLOW_COST_NOTIFICATION.getName());
                if (notificationFeatureEnabled) {
                    checkAndNotifyWorkflowCostAboveThreshold(cluster);
                } else {
                    log.info("Workflow cost notification feature is disabled for user {} and company {}", runBy, companyId);
                }
            });

        } catch (Exception e) {
            log.error("EMR monitor scheduler job failed", e);
        }
    }

    private List<ClusterSummary> findAndTagClustersToTerminate(List<ClusterDetail> allClusters) {
        return allClusters.stream()
            .filter(cluster -> isClusterLongRunning(cluster.clusterSummary) || hasClusterIncurredCostMoreThanThreshold(cluster.clusterSummary, cluster.estimatedCost))
            .map(cluster -> cluster.clusterSummary)
            .collect(toList());
    }

    private boolean hasClusterIncurredCostMoreThanThreshold(ClusterSummary cluster, Double clusterCost) {
        boolean incurredMoreThanCostThreshold = clusterCost != null && clusterCost > costThreshold;

        if (incurredMoreThanCostThreshold) {
            log.warn("Cluster {} has incurred cost of {} USD which is more than cost threshold of {} so it will be terminated",
                cluster.getId(), clusterCost, costThreshold);
            addErrorMessageAsTagInCluster(cluster, format("Exceeded cost threshold of %.2f USD", costThreshold));
        }
        return incurredMoreThanCostThreshold;
    }

    private boolean isClusterLongRunning(ClusterSummary cluster) {
        long currentTimeMillis = Instant.now().toEpochMilli();
        long clusterStartedTimeInMillis = cluster.getStatus()
            .getTimeline()
            .getCreationDateTime()
            .getTime();
        long clusterRunningTimeInMillis = calculateRunningTime(currentTimeMillis, clusterStartedTimeInMillis);
        boolean isRunningLongerThanThreshold = clusterRunningTimeInMillis > TIME_THRESHOLD_IN_MILLIS;

        if (isRunningLongerThanThreshold) {
            long hours = TimeUnit.MILLISECONDS.toHours(clusterRunningTimeInMillis);
            long minutes = TimeUnit.MILLISECONDS.toMinutes(clusterRunningTimeInMillis) - TimeUnit.HOURS.toMinutes(hours);
            log.warn("Cluster {} has been running for {} hours and {} minutes which is more than " +
                "time threshold of {} hours so it will be terminated", cluster.getId(), hours, minutes, THRESHOLD_IN_HOURS);
            addErrorMessageAsTagInCluster(cluster, format("Exceeded runtime threshold of %d hours", THRESHOLD_IN_HOURS));
        }

        return isRunningLongerThanThreshold;
    }

    private void addErrorMessageAsTagInCluster(ClusterSummary cluster, String message) {
        emr.addTags(new AddTagsRequest()
            .withResourceId(cluster.getId())
            .withTags(
                new Tag()
                    .withKey(EMR_TAG_TERMINATION_MESSAGE)
                    .withValue(format("EMR cluster %s terminated by EMR Monitor Scheduler. Reason: %s", cluster.getId(), message))
            )
        );
    }

    private long calculateRunningTime(long currentTimeMillis, long readyTimeMillis) {
        return currentTimeMillis - readyTimeMillis;
    }

    private List<ClusterDetail> getAllActiveClusters() {
        List<ClusterSummary> allClusters = new ArrayList<>();

        ListClustersResult clustersResult = null;

        do {
            clustersResult = emr.listClusters(new ListClustersRequest()
                .withClusterStates(
                    ClusterState.RUNNING,
                    ClusterState.WAITING
                )
                .withMarker(clustersResult == null ? null : clustersResult.getMarker())
            );
            allClusters.addAll(clustersResult.getClusters());
        } while (clustersResult.getMarker() != null);

        long currentTime = System.currentTimeMillis();
        return allClusters.stream()
            .map(cluster -> {
                DescribeClusterResult clusterDetails = emr.describeCluster(new DescribeClusterRequest().withClusterId(cluster.getId()));
                int numberOfInstances = getTargetCapacity(cluster.getId());
                Optional<Double> clusterCost = pricingService.calculateClusterEstimatedCost(cluster.getId());
                long runningTimeMillis = calculateRunningTime(currentTime, cluster.getStatus().getTimeline().getCreationDateTime().getTime());
                return new ClusterDetail(cluster, clusterDetails, clusterCost.orElse(null), numberOfInstances, runningTimeMillis);
            })
            .filter(clusterDetail ->
                clusterDetail.describeClusterResult.getCluster().getTags().stream().anyMatch(tag ->
                    tag.getKey().equals("prospection:context:Env")
                        && tag.getValue().equals(appConfig.getString("env.name"))
                )
            )
            .collect(toList());
    }

    private int getTargetCapacity(String clusterId) {
        List<InstanceFleet> instanceFleets = new ArrayList<>();
        ListInstanceFleetsResult listInstanceFleetsResult = null;

        do {
            listInstanceFleetsResult = emr.listInstanceFleets(new ListInstanceFleetsRequest().withClusterId(clusterId)).withMarker(listInstanceFleetsResult == null ? null : listInstanceFleetsResult.getMarker());
            instanceFleets.addAll(listInstanceFleetsResult.getInstanceFleets());
        } while (listInstanceFleetsResult.getMarker() != null);

        int ondemand = instanceFleets.stream().mapToInt(InstanceFleet::getTargetOnDemandCapacity).sum();
        int spot = instanceFleets.stream().mapToInt(InstanceFleet::getTargetSpotCapacity).sum();
        return ondemand + spot;
    }

    private void checkAndNotifyWorkflowCostAboveThreshold(ClusterDetail clusterDetail) {
        // check if cost of the workflow is above notification threshold
        int costThreshold = clusterDetail.numberOfInstances; // threshold is just number of instances, but presented in USD
        Double estimatedCost = clusterDetail.estimatedCost;
        if (estimatedCost != null && estimatedCost > costThreshold) {
            log.info("Workflow cost is above notification threshold: Workflow={}, Topic={}, Dataset=<{}>, IncurredCost={} USD",
                getClusterTagValue(clusterDetail.describeClusterResult, WORKFLOW_ID).orElse(""),
                getClusterTagValue(clusterDetail.describeClusterResult, TOPIC_ID).orElse(""),
                getClusterTagValue(clusterDetail.describeClusterResult, DATASET_NAME).orElse(""),
                estimatedCost
            );
            try {
                notificationService.sendWorkflowCostNotification(
                    Long.valueOf(getClusterTagValue(clusterDetail.describeClusterResult, TOPIC_ID).orElseThrow(() -> new NotificationException("TopicId tag not found"))),
                    Long.valueOf(getClusterTagValue(clusterDetail.describeClusterResult, WORKFLOW_ID).orElseThrow(() -> new NotificationException("WorkflowId tag not found"))),
                    getClusterTagValue(clusterDetail.describeClusterResult, RUN_BY).orElseThrow(() -> new NotificationException("Run by tag not found")),
                    estimatedCost, clusterDetail.runningTimeMilliseconds
                );
            } catch (NotificationException e) {
                log.error("Error sending workflow cost notification", e);
            }
        }
    }

    private Optional<String> getClusterTagValue(DescribeClusterResult clusterDetails, String key) {
        return clusterDetails.getCluster().getTags().stream()
            .filter(tag -> tag.getKey().equals(key))
            .map(Tag::getValue)
            .findFirst();
    }

    private record ClusterDetail(ClusterSummary clusterSummary, DescribeClusterResult describeClusterResult,
                                 Double estimatedCost, int numberOfInstances, long runningTimeMilliseconds) {

    }
}
