package com.pharmdash.workflow.meta.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PublishedPartitionDto {

    private LocalDate publishedDate;

    @Builder.Default
    private List<LocalDate> generatedDates = new ArrayList<>();

}
