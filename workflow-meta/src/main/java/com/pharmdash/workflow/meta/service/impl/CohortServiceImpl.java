package com.pharmdash.workflow.meta.service.impl;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3URI;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.pharmdash.workflow.WorkflowGlobalProps;
import com.pharmdash.workflow.meta.service.CohortService;
import com.prospection.arch2.util.ConfigurationFactory;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.URI;

import static java.lang.String.format;

@Slf4j
@Service
@AllArgsConstructor
public class CohortServiceImpl implements CohortService {

    private final String commonBucket = URI.create(ConfigurationFactory.getInstance().getString(WorkflowGlobalProps.PropWarehouseBasePath())).getHost();
    private static final String COHORT_PATH = "s3://%s/cohorts/standard/subject_id_list/%s/subjectIds_output.csv";
    private final AmazonS3 s3;

    public void publish(InputStream inputStream, int contentLength, String cohortId) {
        String fullPath = format(COHORT_PATH, commonBucket, cohortId);
        AmazonS3URI uri = new AmazonS3URI(fullPath);
        try {
            log.info("Writing subjectIds for cohort: {}", cohortId);
            log.info("Content length: {}", contentLength);
            ObjectMetadata meta = new ObjectMetadata();
            meta.setContentLength(contentLength);
            s3.putObject(uri.getBucket(), uri.getKey(), inputStream, meta);
        } catch (Exception e) {
            log.error("Failed to write subjectIds to s3: {} - {}", cohortId, fullPath, e);
            throw new RuntimeException(e);
        }
    }
}
