package com.pharmdash.workflow.meta.client.util;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3URI;
import com.amazonaws.services.s3.model.*;
import com.pharmdash.workflow.WorkflowGlobalProps;
import com.prospection.arch2.util.ConfigurationFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static java.lang.String.format;

@Component
@Slf4j
public class S3FileManager {
    private static String DELIMITER = "/";
    private final AmazonS3 s3client;

    @Autowired
    public S3FileManager(AmazonS3 s3client) {
        this.s3client = s3client;
    }

    /**
     * @return collection of child folder under {@code s3Path} of bucket {@code s3BucketName}
     */
    public List<String> listFolders(String s3BucketName, String s3Path) {

        ListObjectsV2Request req = new ListObjectsV2Request()
            .withBucketName(s3BucketName)
            .withDelimiter(DELIMITER)
            .withPrefix(s3Path + DELIMITER);

        ListObjectsV2Result result;
        result = s3client.listObjectsV2(req);
        List<String> commonPrefixes = result.getCommonPrefixes();
        return commonPrefixes.stream()
            .map(prefix -> prefix.substring(0, prefix.lastIndexOf(DELIMITER)))
            .map(prefix -> prefix.substring(prefix.lastIndexOf(DELIMITER) + 1))
            .collect(Collectors.toList());
    }

    public void deleteDryRunResult(Long workflowId) {
        String base = ConfigurationFactory.getInstance().getString(WorkflowGlobalProps.PropWarehouseCheckpointExportPath());
        String fullPath = format("%s%d", base, workflowId);
        log.info("Deleting dryrun output in {}", fullPath);
        AmazonS3URI uri = new AmazonS3URI(fullPath, false);
        ObjectListing objectListing = s3client.listObjects(uri.getBucket(), uri.getKey());
        List<DeleteObjectsRequest.KeyVersion> keysToDelete = new ArrayList<>();
        objectListing.getObjectSummaries().forEach(s3ObjectSummary -> {
                log.info("Deleting key: {}", s3ObjectSummary.getKey());
                keysToDelete.add(new DeleteObjectsRequest.KeyVersion(s3ObjectSummary.getKey()));
            }
        );
        if (keysToDelete.isEmpty()) {
            // nothing to delete here, return otherwise aws server will return MalformedXML error
            return;
        }
        DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(uri.getBucket());
        deleteObjectsRequest.setKeys(keysToDelete);
        s3client.deleteObjects(deleteObjectsRequest);
    }

    /**
     * Lists all objects in the bucket under the s3Path
     *
     * @param s3BucketName bucket to list
     * @param s3Path path in the bucket to list objects in
     * @return list of keys
     */
    public List<String> listObjectKeys(String s3BucketName, String s3Path) {
        ListObjectsV2Request req = new ListObjectsV2Request()
            .withBucketName(s3BucketName)
            .withDelimiter(DELIMITER)
            .withPrefix(s3Path + DELIMITER);

        ListObjectsV2Result result = s3client.listObjectsV2(req);
        return result.getObjectSummaries().stream()
            .map(S3ObjectSummary::getKey)
            .collect(Collectors.toList());
    }

    public String readFileContents(String s3BucketName, String fileKey) {
        return s3client.getObjectAsString(s3BucketName, fileKey);
    }

    public S3Object getObject(String s3BucketName, String fileKey) {
        return s3client.getObject(s3BucketName, fileKey);
    }

    public void writeContent(String s3BucketName, String fileKey, String content) {
        PutObjectRequest req = new PutObjectRequest(s3BucketName, fileKey, content);
        s3client.putObject(req);
    }

    public boolean exists(String bucket, String fileKey) {
        return s3client.doesObjectExist(bucket, fileKey);
    }

    public ObjectMetadata getObjectMetadata(String bucket, String fileKey) {
        return s3client.getObjectMetadata(bucket, fileKey);
    }

    public void copyObject(CopyObjectRequest copyObjectRequest) {
        s3client.copyObject(copyObjectRequest);
    }
}
