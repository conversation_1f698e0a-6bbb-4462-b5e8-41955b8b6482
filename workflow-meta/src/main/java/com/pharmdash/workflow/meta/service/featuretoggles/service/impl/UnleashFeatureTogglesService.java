package com.pharmdash.workflow.meta.service.featuretoggles.service.impl;

import com.pharmdash.workflow.meta.security.PharmdashUser;
import com.pharmdash.workflow.meta.security.SecurityUtil;
import com.pharmdash.workflow.meta.service.featuretoggles.service.FeatureTogglesService;
import io.getunleash.Unleash;
import io.getunleash.UnleashContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service("featureTogglesService")
@RequiredArgsConstructor
public class UnleashFeatureTogglesService implements FeatureTogglesService {

    private final Unleash unleash;

    @Override
    public boolean isEnable(PharmdashUser user, String featureName) {
        return isEnable(user.getUsername(), user.getCompanyId(), featureName);
    }

    @Override
    public boolean isEnable(String featureName) {
        return isEnable(SecurityUtil.getCurrentUser(), featureName);
    }

    @Override
    public boolean isEnable(String username, String companyId, String featureName) {
        UnleashContext unleashContext = UnleashContext.builder()
            .userId(username)
            .addProperty("companyId", companyId)
            .build();
        try {
            return unleash.isEnabled(featureName, unleashContext);
        } catch (Exception e) {
            log.error("Failed to fetch feature toggles. Defaulting to empty feature toggles.", e);
            return false;
        }
    }

}
