package com.pharmdash.workflow.meta.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pharmdash.workflow.dp.meta.processorannotations.SubProcessorCategory;
import com.pharmdash.workflow.dp.meta.processorannotations.ProcessorCategory;
import lombok.Builder;
import lombok.Value;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Nullable;
import java.util.List;

import static java.util.Collections.emptyList;

@Value
@Builder
public class ProcessorMetadata {
    private final String id;
    private final ProcessorCategory category;
    private final SubProcessorCategory subCategory;
    @Nullable
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private final String reportCode;
    private final boolean useDataset;
    private final Boolean deprecated;
    private final String defaultName;
    private final String description;
    private final List<Channel> inputChannels;
    private final List<Channel> outputChannels;
    private final Tags outputTags;
    private final Requires requires;
    private final boolean filterCohortByItemGroupNode;
    private final boolean requireItemGroupConfiguration;

    @Builder.Default
    private final DataProcessorSchema schema = DataProcessorSchema.builder().build();

    @Value
    public static class Channel {
        private final String name;
        private final String type;
        private final boolean required;
    }

    @Value
    public static class Requires {
        public static final Requires EMPTY = new Requires(new Tags(emptyList(), emptyList()));
        private final Tags tags;
    }

    @Value
    public static class Tags {
        private final List<String> eventTags;
        private final List<String> subjectTags;

        public boolean isEmpty() {
            return CollectionUtils.isEmpty(eventTags) && CollectionUtils.isEmpty(subjectTags);
        }
    }
}
