package com.pharmdash.workflow.meta.service.impl;

import com.pharmdash.workflow.WorkflowGlobalProps;
import com.pharmdash.workflow.dp.trigger.Trigger;
import com.pharmdash.workflow.meta.service.WorkflowTriggerService;
import com.prospection.arch2.util.ConfigurationFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Stream;

@Slf4j
@Service
@ConditionalOnProperty(
    value="jobs.local",
    havingValue = "true",
    matchIfMissing = true
)
@RequiredArgsConstructor
public class WorkflowTriggerLocalService implements WorkflowTriggerService {

    private static final String PATIENT_AGGREGATION_LEVEL_OUTPUT_SUFFIX = PATIENT_AGGREGATION_LEVEL + File.separator + "newResult=true";
    private static final String WORKFLOW_ROOT_PATH = ConfigurationFactory.getInstance().getString(WorkflowGlobalProps.PropWarehouseBasePath());
    private static final String TRIGGERS_OUTPUT_PATH = WORKFLOW_ROOT_PATH + "/%s/%d/%d/%s".replace("/", File.separator);

    @Override
    public StreamingResponseBody triggerOutput(Long workflowId, Long nodeId, String aggregateLevel) {
        return out -> getTriggerOutputFile(getTriggerOutputPath(workflowId, nodeId, aggregateLevel))
            .filter(Objects::nonNull)
            .forEach(line -> {
                try {
                    out.write(line.getBytes(StandardCharsets.UTF_8));
                    out.write('\n');
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });
    }

    @Override
    public boolean isTriggerOutputEmpty(Long workflowId, Long nodeId, String aggregateLevel) {
        return getTriggerOutputFile(getTriggerOutputPath(workflowId, nodeId, aggregateLevel))
            .filter(Objects::nonNull)
            .allMatch(String::isEmpty);
    }

    private Stream<String> getTriggerOutputFile(String triggersOutputPath) {
        Path path = Paths.get(triggersOutputPath);

        File triggersOutputDir = path.toFile();
        File[] files = triggersOutputDir.listFiles((dir, fileName) -> fileName.endsWith(".json"));
        if (files == null) {
            throw new RuntimeException("Could not find trigger output file");
        }

        return Arrays.stream(files).flatMap(file -> {
            try {
                return Files.readAllLines(file.toPath()).stream();
            } catch (IOException e) {
                throw new RuntimeException(String.format("Error when read file %s", file));
            }
        });
    }

    String getTriggerOutputPath(Long workflowId, Long nodeId, String aggregateLevel) {
        return String.format(TRIGGERS_OUTPUT_PATH, Trigger.OUTPUT_FOLDER(), workflowId, nodeId,
            PATIENT_AGGREGATION_LEVEL.equals(aggregateLevel) ? PATIENT_AGGREGATION_LEVEL_OUTPUT_SUFFIX : aggregateLevel);
    }
}
