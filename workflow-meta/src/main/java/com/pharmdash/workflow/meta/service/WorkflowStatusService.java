package com.pharmdash.workflow.meta.service;

import com.pharmdash.workflow.dp.javadto.WorkflowDTO;
import com.pharmdash.workflow.dp.javadto.WorkflowTriggerPayload;
import com.pharmdash.workflow.meta.client.jobs.JobDto;
import com.pharmdash.workflow.meta.client.jobs.JobStatusDto;
import com.pharmdash.workflow.meta.client.jobs.Status;
import com.pharmdash.workflow.meta.client.jobs.TaskDto;
import com.pharmdash.workflow.meta.persistence.GeneratingCheckpointEntity;
import com.pharmdash.workflow.meta.persistence.ResumableCheckpointEntity;
import com.pharmdash.workflow.meta.persistence.WorkflowStatusEntity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface WorkflowStatusService {

    void initWorkflowStatus(WorkflowDTO workflow, JobStatusDto dto, Optional<List<WorkflowTriggerPayload>> workflowTriggerPayloads);

    void updateWorkflowStatus(Long workflowId, JobStatusDto dto, String tag, LocalDateTime time);

    void updateWorkflowStatus(String jobId, JobStatusDto dto, String tag, LocalDateTime time);

    void updateNodeStatus(Long workflowId, Long nodeId, JobStatusDto dto, LocalDateTime time);

    void updateNodeStatus(Long workflowId, Long nodeId, JobStatusDto dto, String tag, LocalDateTime time);

    List<TaskDto> findTaskStatusesForNode(Long workflowId, Long nodeId);

    Optional<JobDto> workflowJobStatus(Long workflowId);

    List<JobDto> workflowJobStatuses();

    List<JobDto> workflowJobStatusesByWorkflowIds(List<Long> workflowIds);

    List<JobDto> workflowJobStatusesByWorkflowIdsAndStatus(List<Long> workflowIds, Status status);

    List<TaskDto> getWorkflowStatus(Long workflowId);

    List<ResumableCheckpointEntity> findAllResumableCheckpointsByNodeHashes(List<String> checkpointUpstreamNodeHashes);

    boolean existsBySucceededUpstreamNodesHash(String succeededUpstreamNodesHash);

    List<WorkflowTriggerPayload> getWorkflowTriggerPayloads(String jobOrClusterId);

    void updateWorkflowStatusSucceedIfAllNodeFinished(long workflowId, String tag, LocalDateTime timestamp);

    void updateSucceededResumableCheckpoints(String jobId);

    void deleteExecutingResumableCheckpoints(String jobId);

    void saveGeneratingCheckpoint(GeneratingCheckpointEntity generatingCheckpoint);

    boolean isReportGenerationApplicableForWorkflow(long workflowId);

    WorkflowStatusEntity findWorkflowByJobId(String clusterId);

    WorkflowStatusEntity findWorkflowById(long workflowId);
}
