package com.pharmdash.workflow.meta.client;

import com.pharmdash.workflow.WorkflowGlobalProps;
import com.pharmdash.workflow.meta.client.util.ListenerUtil;
import com.pharmdash.workflow.meta.client.util.LocalFlagUtil;
import com.pharmdash.workflow.meta.service.impl.WorkflowMetadataCreator;
import com.prospection.arch2.util.ConfigurationFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.client.ResponseListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.prospection.arch2.util.MetadataConstants.workflowPathFormat;
import static java.lang.String.format;

@Slf4j
@Component
@ConditionalOnProperty(
    value="jobs.local",
    havingValue = "true",
    matchIfMissing = true
)
public class WorkflowLocalStateService implements WorkflowCleanUpService {

    public static final long SCHEDULE_DELAY = 30000;

    private static final long START_AFTER = 6000;

    private final Path toCreateMetadataPath;
    private final Path creatingMetadataPath;
    private final Path publishedPath;
    private final Path toCleanUpPath;
    private final ElasticSearchClient elasticSearchClient;

    private final WorkflowMetadataCreator workflowMetadataCreator;

    public WorkflowLocalStateService(ElasticSearchClient client, WorkflowMetadataCreator workflowMetadataCreator) {
        String base = ConfigurationFactory.getInstance().getString(WorkflowGlobalProps.PropLocalStateBasePath());

        elasticSearchClient = client;
        this.workflowMetadataCreator = workflowMetadataCreator;

        toCreateMetadataPath = createAndGetPath(base, "to_create_metadata");
        creatingMetadataPath = createAndGetPath(base, "creating_metadata");
        publishedPath = createAndGetPath(base, "published");
        toCleanUpPath = createAndGetPath(base, "to_clean_up");
    }

    private Path createAndGetPath(String base, String pathName) {
        Path path = Paths.get(format(workflowPathFormat(File.separator), base), File.separator, pathName).toAbsolutePath();
        return createDir(path);
    }

    @Scheduled(initialDelay = START_AFTER, fixedDelay = SCHEDULE_DELAY)
    public void restoreAndCreateMetadata() {
        // In local, we never restore index but set a flag to create a metadata instead
        // See ReportParquetWriter.setPublishedFlagLocal
        createMetadata();
    }

    private void createMetadata() {
        List<File> files = Arrays.asList(Objects.requireNonNull(toCreateMetadataPath.toFile().listFiles()));
        if (CollectionUtils.isEmpty(files)) {
            log.debug("Couldn't create metadata. No file exists at " + toCreateMetadataPath.toString());
            return;
        }
        // one workflow at a time
        Optional<String> firstFileName = files.stream()
            .filter(file -> !file.getName().endsWith(".crc"))
            .map(File::getName)
            .findFirst();

        if (!firstFileName.isPresent()) {
            log.debug("Couldn't create metadata. File(s) exist but no valid workflow file exists to create metadata.");
            return;
        }

        String fileName = firstFileName.get();
        Pair<Long, Long> workflowIdAndNodeId = getWorkflowIdAndNodeId(fileName);

        LocalFlagUtil.changeFlag(toCreateMetadataPath, creatingMetadataPath, fileName);

        try {
            workflowMetadataCreator.createMetadata(workflowIdAndNodeId.getLeft(), workflowIdAndNodeId.getRight());

            LocalFlagUtil.changeFlag(creatingMetadataPath, publishedPath, fileName);
        } catch (Exception e) {
            LocalFlagUtil.changeFlag(creatingMetadataPath, toCreateMetadataPath, fileName);
            log.error("Creating metadata failed in local", e);
            throw e;
        }
    }

    @Override
    public void setToDelete(Long workflowId) {
        // snapshot of its corresponding draft has already been created, so just mark index for deletion
        LocalFlagUtil.setFlag(toCleanUpPath, workflowId.toString());

        Arrays.stream(Objects.requireNonNull(publishedPath.toFile().listFiles()))
            .map(file -> getWorkflowIdAndNodeId(file.getName()))
            .map(Pair::getLeft)
            .forEach(fileWorkflowId -> LocalFlagUtil.unsetFlag(publishedPath, fileWorkflowId.toString()));
    }

    @Scheduled(initialDelay = START_AFTER, fixedDelay = SCHEDULE_DELAY)
    public void cleanUp() {
        List<File> files = Arrays.asList(Objects.requireNonNull(toCleanUpPath.toFile().listFiles()));

        if (CollectionUtils.isEmpty(files)) {
            return;
        }
        for (File file : files) {
            Long workflowId = Long.parseLong(file.getName());
            ResponseListener listener = ListenerUtil
                .localResponseListener(workflowId.toString(), null, toCleanUpPath, toCleanUpPath, null);
            elasticSearchClient.deleteProdEsIndex(workflowId, listener);
        }
    }

    private Path createDir(Path path) {
        File dir = path.toFile();
        if (!dir.exists()) {
            dir.mkdirs();
            log.info("Created directory {}", path.toString());
        } else {
            log.info("Directory {} already exists", path.toString());
        }

        return path;
    }
}
