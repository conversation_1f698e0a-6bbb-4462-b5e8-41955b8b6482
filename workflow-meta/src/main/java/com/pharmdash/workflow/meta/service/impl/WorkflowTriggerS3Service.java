package com.pharmdash.workflow.meta.service.impl;

import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.pharmdash.workflow.WorkflowGlobalProps;
import com.pharmdash.workflow.dp.trigger.Trigger;
import com.pharmdash.workflow.meta.client.util.S3FileManager;
import com.pharmdash.workflow.meta.service.WorkflowTriggerService;
import com.prospection.arch2.util.ConfigurationFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URI;

@Slf4j
@Service
@ConditionalOnProperty(
    value="jobs.local",
    havingValue = "false"
)
@RequiredArgsConstructor
public class WorkflowTriggerS3Service implements WorkflowTriggerService {
    private static final String PATIENT_AGGREGATION_LEVEL_OUTPUT_SUFFIX = "patient/newResult=true/";

    private final S3FileManager s3FileManager;

    private final String commonBucket = URI.create(ConfigurationFactory.getInstance().getString(WorkflowGlobalProps.PropWarehouseBasePath())).getHost();

    @Override
    public StreamingResponseBody triggerOutput(Long workflowId, Long nodeId, String aggregateLevel) {
        String s3OutputPath = getS3OutputPath(workflowId, nodeId, aggregateLevel);
        return out -> s3FileManager.listObjectKeys(commonBucket, s3OutputPath)
            .stream()
            .filter(this::isJsonFile)
            .map(key -> s3FileManager.getObject(commonBucket, key))
            .forEach(file -> {
                try(S3ObjectInputStream is = file.getObjectContent()) {
                    StreamUtils.copy(is, out);
                    out.write('\n');
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });
    }

    @Override
    public boolean isTriggerOutputEmpty(Long workflowId, Long nodeId, String aggregateLevel) {
        String s3OutputPath = getS3OutputPath(workflowId, nodeId, aggregateLevel);
        return s3FileManager.listObjectKeys(commonBucket, s3OutputPath)
            .stream()
            .filter(this::isJsonFile)
            .map(key -> s3FileManager.getObject(commonBucket, key))
            .allMatch(file -> {
                try(S3ObjectInputStream is = file.getObjectContent();
                    BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {
                    return reader.lines()
                        .allMatch(String::isEmpty);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });
    }

    private String getS3OutputPath(Long workflowId, Long nodeId, String aggregateLevel) {
        boolean isPatientAggregationLevel = PATIENT_AGGREGATION_LEVEL.equals(aggregateLevel);
        String s3Path = String.format("%s/%d/%d/%s", Trigger.OUTPUT_FOLDER(), workflowId, nodeId,
            isPatientAggregationLevel ? PATIENT_AGGREGATION_LEVEL_OUTPUT_SUFFIX : aggregateLevel);
        // TODO: backward for demoing triggers, don't want to break it when we deploying. Remove later
        if (isPatientAggregationLevel && s3FileManager.listObjectKeys(commonBucket, s3Path).stream().noneMatch(this::isJsonFile)) {
            return String.format("%s/%d/%d/%s", Trigger.OUTPUT_FOLDER(), workflowId, nodeId, PATIENT_AGGREGATION_LEVEL);
        }
        return s3Path;
    }

    private boolean isJsonFile(String key) {
        return key.endsWith(".json");
    }

}
