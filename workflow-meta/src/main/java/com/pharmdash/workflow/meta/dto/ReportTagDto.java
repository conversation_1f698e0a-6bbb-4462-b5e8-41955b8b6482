package com.pharmdash.workflow.meta.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Singular;
import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class ReportTagDto implements Serializable {
    @Singular
    List<TagDto> subjectTags;
    @Singular
    List<TagDto> eventTags;

    @Data
    @Builder(toBuilder = true)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TagDto implements Serializable {
        String tagName;
        String tagType;
    }
}
