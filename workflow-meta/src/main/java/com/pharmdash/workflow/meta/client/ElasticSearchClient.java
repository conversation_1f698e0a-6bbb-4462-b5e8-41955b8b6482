package com.pharmdash.workflow.meta.client;

import org.elasticsearch.action.admin.indices.mapping.get.GetMappingsResponse;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.ResponseListener;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.io.IOException;
import java.util.List;

public interface ElasticSearchClient {

    void restoreEsIndex(String index) throws IOException;

    List<String> getOngoingRestorationsInEs() throws IOException;

    boolean isIndexRestoring(String index) throws IOException;

    boolean restoreCompleted(String index) throws IOException;

    boolean indiceInWorkflowDoesNotExist(Long workflowId) throws IOException;

    void deleteProdEsIndex(Long workflowId, ResponseListener listener);

    GetMappingsResponse getMappings(String indexName) throws IOException;

    GetResponse getDocument(String index, String docType, String docId) throws IOException;

    SearchResponse search(String indexName, SearchSourceBuilder search);

    void registerSnapshotRepository(String indexName) throws IOException;

    boolean repoExists(String indexName) throws IOException;

    void deleteSnapshotRepository(String indexName);

    boolean indexExists(String indexName) throws IOException;
}
