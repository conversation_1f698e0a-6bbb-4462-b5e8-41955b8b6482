package com.pharmdash.workflow.meta.dto;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@EqualsAndHashCode(callSuper = true)
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EMRClusterTagDTO extends MDCAbstractDTO{
    private String topicId;
    private String workflowId;
    private String runBy;

    @Getter(AccessLevel.NONE)
    @Setter
    private String terminationMessage;

    public Optional<String> getTerminationMessageOptional() {
        return Optional.ofNullable(terminationMessage);
    }

    @Override
    public Map<String, String> getDiagnosticContextMap() {
        Map<String, String> enrichmentMap = new HashMap<>();
        enrichmentMap.put("topicId", topicId);
        enrichmentMap.put("workflowId", workflowId);
        enrichmentMap.put("runBy", runBy);
        return enrichmentMap;
    }
}
