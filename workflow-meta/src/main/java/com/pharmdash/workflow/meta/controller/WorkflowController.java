package com.pharmdash.workflow.meta.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.pharmdash.workflow.dp.javadto.CheckpointInfoDTO;
import com.pharmdash.workflow.dp.javadto.GroovyTestDTO;
import com.pharmdash.workflow.dp.javadto.ViolationDTO;
import com.pharmdash.workflow.dp.javadto.WorkflowDTO;
import com.pharmdash.workflow.dp.validation.exceptions.WorkflowInitException;
import com.pharmdash.workflow.dp.validation.exceptions.WorkflowValidationException;
import com.pharmdash.workflow.meta.client.jobs.JobDto;
import com.pharmdash.workflow.meta.client.jobs.Status;
import com.pharmdash.workflow.meta.config.CacheConfiguration;
import com.pharmdash.workflow.meta.controller.errors.MetadataDoestNotExistException;
import com.pharmdash.workflow.meta.controller.errors.WorkflowCleanUpException;
import com.pharmdash.workflow.meta.controller.errors.WorkflowNotRunningException;
import com.pharmdash.workflow.meta.dto.*;
import com.pharmdash.workflow.meta.service.WorkflowService;
import com.pharmdash.workflow.meta.service.impl.GenericReportMetadata;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.validation.constraints.NotBlank;
import java.util.*;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("warehouse/v1")
public class WorkflowController {

    private final WorkflowService service;

    @PostMapping("/workflow/validate")
    public ValidationResultDTO validate(@RequestBody WorkflowDTO workflow) {
        try {
            return service.validate(workflow);
        } catch (WorkflowValidationException e) {
            return new ValidationResultDTO(Collections.singletonList(new ViolationDTO(ViolationDTO.FATAL, e.getNodeId(),
                e.getMessage(), true)));
        }
    }

    @GetMapping("/workflow/validate/debugWorkflowProperties")
    public Set<String> getDebugWorkflowProperties() {
        return service.getDebugWorkflowProperties();
    }

    @PostMapping("/workflow/test-groovy")
    public String testGroovy(@RequestBody GroovyTestDTO groovyTest) {
        return service.runTestGroovy(groovyTest);
    }

    @PostMapping("/workflow/execute")
    public void execute(@RequestBody WorkflowDTO workflow) {
        service.submitJob(workflow);
    }

    @PostMapping("/workflow/{workflowId}/jobs/cancel")
    public void cancel(@PathVariable Long workflowId) throws WorkflowNotRunningException {
        service.cancelJob(workflowId);
    }

    @PostMapping("/workflow/available-tags")
    public Map<Long, ReportTagDto> getAvailableTagsByReportId(@RequestBody WorkflowDTO workflow) {
        return service.getAvailableTagsByReportId(workflow);
    }

    @GetMapping("/workflow/{workflowId}/node/{nodeId}/sample-data")
    public String getSampleData(@PathVariable Long workflowId, @PathVariable Long nodeId) {
        return service.readSampleData(workflowId, nodeId);
    }

    @GetMapping("/workflow/{workflowId}/jobs")
    public ResponseEntity<JobDto> jobStatus(@PathVariable Long workflowId) {
        Optional<JobDto> jobDtoOptional = service.readJobStatusAndEstimatedCost(workflowId);
        return jobDtoOptional
            .map(jobDto -> ResponseEntity.ok().body(jobDto))
            .orElseGet(() -> ResponseEntity.notFound().build());
    }

    @GetMapping("/workflow/jobs")
    public List<JobDto> listJobs(@RequestParam(defaultValue = "") List<Long> workflowIds, @RequestParam Optional<Status> status) {
        if (workflowIds.isEmpty()) {
            throw new IllegalArgumentException("Workflow ids cannot be empty");
        }

        return status.map(jobStatus -> service.jobHistoryByWorkflowIdsAndStatus(workflowIds, jobStatus))
            .orElseGet(() -> service.jobHistoryByWorkflowIds(workflowIds));
    }

    @GetMapping("/workflow/{workflowId}/node/{nodeId}/metadata")
    public GenericReportMetadata metadata(@PathVariable @NotBlank Long workflowId, @PathVariable @NotBlank Long nodeId) throws MetadataDoestNotExistException {
        return service.metadata(workflowId, nodeId);
    }

    @GetMapping("/workflow/{workflowId}/node/{nodeId}/trigger-output")
    public StreamingResponseBody triggerOutput(@PathVariable @NotBlank Long workflowId, @PathVariable @NotBlank Long nodeId, @RequestParam String aggregateLevel) {
        return service.triggerOutput(workflowId, nodeId, aggregateLevel);
    }

    @GetMapping("/workflow/{workflowId}/node/{nodeId}/trigger-output-is-empty")
    public boolean isTriggerOutputEmpty(@PathVariable @NotBlank Long workflowId, @PathVariable @NotBlank Long nodeId, @RequestParam String aggregateLevel) {
        return service.isTriggerOutputEmpty(workflowId, nodeId, aggregateLevel);
    }

    /**
     * @deprecated we now should be using /dataset/partitions endpoint. This is to support older configurations.
     */
    @Deprecated
    @GetMapping("/workflow/dataset/versions")
    public ResponseEntity<List<String>> datasetVersions(@RequestParam String source) {
        List<String> versions = service.readDatasetVersions(source);
        return ResponseEntity.ok().body(versions);
    }

    @PostMapping("/workflow/upstream-node-hash/{nodeId}")
    public ResponseEntity<String> getCheckpointUpstreamNodeHash(@RequestBody WorkflowDTO workflow, @PathVariable @NotBlank Long nodeId) {
        // TODO: Temporarily use for checkpoint, might need to enhance this logic to tell if this checkpoint is resumable
        return ResponseEntity.ok().body(service.getCheckpointNodeMap(workflow).get(nodeId));

    }

    @PostMapping("/workflow/checkpoints/{checkpointNodeId}")
    public ResponseEntity<CheckpointInfoDTO> getCheckpointProcessorData(@RequestBody WorkflowDTO workflow, @PathVariable @NotBlank Long checkpointNodeId) {
        CheckpointInfoDTO checkpointInfoDTO = service.readCheckpointInfo(workflow, checkpointNodeId);
        return ResponseEntity.ok().body(checkpointInfoDTO);
    }

    @DeleteMapping("/workflow/prod/{workflowId}")
    public void cleanUp(@PathVariable Long workflowId) throws WorkflowCleanUpException {
        service.cleanUp(workflowId);
    }

    @GetMapping("/workflow/{workflowId}/node/{nodeId}/dataset-info")
    @Cacheable(cacheNames = CacheConfiguration.CACHE_DATASET_INFO)
    public DatasetInfoDto getDatasetInfo(@PathVariable Long workflowId, @PathVariable Long nodeId) {
        return service.getDatasetInfo(workflowId, nodeId);
    }

    @GetMapping("/workflow/{workflowId}/node/{nodeId}/configuration")
    public JsonNode getConfiguration(@PathVariable Long workflowId, @PathVariable Long nodeId) {
        return service.getConfiguration(workflowId, nodeId);
    }

    @PostMapping("/workflow/report-metrics")
    public List<ReportMetricResultDTO> getReportMetrics(@RequestBody WorkflowDTO workflow) {
        return service.getReportMetrics(workflow);
    }

    @GetMapping("/workflow/{workflowId}/node/{nodeId}/event-codes")
    public ReportMetricDetailDTO getReportEventCodes(@PathVariable Long workflowId, @PathVariable Long nodeId) {
        return service.getReportEventCodes(workflowId, nodeId);
    }

    @PostMapping("/workflow/metrics-details")
    public WorkflowMetricsDetailsListResponseDTO getWorkflowMetricsDetails(@RequestBody BasicWorkflowDetailsListDto metricsDetailsRequestDTO) {
        return WorkflowMetricsDetailsListResponseDTO
            .builder()
            .workflowMetricsDetailsList(service.getWorkflowMetricDetailsList(metricsDetailsRequestDTO))
            .build();
    }

    @DeleteMapping("/workflow/{workflowId}/dryrun")
    public void cleanUpDryRunResult(@PathVariable Long workflowId) {
        service.cleanUpDryRunResult(workflowId);
    }

    @ExceptionHandler
    public ResponseEntity<ValidationResultDTO> handleMetadataNotFoundException(MetadataDoestNotExistException ex) {
        log.error("Could not find metadata", ex);
        return ResponseEntity.notFound().build();
    }

    @ExceptionHandler
    public ResponseEntity<ValidationResultDTO> handleWorkflowJobNotFoundException(WorkflowNotRunningException ex) {
        log.error("Could not find workflow", ex);
        return ResponseEntity.notFound().build();
    }

    @ExceptionHandler
    public ResponseEntity<ValidationResultDTO> handleBadRequestAlertException(WorkflowInitException ex) {
        log.error("Bad request alert", ex);
        ViolationDTO violation = new ViolationDTO(ViolationDTO.FATAL, null, ex.getMessage(), true);
        return ResponseEntity
            .badRequest()
            .body(new ValidationResultDTO(Collections.singletonList(violation)));
    }

    @ExceptionHandler
    public ResponseEntity<ValidationResultDTO> handleWorkflowCleanUpException(WorkflowCleanUpException ex) {
        log.error("Could not clean up workflow", ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }

    @ExceptionHandler
    public ResponseEntity<Map<String, String>> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException ex) {
        log.warn("Invalid parameter", ex);
        return ResponseEntity.badRequest().body(Collections.singletonMap("message", "Invalid parameter"));
    }

    @ExceptionHandler
    public ResponseEntity<Map<String, String>> handleMethodIllegalArgumentException(IllegalArgumentException ex) {
        log.warn("Illegal parameter", ex);
        return ResponseEntity.badRequest().body(Collections.singletonMap("message", ex.getMessage()));
    }
}
