package com.pharmdash.workflow.meta.persistence;

import com.pharmdash.workflow.meta.client.jobs.JobStatusDto;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

import java.util.Optional;

import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.defaultIfBlank;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Setter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@ToString
@DynamoDbBean
public class NodeStatusEntity implements StatusEntity {

    private Long workflowId;

    private String jobId;

    private Long erroredOn;

    @Builder.Default
    private String messages = "";

    @Getter(AccessLevel.NONE)
    private Long nodeId;

    private String nodeType;

    private Long indexingSubmittedOn;

    private Long indexingStartedOn;

    private Long indexingCompletedOn;

    private Long publishingSubmittedOn;

    private Long publishingStartedOn;

    private Long publishingCompletedOn;

    private Long ingestionStartedOn;

    private Long ingestionCompletedOn;

    @Override
    @DynamoDbPartitionKey
    @DynamoDbAttribute("WorkflowId")
    public Long getWorkflowId() {
        return workflowId;
    }

    @DynamoDbSortKey
    @DynamoDbAttribute("NodeId")
    public Long getNodeId() {
        return nodeId;
    }

    @DynamoDbAttribute("NodeType")
    public String getNodeType() {
        return nodeType;
    }

    @DynamoDbAttribute("JobId")
    @Override
    public String getJobId() {
        return jobId;
    }

    @DynamoDbAttribute("ErroredOn")
    @Override
    public Long getErroredOn() {
        return erroredOn;
    }

    @Override
    @DynamoDbAttribute("Messages")
    public String getMessages() {
        return messages;
    }

    @DynamoDbAttribute("IndexingSubmittedOn")
    public Long getIndexingSubmittedOn() {
        return indexingSubmittedOn;
    }

    @DynamoDbAttribute("IndexingStartedOn")
    public Long getIndexingStartedOn() {
        return indexingStartedOn;
    }

    @DynamoDbAttribute("IndexingCompletedOn")
    public Long getIndexingCompletedOn() {
        return indexingCompletedOn;
    }

    @DynamoDbAttribute("PublishingSubmittedOn")
    public Long getPublishingSubmittedOn() {
        return publishingSubmittedOn;
    }

    @DynamoDbAttribute("PublishingStartedOn")
    public Long getPublishingStartedOn() {
        return publishingStartedOn;
    }

    @DynamoDbAttribute("PublishingCompletedOn")
    public Long getPublishingCompletedOn() {
        return publishingCompletedOn;
    }

    @DynamoDbAttribute("IngestionStartedOn")
    public Long getIngestionStartedOn() {
        return ingestionStartedOn;
    }

    @DynamoDbAttribute("IngestionCompletedOn")
    public Long getIngestionCompletedOn() {
        return ingestionCompletedOn;
    }

    public NodeStatusEntity copyIfEmptyAndAppendMessage(JobStatusDto dto) {
        return NodeStatusEntity.builder()
            .workflowId(ofNullable(workflowId).orElse(dto.getWorkflowId()))
            .jobId(defaultIfBlank(jobId, dto.getJobId()))
            .nodeType(defaultIfBlank(nodeType, Optional.ofNullable(dto.getNodeReportType()).map(Enum::name).orElse(null)))
            .erroredOn(ofNullable(erroredOn).orElse(dto.getErroredOn()))
            .messages(isBlank(dto.getMessages()) ? messages : messages + " \n " + dto.getMessages())
            .nodeId(ofNullable(nodeId).orElse(dto.getNodeId()))
            .indexingSubmittedOn(ofNullable(indexingSubmittedOn).orElse(dto.getIndexingSubmittedOn()))
            .indexingStartedOn(ofNullable(indexingStartedOn).orElse(dto.getIndexingStartedOn()))
            .indexingCompletedOn(ofNullable(indexingCompletedOn).orElse(dto.getIndexingCompletedOn()))
            .publishingSubmittedOn(ofNullable(publishingSubmittedOn).orElse(dto.getPublishingSubmittedOn()))
            .publishingStartedOn(ofNullable(publishingStartedOn).orElse(dto.getPublishingStartedOn()))
            .publishingCompletedOn(ofNullable(publishingCompletedOn).orElse(dto.getPublishingCompletedOn()))
            .ingestionStartedOn(ofNullable(ingestionStartedOn).orElse(dto.getIngestionStartedOn()))
            .ingestionCompletedOn(ofNullable(ingestionCompletedOn).orElse(dto.getIngestionCompletedOn()))
            .build();
    }
}
