package com.pharmdash.workflow.meta.config;

import com.pharmdash.workflow.common.WorkflowKryo;
import com.pharmdash.workflow.meta.client.SparkWrapper;
import com.prospection.arch2.config.SparkConfiguration;
import org.apache.spark.SparkConf;
import org.apache.spark.sql.SparkSession;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.util.TimeZone;

@Configuration
public class SparkWrapperConfiguration {

    @Value("${jobs.local}")
    private boolean sparkLocal;

    @Value("${localstack.s3.endpoint:#{null}}")
    private String refDataS3Endpoint;

    @Value("${localstack.s3.accessKey:#{null}}")
    private String refDataS3AccessKey;

    @Value("${localstack.s3.secretKey:#{null}}")
    private String refDataS3SecretKey;

    @Value("${aws.s3.endpoint:#{null}}")
    private String endpoint;

    @Bean
    @Lazy
    public SparkWrapper sparkSession() {
        if (sparkLocal) {
            // Make sure workflow kryo classes are ready to go.
            TimeZone.setDefault(TimeZone.getTimeZone("UTC"));

            SparkConf sparkConf = SparkConfiguration.sparkLocalConfiguration(WorkflowKryo.classes())
                .set("spark.hadoop.fs.s3a.bucket.pd-au-local-ref-data-v2.access.key", refDataS3AccessKey)
                .set("spark.hadoop.fs.s3a.bucket.pd-au-local-ref-data-v2.secret.key", refDataS3SecretKey)
                .set("spark.hadoop.fs.s3a.bucket.pd-au-local-ref-data-v2.endpoint", refDataS3Endpoint)
                .set("spark.hadoop.fs.s3a.endpoint", endpoint)
                .set("spark.hadoop.fs.s3a.path.style.access", "true")
                .set("spark.sql.parquet.datetimeRebaseModeInWrite", "CORRECTED")
                .set("spark.sql.parquet.datetimeRebaseModeInRead", "CORRECTED")
                .set("spark.sql.parquet.int96RebaseModeInWrite", "CORRECTED")
                .set("spark.sql.parquet.int96RebaseModeInRead", "CORRECTED");

            SparkSession.builder()
                .appName("Warehouse API")
                .config(sparkConf)
                .getOrCreate();

            return SparkWrapper.builder()
                .isLocal(true)
                .build();
        }

        return SparkWrapper.builder().isLocal(false).build();
    }

}
