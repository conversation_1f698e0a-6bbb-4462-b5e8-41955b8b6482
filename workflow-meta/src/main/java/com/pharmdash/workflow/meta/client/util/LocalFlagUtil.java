package com.pharmdash.workflow.meta.client.util;

import com.pharmdash.workflow.meta.client.exceptions.WorkflowBackupException;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Path;

@Slf4j
public class LocalFlagUtil {
    public static void changeFlag(Path from, Path to, String workflowId) {
        unsetFlag(from, workflowId);
        setFlag(to, workflowId);
    }

    public static void setFlag(Path absolutePath, String workflowId) {
        try {
            File dir = absolutePath.toFile();
            if (!dir.exists()) dir.mkdirs();
            String filePath = dir.getAbsolutePath() + File.separator + workflowId;
            if (!new File(filePath).exists()) {
                try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
                    writer.write(workflowId);
                }
            }
        } catch (IOException e) {
            String message = "Failed to set " + absolutePath + " flag for workflowId: " + workflowId + e.getMessage();
            log.error(message);
            throw new WorkflowBackupException(message);
        }
    }

    public static void unsetFlag(Path absolutePath, String workflowId) {
        File dir = absolutePath.toFile();
        if (!dir.exists()) dir.mkdirs();
        String filePath = dir.getAbsolutePath() + File.separator + workflowId;
        File toDelete = new File(filePath);
        if (toDelete.exists()) {
            boolean success = toDelete.delete();
            if (!success) {
                log.error("File could not be deleted " + workflowId);
                String message = "Failed to unset " + absolutePath + " flag for workflowId: " + workflowId;
                throw new WorkflowBackupException(message);
            }
        }
    }
}
