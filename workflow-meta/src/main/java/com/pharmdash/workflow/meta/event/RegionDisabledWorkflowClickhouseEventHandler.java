package com.pharmdash.workflow.meta.event;

import com.pharmdash.workflow.dp.meta.processorannotations.ReportType;
import com.pharmdash.workflow.meta.client.jobs.JobStatusDto;
import com.pharmdash.workflow.meta.service.WorkflowStatusService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

import static com.pharmdash.workflow.meta.client.util.TimeUtil.toEpochMilli;
import static com.pharmdash.workflow.meta.event.WorkflowStatusEventHandler.TAG_REPORT_CLICKHOUSE_INGESTOR;
import static java.lang.String.format;

@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnMissingBean(RegionEnabledWorkflowClickhouseEventHandler.class)
public class RegionDisabledWorkflowClickhouseEventHandler implements WorkflowClickhouseEventHandler {

    private final WorkflowStatusService workflowStatusService;

    @Override
    public void handleWorkflowClickhouseIngestionEvent(WorkflowClickhouseEvent event) {
        Long workflowId = event.getWorkflowId();
        Long nodeId = event.getNodeId();

        if (workflowId == null) {
            log.error("Event is missing workflowId, skipping ingestion event {}", event);
            // we don't want it to be retried and block the FIFO queue
            return;
        }

        if (nodeId == null) {
            log.error("Event is missing nodeId, skipping ingestion event {}", event);
            // we don't want it to be retried and block the FIFO queue
            return;
        }

        LocalDateTime erroredOn = LocalDateTime.now();
        String message = format("Clickhouse client is not available in this region, skipping ingestion for [workflowId: %s, nodeId: %s]", workflowId, nodeId);

        workflowStatusService.updateNodeStatus(
            workflowId,
            nodeId,
            JobStatusDto.builder()
                .nodeReportType(ReportType.CLICKHOUSE)
                .ingestionStartedOn(toEpochMilli(erroredOn))
                .erroredOn(toEpochMilli(erroredOn))
                .messages(message)
                .build(),
            TAG_REPORT_CLICKHOUSE_INGESTOR,
            erroredOn
        );
        log.error(message);
    }

    @Override
    public void handleWorkflowClickhouseCleanUpEvent(WorkflowClickhouseEvent event) {
        Long workflowId = event.getWorkflowId();
        if (workflowId == null) {
            log.error("Event is missing workflowId, skipping clean up event {}", event);
            // we don't want it to be retried and block the FIFO queue
            return;
        }

        log.warn("Clickhouse client is not available in this region, skipping clean up for workflowId: {}", workflowId);
        // We don't want to throw here because the event will be put back in to the queue and retried
    }
}
