package com.pharmdash.workflow.meta.service.impl;

import com.amazonaws.services.s3.model.S3Object;
import com.pharmdash.workflow.meta.client.util.S3FileManager;
import com.pharmdash.workflow.meta.utils.StreamingUtils;
import org.junit.Before;
import org.junit.Test;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class WorkflowTriggerS3ServiceTest {

    private WorkflowTriggerS3Service workflowTriggerS3Service;
    private final S3FileManager s3FileManager = mock(S3FileManager.class);

    @Before
    public void setup() {
        workflowTriggerS3Service = new WorkflowTriggerS3Service(s3FileManager);
    }

    @Test
    public void shouldReadTriggerFile() throws IOException {
        String fileContent = "{\"subjectId\":\"1\",\"subjectTags\":{\"gender\":[\"Male\"]},\"event\":\"A\",\"date\":\"2018-04-11\",\"eventTags\":{\"stateAtEvent\":[\"VIC\"]}}\n" +
            "{\"subjectId\":\"2\",\"subjectTags\":{\"gender\":[\"Female\"]},\"event\":\"A\",\"date\":\"2016-02-12\",\"eventTags\":{\"stateAtEvent\":[\"QLD\"]}}";
        String fileContent2 = "{\"subjectId\":\"1\",\"subjectTags\":{\"gender\":[\"Male\"]},\"event\":\"A\",\"date\":\"2018-04-11\",\"eventTags\":{\"stateAtEvent\":[\"VIC\"]}}\n" +
            "{\"subjectId\":\"2\",\"subjectTags\":{\"gender\":[\"Female\"]},\"event\":\"A\",\"date\":\"2016-02-12\",\"eventTags\":{\"stateAtEvent\":[\"QLD\"]}}";
        try (S3Object object1 = new S3Object(); S3Object object2 = new S3Object()) {
            object1.setObjectContent(new ByteArrayInputStream(fileContent.getBytes()));
            object2.setObjectContent(new ByteArrayInputStream(fileContent2.getBytes()));
            when(s3FileManager.listObjectKeys(isNull(), anyString())).thenReturn(Arrays.asList("some_unrelated_file", "trigger.json", "trigger2.json"));
            when(s3FileManager.getObject(isNull(), eq("trigger.json"))).thenReturn(object1);
            when(s3FileManager.getObject(isNull(), eq("trigger2.json"))).thenReturn(object2);
            StreamingResponseBody result = workflowTriggerS3Service.triggerOutput(1L, 1L, "patient");
            assertThat(StreamingUtils.responseToString(result)).isEqualTo(fileContent + "\n" + fileContent2 + "\n");
        }
    }

    @Test
    public void shouldReadEmptyTriggerOutputFileAndReturnEmptyList() throws RuntimeException, IOException {
        String fileContent = "";
        when(s3FileManager.listObjectKeys(isNull(), anyString())).thenReturn(Arrays.asList("some_unrelated_file","trigger.json"));
        when(s3FileManager.readFileContents(isNull(), eq("trigger.json"))).thenReturn(fileContent);

        try (S3Object object1 = new S3Object()) {
            object1.setObjectContent(new ByteArrayInputStream(fileContent.getBytes()));
            when(s3FileManager.listObjectKeys(isNull(), anyString())).thenReturn(Arrays.asList("some_unrelated_file", "trigger.json"));
            when(s3FileManager.getObject(isNull(), eq("trigger.json"))).thenReturn(object1);
            StreamingResponseBody result = workflowTriggerS3Service.triggerOutput(1L, 1L, "patient");
            assertThat(StreamingUtils.responseToString(result)).isEqualTo("\n");
        }
    }

    @Test
    public void shouldReturnIsNotEmptyResultWhenThereAreRecord() throws IOException {
        String fileContent = "";
        String fileContent2 = "{\"subjectId\":\"1\",\"subjectTags\":{\"gender\":[\"Male\"]},\"event\":\"A\",\"date\":\"2018-04-11\",\"eventTags\":{\"stateAtEvent\":[\"VIC\"]}}\n" +
            "{\"subjectId\":\"2\",\"subjectTags\":{\"gender\":[\"Female\"]},\"event\":\"A\",\"date\":\"2016-02-12\",\"eventTags\":{\"stateAtEvent\":[\"QLD\"]}}";
        try (S3Object object1 = new S3Object(); S3Object object2 = new S3Object()) {
            object1.setObjectContent(new ByteArrayInputStream(fileContent.getBytes()));
            object2.setObjectContent(new ByteArrayInputStream(fileContent2.getBytes()));
            when(s3FileManager.listObjectKeys(isNull(), anyString())).thenReturn(Arrays.asList("some_unrelated_file", "trigger.json", "trigger2.json"));
            when(s3FileManager.getObject(isNull(), eq("trigger.json"))).thenReturn(object1);
            when(s3FileManager.getObject(isNull(), eq("trigger2.json"))).thenReturn(object2);
            assert !workflowTriggerS3Service.isTriggerOutputEmpty(1L, 1L, "patient");
        }
    }

    @Test
    public void shouldReturnIsEmptyResultWhenThereAreNoRecord() throws IOException {
        String fileContent = "";
        String fileContent2 = "";
        try (S3Object object1 = new S3Object(); S3Object object2 = new S3Object()) {
            object1.setObjectContent(new ByteArrayInputStream(fileContent.getBytes()));
            object2.setObjectContent(new ByteArrayInputStream(fileContent2.getBytes()));
            when(s3FileManager.listObjectKeys(isNull(), anyString())).thenReturn(Arrays.asList("some_unrelated_file", "trigger.json", "trigger2.json"));
            when(s3FileManager.getObject(isNull(), eq("trigger.json"))).thenReturn(object1);
            when(s3FileManager.getObject(isNull(), eq("trigger2.json"))).thenReturn(object2);
            assert workflowTriggerS3Service.isTriggerOutputEmpty(1L, 1L, "patient");
        }
    }

    @Test
    public void shouldReturnIsEmptyResultWhenThereAreNoFile() {
        when(s3FileManager.listObjectKeys(isNull(), anyString())).thenReturn(Collections.emptyList());
        assert workflowTriggerS3Service.isTriggerOutputEmpty(1L, 1L, "patient");
    }

}
