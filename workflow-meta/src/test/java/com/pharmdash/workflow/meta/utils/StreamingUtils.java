package com.pharmdash.workflow.meta.utils;

import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

public class StreamingUtils {

    public static String responseToString(StreamingResponseBody streamingResponseBody) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            streamingResponseBody.writeTo(outputStream);
            return outputStream.toString();
        } catch (IOException e) {
            return null;
        }
    }

}
