package com.pharmdash.workflow.meta.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpHost;
import org.apache.http.ProtocolVersion;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicRequestLine;
import org.apache.http.message.BasicStatusLine;
import org.elasticsearch.action.admin.cluster.repositories.delete.DeleteRepositoryRequest;
import org.elasticsearch.action.admin.cluster.repositories.get.GetRepositoriesResponse;
import org.elasticsearch.action.admin.cluster.repositories.put.PutRepositoryRequest;
import org.elasticsearch.action.admin.indices.get.GetIndexRequest;
import org.elasticsearch.client.*;
import org.elasticsearch.cluster.metadata.RepositoryMetaData;
import org.elasticsearch.common.settings.Settings;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatcher;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;

import static com.pharmdash.workflow.meta.client.ElasticSearchClientImpl.MAX_RESTORE_BYTES_PER_SEC;
import static com.pharmdash.workflow.meta.client.ElasticSearchClientImpl.MAX_SNAPSHOT_BYTES_PER_SEC;
import static com.pharmdash.workflow.meta.client.ElasticSearchClientImpl.SNAPSHOT_OPERATION_TIMEOUT;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

public class ElasticSearchClientImplTest {

    private static final String ES_WORKFLOW_PREFIX = "ES_WORKFLOW_PREFIX";
    private static final String REPORTS_ES_SNAPSHOT_ROLE_ARN = "REPORTS_ES_SNAPSHOT_ROLE_ARN";
    private static final String ES_BUCKET = "ES_BUCKET";
    private static final String INDEX_NAME = "index_name";

    private RestClient prodElasticSearchClient = mock(RestClient.class);
    private RestHighLevelClient prodHighLevelClient = mock(RestHighLevelClient.class);
    private ElasticSearchClientImpl esClient;

    @Before
    public void setup() {
        esClient = new ElasticSearchClientImpl(prodElasticSearchClient, prodHighLevelClient, new ObjectMapper());
        ReflectionTestUtils.setField(esClient, "reportsEsSnapshotRoleArn", REPORTS_ES_SNAPSHOT_ROLE_ARN);
        ReflectionTestUtils.setField(esClient, "esBucket", ES_BUCKET);
    }

    @Test
    public void testRegisterSnapshotRepository() throws IOException{
        SnapshotClient snapshotApi = mock(SnapshotClient.class);
        when(prodHighLevelClient.snapshot()).thenReturn(snapshotApi);

        esClient.registerSnapshotRepository(INDEX_NAME);

        ArgumentCaptor<PutRepositoryRequest> reqCaptor = ArgumentCaptor.forClass(PutRepositoryRequest.class);
        verify(snapshotApi, times(1)).createRepository(reqCaptor.capture(), any());

        PutRepositoryRequest req = reqCaptor.getValue();
        assertThat(req).isEqualToComparingFieldByFieldRecursively(
                new PutRepositoryRequest()
                        .name(INDEX_NAME)
                        .type("s3")
                        .timeout(SNAPSHOT_OPERATION_TIMEOUT)
                        .masterNodeTimeout(SNAPSHOT_OPERATION_TIMEOUT)
                        .verify(true)
                        .settings(Settings.builder()
                                .put("bucket", ES_BUCKET)
                                .put("role_arn", REPORTS_ES_SNAPSHOT_ROLE_ARN)
                                .put("base_path", INDEX_NAME)
                                .put("max_snapshot_bytes_per_sec", MAX_SNAPSHOT_BYTES_PER_SEC)
                                .put("max_restore_bytes_per_sec", MAX_RESTORE_BYTES_PER_SEC)
                                .build()
                        )
        );
    }

    @Test
    public void testDeleteSnapshotRepository() throws IOException{
        SnapshotClient snapshotApi = mock(SnapshotClient.class);
        when(prodHighLevelClient.snapshot()).thenReturn(snapshotApi);

        GetRepositoriesResponse getRepositoriesResponse = mock(GetRepositoriesResponse.class);
        when(snapshotApi.getRepository(any(), any())).thenReturn(getRepositoriesResponse);
        when(getRepositoriesResponse.repositories()).thenReturn(List.of(new RepositoryMetaData(INDEX_NAME, INDEX_NAME, null)));

        esClient.deleteSnapshotRepository(INDEX_NAME);

        ArgumentCaptor<DeleteRepositoryRequest> reqCaptor = ArgumentCaptor.forClass(DeleteRepositoryRequest.class);
        verify(snapshotApi, times(1)).deleteRepository(reqCaptor.capture(), any());

        DeleteRepositoryRequest req = reqCaptor.getValue();
        assertThat(req.name()).isEqualTo(INDEX_NAME);
    }

    @Test
    public void shouldNotDeleteSnapshotRepositoryIfNotExists() throws IOException{
        SnapshotClient snapshotApi = mock(SnapshotClient.class);
        when(prodHighLevelClient.snapshot()).thenReturn(snapshotApi);

        GetRepositoriesResponse getRepositoriesResponse = mock(GetRepositoriesResponse.class);
        when(snapshotApi.getRepository(any(), any())).thenReturn(getRepositoriesResponse);
        when(getRepositoriesResponse.repositories()).thenReturn(List.of());

        esClient.deleteSnapshotRepository(INDEX_NAME);

        ArgumentCaptor<DeleteRepositoryRequest> reqCaptor = ArgumentCaptor.forClass(DeleteRepositoryRequest.class);
        verify(snapshotApi, times(0)).deleteRepository(reqCaptor.capture(), any());
    }

    @Test
    public void given_index_exists_restoreEsIndex_should_do_nothing() throws IOException {
        // given
        IndicesClient indicesClient = mock(IndicesClient.class);
        when(indicesClient.exists(argThat(getIndexRequestMatcher(INDEX_NAME)), any(RequestOptions.class))).thenReturn(true);

        when(prodHighLevelClient.indices()).thenReturn(indicesClient);

        // when
        esClient.restoreEsIndex(INDEX_NAME);

        // then
        verifyNoMoreInteractions(prodElasticSearchClient);
    }

    @Test
    public void given_index_does_not_exist_restoreEsIndex_should_submit_restore_request() throws IOException {
        // given
        IndicesClient indicesClient = mock(IndicesClient.class);
        when(indicesClient.exists(argThat(getIndexRequestMatcher(INDEX_NAME)), any(RequestOptions.class))).thenReturn(false);

        when(prodHighLevelClient.indices()).thenReturn(indicesClient);

        // when
        esClient.restoreEsIndex(INDEX_NAME);

        // then
        ArgumentCaptor<Request> reqCaptor = ArgumentCaptor.forClass(Request.class);
        verify(prodElasticSearchClient, times(1)).performRequest(reqCaptor.capture());

        Request req = reqCaptor.getValue();
        assertThat(req.getMethod()).isEqualTo("POST");
        assertThat(req.getEndpoint()).isEqualTo(String.join("/", "/_snapshot", INDEX_NAME, INDEX_NAME, "_restore"));
    }

    @Test
    public void given_ES_client_fails_to_restore_snapshot_then_restoreEsIndex_should_log_and_not_throw_exception() throws IOException {
        // given
        IndicesClient indicesClient = mock(IndicesClient.class);
        when(indicesClient.exists(argThat(getIndexRequestMatcher(INDEX_NAME)), any(RequestOptions.class))).thenReturn(false);

        when(prodHighLevelClient.indices()).thenReturn(indicesClient);

        ResponseException responseException = new ResponseException(mockResponse());
        when(prodElasticSearchClient.performRequest(any()))
            .thenThrow(responseException);

        // when
        esClient.restoreEsIndex(INDEX_NAME);

        // then no exception
    }

    private Response mockResponse() throws UnsupportedEncodingException {
        Response response = mock(Response.class);
        ProtocolVersion protocolVersion = new ProtocolVersion("HTTP", 1, 1);
        when(response.getStatusLine())
            .thenReturn(new BasicStatusLine(protocolVersion, 500, "InternalServerError"));
        when(response.getEntity()).thenReturn(new StringEntity(""));
        when(response.getRequestLine()).thenReturn(new BasicRequestLine("GET", "http://localhost:9200/_snapshot", protocolVersion));
        when(response.getHost()).thenReturn(new HttpHost("localhost"));
        return response;
    }

    private ArgumentMatcher<GetIndexRequest> getIndexRequestMatcher(String indexName) {
        return argument -> argument.indices()[0].equals(indexName);
    }

}
