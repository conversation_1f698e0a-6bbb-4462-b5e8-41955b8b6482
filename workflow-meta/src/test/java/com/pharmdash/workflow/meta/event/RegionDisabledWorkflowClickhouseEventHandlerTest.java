package com.pharmdash.workflow.meta.event;

import com.pharmdash.workflow.dp.meta.processorannotations.ReportType;
import com.pharmdash.workflow.meta.client.jobs.JobStatusDto;
import com.pharmdash.workflow.meta.service.WorkflowStatusService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class RegionDisabledWorkflowClickhouseEventHandlerTest {
    private static final long WORKFLOW_ID = 111L;
    private static final long NODE_ID = 222L;

    @InjectMocks
    private RegionDisabledWorkflowClickhouseEventHandler workflowClickhouseEventHandler;

    @Mock
    private WorkflowStatusService workflowStatusService;

    @Captor
    private ArgumentCaptor<Long> workflowIdCapture;
    @Captor
    private ArgumentCaptor<Long> nodeIdCapture;
    @Captor
    private ArgumentCaptor<JobStatusDto> statusCapture;
    @Captor
    private ArgumentCaptor<String> tagCapture;

    @Test
    public void handleWorkflowClickhouseIngestionEvent_should_return_and_do_nothing_if_workflow_id_is_null() {
        // test
        workflowClickhouseEventHandler.handleWorkflowClickhouseCleanUpEvent(
            new WorkflowClickhouseEvent(null, null, null, null, NODE_ID)
        );

        // verify
        verifyNoInteractions(workflowStatusService);
    }

    @Test
    public void handleWorkflowClickhouseIngestionEvent_should_return_and_do_nothing_if_node_id_is_null() {
        // test
        workflowClickhouseEventHandler.handleWorkflowClickhouseCleanUpEvent(
            new WorkflowClickhouseEvent(null, null, null, WORKFLOW_ID, null)
        );

        // verify
        verifyNoInteractions(workflowStatusService);
    }

    @Test
    public void handleWorkflowClickhouseIngestionEvent_should_return_and_fail_node_status() {
        // test
        workflowClickhouseEventHandler.handleWorkflowClickhouseIngestionEvent(
            new WorkflowClickhouseEvent(null, null, null, WORKFLOW_ID, NODE_ID)
        );

        // verify
        verify(workflowStatusService, times(1)).updateNodeStatus(
            workflowIdCapture.capture(),
            nodeIdCapture.capture(),
            statusCapture.capture(),
            tagCapture.capture(),
            any()
        );
        assertThat(workflowIdCapture.getValue()).isEqualTo(WORKFLOW_ID);
        assertThat(nodeIdCapture.getValue()).isEqualTo(NODE_ID);
        assertThat(statusCapture.getValue().getNodeReportType()).isEqualTo(ReportType.CLICKHOUSE);
        assertThat(statusCapture.getValue().getMessages()).isNotBlank();
        assertThat(statusCapture.getValue().getIngestionStartedOn()).isNotNull();
        assertThat(statusCapture.getValue().getErroredOn()).isEqualTo(statusCapture.getValue().getIngestionStartedOn());
        assertThat(tagCapture.getValue()).isEqualTo("Report Clickhouse Ingestor");

        verifyNoMoreInteractions(workflowStatusService);
    }

    @Test
    public void handleWorkflowClickhouseCleanUpEvent_should_do_nothing() {
        // test
        workflowClickhouseEventHandler.handleWorkflowClickhouseCleanUpEvent(
            new WorkflowClickhouseEvent(null, null, null, WORKFLOW_ID, null)
        );

        // verify
        verifyNoInteractions(workflowStatusService);
    }
}