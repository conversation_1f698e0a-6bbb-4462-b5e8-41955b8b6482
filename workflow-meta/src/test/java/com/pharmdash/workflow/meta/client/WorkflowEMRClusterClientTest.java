package com.pharmdash.workflow.meta.client;

import com.amazonaws.services.elasticmapreduce.model.RunJobFlowResult;
import com.amazonaws.services.elasticmapreduce.model.SpotProvisioningAllocationStrategy;
import com.amazonaws.services.s3.AmazonS3;
import com.google.common.collect.ImmutableList;
import com.pharmdash.workflow.dp.exporter.Checkpoint;
import com.pharmdash.workflow.dp.importer.JsonSubjectImporter;
import com.pharmdash.workflow.dp.javadto.PropertiesDTO;
import com.pharmdash.workflow.dp.javadto.WorkflowConfigDTO;
import com.pharmdash.workflow.dp.javadto.WorkflowDTO;
import com.pharmdash.workflow.dp.report.WaterfallReport;
import com.pharmdash.workflow.meta.client.jobs.JobDto;
import com.pharmdash.workflow.meta.client.jobs.JobStatusDto;
import com.pharmdash.workflow.meta.client.jobs.Status;
import com.pharmdash.workflow.meta.controller.errors.WorkflowNotRunningException;
import com.pharmdash.workflow.meta.persistence.GeneratingCheckpointEntity;
import com.pharmdash.workflow.meta.service.DefaultSpotFleetInstanceType;
import com.pharmdash.workflow.meta.service.EMRWorkflowClusterToolImpl;
import com.pharmdash.workflow.meta.service.WorkflowStatusService;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.junit.MockitoJUnitRunner;
import org.yaml.snakeyaml.Yaml;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS;
import static com.pharmdash.workflow.meta.utils.WorkflowSetupUtils.link;
import static com.pharmdash.workflow.meta.utils.WorkflowSetupUtils.node;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class WorkflowEMRClusterClientTest {

    @Captor
    private ArgumentCaptor<JobStatusDto> jobStatusDtoArgumentCaptor;

    @Captor
    private ArgumentCaptor<WorkflowDTO> workflowDTOArgumentCaptor;

    @Captor
    private ArgumentCaptor<String> yamlStringCapture;

    @Captor
    private ArgumentCaptor<EMRWorkflowRequest> emrWorkflowRequestArgumentCaptor;
    @Captor
    private ArgumentCaptor<GeneratingCheckpointEntity> generatingCheckpointEntityArgumentCaptor;

    private final WorkflowTaggerClient taggerMock = mock(WorkflowTaggerClient.class);

    @BeforeEach
    void beforeEach() {
        doReturn(new HashMap()).when(taggerMock).generateResourceTags(any());
    }


    @Test
    public void canHandleSparkProps() {
        Map<String, String> props = new HashMap<String, String>() {{
            put("spark.driver.maxResultSize", "12g");
            put("spark.default.parallelism", "1234");
            put("numInstances", "7");
        }};

        //test
        final Set<String> sparkConfigs = new WorkflowEMRClusterClient(null, null, null, taggerMock, null, null, null, null).getSparkConfigs(props);


        assertEquals(2, sparkConfigs.size());
        assertTrue(sparkConfigs.contains("spark.driver.maxResultSize=12g"));
        assertTrue(sparkConfigs.contains("spark.default.parallelism=1234"));
    }

    @Test
    public void canHandleTheInstance_FleetParams() {
        //setup
        WorkflowStatusService workflowStatusService = mock(WorkflowStatusService.class);
        AmazonS3 s3 = mock(AmazonS3.class);
        String bucket = "bucket";
        EMRWorkflowClusterToolImpl emrWorkflowClusterTool = mock(EMRWorkflowClusterToolImpl.class);
        final long workflowId = 1L;
        final WorkflowDTO workflowDTO = new WorkflowDTO();
        workflowDTO.setId(workflowId);

        final WorkflowConfigDTO workflowConfigDTO = new WorkflowConfigDTO();
        workflowDTO.setConfig(workflowConfigDTO);
        Map<String, String> props = new HashMap<String, String>() {{
            put("instanceTypes", "r5.2xlarge,m4.xlarge");
            put("subnets", "subnet1234,subnet1235");
            put("masterInstanceType", "r4.4xlarge");
        }};

        workflowConfigDTO.setProperties(props);

        when(workflowStatusService.workflowJobStatus(workflowId)).thenReturn(Optional.empty());

        mockTheWorkflowGenerateResult(emrWorkflowClusterTool);

        //test
        new WorkflowEMRClusterClient(s3, emrWorkflowClusterTool, workflowStatusService, taggerMock, bucket, null, null, null).submitJob(workflowDTO);

        //verify
        verify(emrWorkflowClusterTool, times(1)).createClusterAndSubmitWorkflowAsStep(emrWorkflowRequestArgumentCaptor.capture());
        final EMRWorkflowRequest theRequest = emrWorkflowRequestArgumentCaptor.getValue();
        assertEquals(2, theRequest.getSubnets().size());
        assertTrue(theRequest.getSubnets().contains("subnet1234"));
        assertTrue(theRequest.getSubnets().contains("subnet1235"));
        assertEquals("r4.4xlarge", theRequest.getMasterInstanceType());
        assertEquals(2, theRequest.getInstanceTypes().size());
        assertTrue(theRequest.getInstanceTypes().contains("r5.2xlarge"));
        assertTrue(theRequest.getInstanceTypes().contains("m4.xlarge"));
    }

    @Test
    public void canHandleKryClasses() {
        Map<String, String> props = new HashMap<String, String>() {{
            put("extraKryoClasses", "foo.bar.Far,noo.gar.Gar");
            put("spark.default.parallelism", "1234");
            put("numInstances", "7");
        }};

        //test
        final Set<String> classes = new WorkflowEMRClusterClient(null, null, null, taggerMock, null, null, null, null).getExtraKryoClass(props);

        assertEquals(2, classes.size());
        assertTrue(classes.contains("foo.bar.Far"));
        assertTrue(classes.contains("noo.gar.Gar"));
    }

    @Test
    public void canHandleKryClasses_ifNotPresent() {
        Map<String, String> props = new HashMap<String, String>() {{
            put("spark.default.parallelism", "1234");
            put("numInstances", "7");
        }};

        //test
        final Set<String> classes = new WorkflowEMRClusterClient(null, null, null, taggerMock, null, null, null, null).getExtraKryoClass(props);

        assertEquals(0, classes.size());
    }

    @Test
    public void canHandleBootstrapActions() {
        Map<String, String> props = new HashMap<String, String>() {{
            put("bootstrapActions", "{\"Name\":\"MyAction\", \"scriptBootstrapAction\":{\"Path\":\"s3://mybucket/filename\",\"Args\":[\"arg1\",\"arg2\"]}}");
            put("numInstances", "7");
        }};

        //test
        final Set<String> bootStrapActions = new WorkflowEMRClusterClient(null, null, null, taggerMock, null, null, null, null).getBootstrapActions(props);

        assertEquals(1, bootStrapActions.size());
        assertTrue(bootStrapActions.contains("{\"Name\":\"MyAction\", \"scriptBootstrapAction\":{\"Path\":\"s3://mybucket/filename\",\"Args\":[\"arg1\",\"arg2\"]}}"));
    }

    @Test
    public void canHandleBootstrapActions_ifNotPresent() {
        Map<String, String> props = new HashMap<String, String>() {{
            put("numInstances", "7");
        }};

        //test
        final Set<String> bootStrapActions = new WorkflowEMRClusterClient(null, null, null, taggerMock, null, null, null, null).getBootstrapActions(props);

        assertEquals(0, bootStrapActions.size());
    }

    @Test(expected = WorkflowNotRunningException.class)
    public void cancellingJob_throwsExceptionIfNotInProgress() throws WorkflowNotRunningException {
        //setup
        WorkflowStatusService workflowStatusService = mock(WorkflowStatusService.class);
        AmazonS3 s3 = mock(AmazonS3.class);
        String bucket = "bucket";
        EMRWorkflowClusterToolImpl emrWorkflowClusterTool = mock(EMRWorkflowClusterToolImpl.class);
        final long workflowId = 1L;
        final JobDto jobDto = JobDto.builder().jobId("jobId").status(Status.CANCELLING).build();
        when(workflowStatusService.workflowJobStatus(workflowId)).thenReturn(Optional.of(jobDto));

        //test
        new WorkflowEMRClusterClient(s3, emrWorkflowClusterTool, workflowStatusService, taggerMock, bucket, null, null, null).cancelJob(workflowId);

        //verified by annotation
    }


    @Test()
    public void cancellingHappensWhenRunning() throws WorkflowNotRunningException {
        //setup
        WorkflowStatusService workflowStatusService = mock(WorkflowStatusService.class);
        AmazonS3 s3 = mock(AmazonS3.class);
        String bucket = "bucket";
        EMRWorkflowClusterToolImpl emrWorkflowClusterTool = mock(EMRWorkflowClusterToolImpl.class);
        final long workflowId = 1L;
        final JobDto jobDto = JobDto.builder().jobId("j-1234").status(Status.RUNNING).build();
        when(workflowStatusService.workflowJobStatus(workflowId)).thenReturn(Optional.of(jobDto));

        //test
        new WorkflowEMRClusterClient(s3, emrWorkflowClusterTool, workflowStatusService, taggerMock, bucket, null, null, null).cancelJob(workflowId);

        //verify
        verify(workflowStatusService, times(1)).updateWorkflowStatus(eq(1L), jobStatusDtoArgumentCaptor.capture(), eq("Warehouse Meta"), any());
        verify(emrWorkflowClusterTool, times(1)).shutdownCluster("j-1234");
        assertNotNull(jobStatusDtoArgumentCaptor.getValue().getWorkflowCancellingStartedOn());
        assertEquals(Status.CANCELLING, jobStatusDtoArgumentCaptor.getValue().getWorkflowStatus());
        assertEquals("Workflow[1] cancelling started", jobStatusDtoArgumentCaptor.getValue().getMessages());
    }

    @Test
    public void shouldSaveWorkflowStatusFinishedWhenAllReportsAreFacilityInsight() {
        // arrange
        WorkflowStatusService workflowStatusService = mock(WorkflowStatusService.class);
        AmazonS3 s3 = mock(AmazonS3.class);
        String bucket = "bucket";
        EMRWorkflowClusterToolImpl emrWorkflowClusterTool = mock(EMRWorkflowClusterToolImpl.class);
        WorkflowEMRClusterClient workflowEMRClusterClient = new WorkflowEMRClusterClient(s3, emrWorkflowClusterTool, workflowStatusService, taggerMock, bucket, null, null, null);
        WorkflowDTO workflowDTO = new WorkflowDTO();
        long workflowId = 1L;
        workflowDTO.setId(workflowId);

        when(workflowStatusService.workflowJobStatus(workflowId)).thenReturn(Optional.of(JobDto.builder().status(Status.SUCCEEDED).build()));

        // act
        workflowEMRClusterClient.markWorkflowJobAsFinished(workflowDTO);

        // assert
        verify(workflowStatusService).initWorkflowStatus(workflowDTOArgumentCaptor.capture(), jobStatusDtoArgumentCaptor.capture(), any());
        assertThat(workflowDTOArgumentCaptor.getValue().getId()).isEqualTo(workflowId);
        assertThat(jobStatusDtoArgumentCaptor.getValue().getWorkflowStatus()).isEqualTo(Status.SUCCEEDED);
        assertThat(jobStatusDtoArgumentCaptor.getValue().getMessages()).isEqualTo("Workflow[1] finished.");
        assertThat(jobStatusDtoArgumentCaptor.getValue().getWorkflowQueuedOn()).isNotNull();
        assertThat(jobStatusDtoArgumentCaptor.getValue().getWorkflowSubmittedOn()).isNotNull();
        assertThat(jobStatusDtoArgumentCaptor.getValue().getWorkflowStartedOn()).isNotNull();
        assertThat(jobStatusDtoArgumentCaptor.getValue().getWorkflowCompletedOn()).isNotNull();

    }


    @Test
    public void workflowNotSubmittedIf_alreadyInProgress() {
        //setup
        WorkflowStatusService workflowStatusService = mock(WorkflowStatusService.class);
        AmazonS3 s3 = mock(AmazonS3.class);
        String bucket = "bucket";
        EMRWorkflowClusterToolImpl emrWorkflowClusterTool = mock(EMRWorkflowClusterToolImpl.class);
        final long workflowId = 1L;
        final WorkflowDTO workflowDTO = new WorkflowDTO();
        workflowDTO.setId(workflowId);
        final JobDto jobDto = JobDto.builder().jobId("j-1234").status(Status.RUNNING).build();
        when(workflowStatusService.workflowJobStatus(workflowId)).thenReturn(Optional.of(jobDto));

        //test
        new WorkflowEMRClusterClient(s3, emrWorkflowClusterTool, workflowStatusService, taggerMock, bucket, null, null, null).submitJob(workflowDTO);


        //verify
        verify(emrWorkflowClusterTool, times(0)).createClusterAndSubmitWorkflowAsStep(any());
    }


    @Test
    public void workflow_isSaved_isSubmitted_propsGetPassedDown_onSubmission() {
        //setup
        WorkflowStatusService workflowStatusService = mock(WorkflowStatusService.class);
        AmazonS3 s3 = mock(AmazonS3.class);
        String bucket = "bucket";
        EMRWorkflowClusterToolImpl emrWorkflowClusterTool = mock(EMRWorkflowClusterToolImpl.class);
        final long workflowId = 1L;
        final WorkflowDTO workflowDTO = new WorkflowDTO();
        workflowDTO.setId(workflowId);

        final WorkflowConfigDTO workflowConfigDTO = new WorkflowConfigDTO();
        workflowDTO.setConfig(workflowConfigDTO);
        final HashMap<String, String> properties = new HashMap<>();
        properties.put("USE_EMR_CLUSTER", "true");
        properties.put("spark.foo.bar", "1");
        properties.put("--submit-arg.foo.bar", "1");
        properties.put("extraKryoClasses", "foo.bar.Gar");
        properties.put("subnets", "thisIsASillySubnet");

        workflowConfigDTO.setProperties(properties);

        when(workflowStatusService.workflowJobStatus(workflowId)).thenReturn(Optional.empty());

        mockTheWorkflowGenerateResult(emrWorkflowClusterTool);

        WorkflowEMRClusterClient workflowEMRClusterClient = new WorkflowEMRClusterClient(
            s3, emrWorkflowClusterTool, workflowStatusService, taggerMock, bucket, null, null, null);

        //test
        workflowEMRClusterClient.submitJob(workflowDTO);

        //verify
        verify(emrWorkflowClusterTool, times(1)).createClusterAndSubmitWorkflowAsStep(emrWorkflowRequestArgumentCaptor.capture());
        verify(s3, times(1)).putObject(eq("bucket"), eq("workflow/workflow-config/1/workflow.yml"), yamlStringCapture.capture());
        verify(workflowStatusService, times(1)).initWorkflowStatus(eq(workflowDTO), jobStatusDtoArgumentCaptor.capture(), eq(Optional.empty()));

        final WorkflowDTO savedWorkflow = (WorkflowDTO) new Yaml().load(yamlStringCapture.getValue());
        assertEquals(1l, savedWorkflow.getId());
        assertNotNull(jobStatusDtoArgumentCaptor.getValue().getWorkflowSubmittedOn());
        assertEquals(Status.RUNNING, jobStatusDtoArgumentCaptor.getValue().getWorkflowStatus());
        assertEquals("EMR_CLUSTER", jobStatusDtoArgumentCaptor.getValue().getWorkflowType());
        assertTrue(jobStatusDtoArgumentCaptor.getValue().getMessages().startsWith("Workflow[1] job submitted: "));
        assertEquals("spark.foo.bar=1", emrWorkflowRequestArgumentCaptor.getValue().getSparkConfigs().iterator().next());
        assertEquals("--submit-arg.foo.bar=1", emrWorkflowRequestArgumentCaptor.getValue().getSparkExtraSubmitArgs().iterator().next());
        assertEquals("foo.bar.Gar", emrWorkflowRequestArgumentCaptor.getValue().getExtraKryoClasses().iterator().next());
        assertEquals(Collections.singletonList("thisIsASillySubnet"), emrWorkflowRequestArgumentCaptor.getValue().getSubnets());
    }

    @Test
    public void sensibleDefaultValuesUsedWhenSubmitted() {
        //setup
        WorkflowStatusService workflowStatusService = mock(WorkflowStatusService.class);
        AmazonS3 s3 = mock(AmazonS3.class);
        String bucket = "bucket";
        EMRWorkflowClusterToolImpl emrWorkflowClusterTool = mock(EMRWorkflowClusterToolImpl.class);
        final long workflowId = 1L;
        final WorkflowDTO workflowDTO = new WorkflowDTO();

        final WorkflowConfigDTO workflowConfigDTO = new WorkflowConfigDTO();
        final HashMap<String, String> properties = new HashMap<>();
        properties.put("USE_EMR_CLUSTER", "true"); //no properties set, code will default some things
        workflowConfigDTO.setProperties(properties);

        workflowDTO.setId(workflowId);
        workflowDTO.setConfig(workflowConfigDTO);

        when(workflowStatusService.workflowJobStatus(workflowId)).thenReturn(Optional.empty());

        mockTheWorkflowGenerateResult(emrWorkflowClusterTool);

        //test
        final JobDto jobDto = new WorkflowEMRClusterClient(s3, emrWorkflowClusterTool, workflowStatusService, taggerMock, bucket, null, null, null).submitJob(workflowDTO);


        //verify
        verify(emrWorkflowClusterTool, times(1)).createClusterAndSubmitWorkflowAsStep(emrWorkflowRequestArgumentCaptor.capture());
        final EMRWorkflowRequest request = emrWorkflowRequestArgumentCaptor.getValue();

        //no args passed to spark - would use default spark settings
        assertNull(request.getBidPrice());

        //defaulted
        assertEquals(true, request.getAreCoresSpot());
        assertEquals(10, request.getNumInstances().intValue());

        assertEquals("************", request.getAwsAccountId());
        assertEquals("ap-southeast-2", request.getAwsRegion());
        assertEquals("dev", request.getEnvName());
        assertEquals("emr-7.2.0", request.getEmrVersion());
        assertEquals("s3://bucket/workflow/workflow-config/1/workflow.yml", request.getWorkflowPath());

        assertTrue(request.getSparkConfigs().isEmpty());
        assertNull(request.getConfigurations());
    }

    @Test
    public void shouldSaveGeneratingCheckpointNodeHashInDryRun() {
        WorkflowDTO workflowDTO = sampleWorkflowWithCheckpointNode();
        WorkflowStatusService workflowStatusService = mock(WorkflowStatusService.class);
        AmazonS3 s3 = mock(AmazonS3.class);
        String bucket = "bucket";
        EMRWorkflowClusterToolImpl emrWorkflowClusterTool = mock(EMRWorkflowClusterToolImpl.class);

        mockTheWorkflowGenerateResult(emrWorkflowClusterTool);

        // Test
        new WorkflowEMRClusterClient(s3, emrWorkflowClusterTool, workflowStatusService, taggerMock, bucket, null, null, null).submitJob(workflowDTO);


        // verify
        verify(workflowStatusService, times(1)).saveGeneratingCheckpoint(generatingCheckpointEntityArgumentCaptor.capture());
        verify(workflowStatusService, times(1)).initWorkflowStatus(eq(workflowDTO), jobStatusDtoArgumentCaptor.capture(), eq(Optional.empty()));

        assertEquals(Status.RUNNING, jobStatusDtoArgumentCaptor.getValue().getWorkflowStatus());

        GeneratingCheckpointEntity generatingCheckpoint = generatingCheckpointEntityArgumentCaptor.getValue();
        assertThat(generatingCheckpoint.getCheckpointUpstreamNodeHashes().size()).isEqualTo(1);
        assertThat(generatingCheckpoint.getCheckpointUpstreamNodeHashes().get(0)).isNotEmpty();
    }

    @Test
    public void shouldUseDefaultSubnets_andSecurityGroups_ForEnv_ifNoOtherPropsSet() {
        //setup
        WorkflowStatusService workflowStatusService = mock(WorkflowStatusService.class);
        AmazonS3 s3 = mock(AmazonS3.class);
        String bucket = "bucket";
        EMRWorkflowClusterToolImpl emrWorkflowClusterTool = mock(EMRWorkflowClusterToolImpl.class);
        final long workflowId = 1L;
        final WorkflowDTO workflowDTO = new WorkflowDTO();
        final WorkflowConfigDTO workflowConfigDTO = new WorkflowConfigDTO();
        workflowDTO.setConfig(workflowConfigDTO);
        workflowConfigDTO.setProperties(new HashMap<>());//empty props
        workflowDTO.setId(workflowId);

        mockTheWorkflowGenerateResult(emrWorkflowClusterTool);

        final List<String> securityGroups = Collections.singletonList("securityGroup1");
        final List<String> subnets = Arrays.asList("subnet1", "subnet2");

        //test
        new WorkflowEMRClusterClient(s3, emrWorkflowClusterTool, workflowStatusService, taggerMock, bucket, securityGroups, subnets, null).submitJob(workflowDTO);

        //veridfy
        verify(emrWorkflowClusterTool, times(1)).createClusterAndSubmitWorkflowAsStep(emrWorkflowRequestArgumentCaptor.capture());
        assertThat(emrWorkflowRequestArgumentCaptor.getValue().getSubnets()).isEqualTo(subnets);
        assertThat(emrWorkflowRequestArgumentCaptor.getValue().getAdditionalSecurityGroups()).isEqualTo(securityGroups);

    }

    @Test
    public void shouldReturnTheJobLogsLink_success() {
        //setup
        WorkflowStatusService workflowStatusService = mock(WorkflowStatusService.class);
        AmazonS3 s3 = mock(AmazonS3.class);
        String bucket = "bucket";
        EMRWorkflowClusterToolImpl emrWorkflowClusterTool = mock(EMRWorkflowClusterToolImpl.class);
        final long workflowId = 1L;
        final WorkflowDTO workflowDTO = new WorkflowDTO();
        final WorkflowConfigDTO workflowConfigDTO = new WorkflowConfigDTO();
        workflowDTO.setConfig(workflowConfigDTO);
        workflowConfigDTO.setProperties(new HashMap<>());//empty props
        workflowDTO.setId(workflowId);
        mockTheWorkflowGenerateResult(emrWorkflowClusterTool);

        //test
        final JobDto jobDto = new WorkflowEMRClusterClient(s3, emrWorkflowClusterTool, workflowStatusService, taggerMock, bucket, null, null, null).submitJob(workflowDTO);

        //verify
        assertNotNull(jobDto.getTasks());
        assertEquals(1, jobDto.getTasks().size());
        assertEquals("https://ap-southeast-2.console.aws.amazon.com/emr/home?region=ap-southeast-2#/clusterDetails/j-1", jobDto.getTasks().get(0).getJobLogsLink());
        assertEquals("https://ap-southeast-2.console.aws.amazon.com/emr/home?region=ap-southeast-2#/clusterDetails/j-1", jobDto.getTasks().get(0).getJobErrorLogsLink());
    }

    @Test
    public void securityGroupProps_handledCorrectly() {
        //setup
        WorkflowStatusService workflowStatusService = mock(WorkflowStatusService.class);
        AmazonS3 s3 = mock(AmazonS3.class);
        String bucket = "bucket";
        EMRWorkflowClusterToolImpl emrWorkflowClusterTool = mock(EMRWorkflowClusterToolImpl.class);
        final long workflowId = 1L;
        final WorkflowDTO workflowDTO = new WorkflowDTO();
        final WorkflowConfigDTO workflowConfigDTO = new WorkflowConfigDTO();
        workflowDTO.setConfig(workflowConfigDTO);
        workflowConfigDTO.setProperties(new HashMap<String, String>() {{
            put("additionalSecurityGroups", "sc1,sc2");
        }});
        workflowDTO.setId(workflowId);

        mockTheWorkflowGenerateResult(emrWorkflowClusterTool);

        //test
        new WorkflowEMRClusterClient(s3, emrWorkflowClusterTool, workflowStatusService, taggerMock, bucket, null, null, null).submitJob(workflowDTO);

        //veridfy
        verify(emrWorkflowClusterTool, times(1)).createClusterAndSubmitWorkflowAsStep(emrWorkflowRequestArgumentCaptor.capture());
        assertThat(emrWorkflowRequestArgumentCaptor.getValue().getAdditionalSecurityGroups().get(0)).isEqualTo("sc1");
        assertThat(emrWorkflowRequestArgumentCaptor.getValue().getAdditionalSecurityGroups().get(1)).isEqualTo("sc2");

    }

    private void mockTheWorkflowGenerateResult(EMRWorkflowClusterToolImpl emrWorkflowClusterTool) {
        RunJobFlowResult result = mock(RunJobFlowResult.class);
        when(result.getJobFlowId()).thenReturn("j-1");
        when(emrWorkflowClusterTool.createClusterAndSubmitWorkflowAsStep(any())).thenReturn(result);
    }

    private WorkflowDTO sampleWorkflowWithCheckpointNode() {
        WorkflowDTO workflow = new WorkflowDTO();
        workflow.setDryRun(true);
        workflow.setId(0L);
        workflow.setNodes(ImmutableList.of(
            node(0L, "dataset importer", JsonSubjectImporter.class.getName(), new PropertiesDTO()),
            node(4L, "Checkpoint", Checkpoint.class.getName(), new PropertiesDTO()),
            node(5L, "waterfall", WaterfallReport.class.getName(), new PropertiesDTO()),
            node(6L, "waterfall", WaterfallReport.class.getName(), new PropertiesDTO())
        ));
        workflow.setLinks(ImmutableList.of(
            link(0L, 0L, EVENTS, 4L, EVENTS),
            link(1L, 4L, EVENTS, 5L, EVENTS),
            link(2L, 3L, EVENTS, 6L, EVENTS)
        ));

        workflow.setExecutingCheckpointUpstreamNodeHashes(ImmutableList.of("6d8350daa2e62607b253b9b504a1200c"));
        return workflow;
    }

    @Test
    public void should_return_default_fleet_instances_when_no_instance_types_specified() {
        // setup
        WorkflowStatusService workflowStatusService = mock(WorkflowStatusService.class);
        AmazonS3 s3 = mock(AmazonS3.class);
        String bucket = "bucket";
        EMRWorkflowClusterToolImpl emrWorkflowClusterTool = mock(EMRWorkflowClusterToolImpl.class);
        long workflowId = 1L;
        WorkflowDTO workflowDTO = new WorkflowDTO();
        workflowDTO.setId(workflowId);

        WorkflowConfigDTO workflowConfigDTO = new WorkflowConfigDTO();
        workflowDTO.setConfig(workflowConfigDTO);

        when(workflowStatusService.workflowJobStatus(workflowId)).thenReturn(Optional.empty());

        mockTheWorkflowGenerateResult(emrWorkflowClusterTool);

        // test
        new WorkflowEMRClusterClient(s3, emrWorkflowClusterTool, workflowStatusService, taggerMock, bucket, null, null, null).submitJob(workflowDTO);

        // verify
        verify(emrWorkflowClusterTool, times(1)).createClusterAndSubmitWorkflowAsStep(emrWorkflowRequestArgumentCaptor.capture());
        EMRWorkflowRequest emrWorkflowRequest = emrWorkflowRequestArgumentCaptor.getValue();

        assertEquals(DefaultSpotFleetInstanceType.values().length, emrWorkflowRequest.getInstanceTypes().size());
        List<String> defaultFleetInstanceTypes = Arrays.stream(DefaultSpotFleetInstanceType.values())
            .map(DefaultSpotFleetInstanceType::getInstanceType)
            .collect(Collectors.toList());
        assertEquals(defaultFleetInstanceTypes, emrWorkflowRequest.getInstanceTypes());
        assertEquals(SpotProvisioningAllocationStrategy.CapacityOptimized.toString(), emrWorkflowRequest.getSpotAllocationStrategy());
        assertEquals(20, emrWorkflowRequest.getSpotAllocationTimeOutInMinute().intValue());
    }

    @Test
    public void no_default_value_for_master_instance_type_and_instance_type_when_they_are_not_specified() {
        // setup
        WorkflowStatusService workflowStatusService = mock(WorkflowStatusService.class);
        AmazonS3 s3 = mock(AmazonS3.class);
        String bucket = "bucket";
        EMRWorkflowClusterToolImpl emrWorkflowClusterTool = mock(EMRWorkflowClusterToolImpl.class);
        long workflowId = 1L;
        WorkflowDTO workflowDTO = new WorkflowDTO();
        workflowDTO.setId(workflowId);

        WorkflowConfigDTO workflowConfigDTO = new WorkflowConfigDTO();
        workflowDTO.setConfig(workflowConfigDTO);

        when(workflowStatusService.workflowJobStatus(workflowId)).thenReturn(Optional.empty());

        mockTheWorkflowGenerateResult(emrWorkflowClusterTool);

        // test
        new WorkflowEMRClusterClient(s3, emrWorkflowClusterTool, workflowStatusService, taggerMock, bucket, null, null, null).submitJob(workflowDTO);

        // verify
        verify(emrWorkflowClusterTool, times(1)).createClusterAndSubmitWorkflowAsStep(emrWorkflowRequestArgumentCaptor.capture());
        EMRWorkflowRequest emrWorkflowRequest = emrWorkflowRequestArgumentCaptor.getValue();

        assertNull(emrWorkflowRequest.getInstanceType());
        assertNull(emrWorkflowRequest.getMasterInstanceType());
    }
}
