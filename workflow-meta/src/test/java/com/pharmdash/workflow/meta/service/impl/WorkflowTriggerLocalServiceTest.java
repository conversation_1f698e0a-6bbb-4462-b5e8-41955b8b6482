package com.pharmdash.workflow.meta.service.impl;

import com.pharmdash.workflow.meta.utils.StreamingUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.util.FileSystemUtils;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class WorkflowTriggerLocalServiceTest {

    private final WorkflowTriggerLocalService workflowTriggerLocalService = new WorkflowTriggerLocalService();

    @Before
    public void before() throws IOException {
        String triggersOutputPath = workflowTriggerLocalService.getTriggerOutputPath(1L, 1L, "patient");
        Path path = Paths.get(triggersOutputPath);
        FileSystemUtils.deleteRecursively(path);
    }

    @After
    public void cleanup() throws IOException {
        String triggersOutputPath = workflowTriggerLocalService.getTriggerOutputPath(1L, 1L, "patient");
        Path path = Paths.get(triggersOutputPath);
        FileSystemUtils.deleteRecursively(path);
    }

    @Test
    public void shouldReadTriggerFile() throws IOException {
        List<String> fileLines = Arrays.asList("{\"subjectId\":\"1\",\"subjectTags\":{\"gender\":[\"Male\"]},\"event\":\"A\",\"date\":\"2018-04-11\",\"eventTags\":{\"stateAtEvent\":[\"VIC\"]}}",
            "{\"subjectId\":\"2\",\"subjectTags\":{\"gender\":[\"Female\"]},\"event\":\"A\",\"date\":\"2016-02-12\",\"eventTags\":{\"stateAtEvent\":[\"QLD\"]}}");
        List<String> file2Lines = Arrays.asList("{\"subjectId\":\"1\",\"subjectTags\":{\"gender\":[\"Male\"]},\"event\":\"A\",\"date\":\"2018-04-11\",\"eventTags\":{\"stateAtEvent\":[\"VIC\"]}}",
            "{\"subjectId\":\"2\",\"subjectTags\":{\"gender\":[\"Female\"]},\"event\":\"A\",\"date\":\"2016-02-12\",\"eventTags\":{\"stateAtEvent\":[\"QLD\"]}}");

        String triggersOutputPath = workflowTriggerLocalService.getTriggerOutputPath(1L, 1L, "patient");
        Path path = Paths.get(triggersOutputPath);
        Files.createDirectories(path);
        Path filePath = path.resolve("trigger.json");
        Files.write(filePath, fileLines);
        filePath = path.resolve("trigger2.json");
        Files.write(filePath, file2Lines);

        StreamingResponseBody result = workflowTriggerLocalService.triggerOutput(1L, 1L, "patient");
        assertThat(StreamingUtils.responseToString(result)).isEqualTo("{\"subjectId\":\"1\",\"subjectTags\":{\"gender\":[\"Male\"]},\"event\":\"A\",\"date\":\"2018-04-11\",\"eventTags\":{\"stateAtEvent\":[\"VIC\"]}}\n" +
            "{\"subjectId\":\"2\",\"subjectTags\":{\"gender\":[\"Female\"]},\"event\":\"A\",\"date\":\"2016-02-12\",\"eventTags\":{\"stateAtEvent\":[\"QLD\"]}}\n" +
            "{\"subjectId\":\"1\",\"subjectTags\":{\"gender\":[\"Male\"]},\"event\":\"A\",\"date\":\"2018-04-11\",\"eventTags\":{\"stateAtEvent\":[\"VIC\"]}}\n" +
            "{\"subjectId\":\"2\",\"subjectTags\":{\"gender\":[\"Female\"]},\"event\":\"A\",\"date\":\"2016-02-12\",\"eventTags\":{\"stateAtEvent\":[\"QLD\"]}}\n");
    }

    @Test
    public void shouldReadEmptyTriggerOutputFileAndReturnEmptyList() throws IOException {
        String triggersOutputPath = workflowTriggerLocalService.getTriggerOutputPath(1L, 1L, "patient");
        Path path = Paths.get(triggersOutputPath);
        Files.createDirectories(path);
        Path filePath = path.resolve("trigger.json");
        Files.write(filePath, "".getBytes(StandardCharsets.UTF_8));

        StreamingResponseBody result = workflowTriggerLocalService.triggerOutput(1L, 1L, "patient");
        assertThat(StreamingUtils.responseToString(result)).isEqualTo("");
    }

    @Test(expected = RuntimeException.class)
    public void shouldThrowExceptionIfReaderFileNotFound() {
        StreamingUtils.responseToString(workflowTriggerLocalService.triggerOutput(1L, 2L, "patient"));  // non-existent node
    }

    @Test
    public void shouldReturnIsNotEmptyResultWhenThereAreRecord() throws IOException {
        List<String> fileLines = Arrays.asList("{\"subjectId\":\"1\",\"subjectTags\":{\"gender\":[\"Male\"]},\"event\":\"A\",\"date\":\"2018-04-11\",\"eventTags\":{\"stateAtEvent\":[\"VIC\"]}}",
            "{\"subjectId\":\"2\",\"subjectTags\":{\"gender\":[\"Female\"]},\"event\":\"A\",\"date\":\"2016-02-12\",\"eventTags\":{\"stateAtEvent\":[\"QLD\"]}}");
        List<String> file2Lines = Arrays.asList("{\"subjectId\":\"1\",\"subjectTags\":{\"gender\":[\"Male\"]},\"event\":\"A\",\"date\":\"2018-04-11\",\"eventTags\":{\"stateAtEvent\":[\"VIC\"]}}",
            "{\"subjectId\":\"2\",\"subjectTags\":{\"gender\":[\"Female\"]},\"event\":\"A\",\"date\":\"2016-02-12\",\"eventTags\":{\"stateAtEvent\":[\"QLD\"]}}");

        String triggersOutputPath = workflowTriggerLocalService.getTriggerOutputPath(1L, 1L, "patient");
        Path path = Paths.get(triggersOutputPath);
        Files.createDirectories(path);
        Path filePath = path.resolve("trigger.json");
        Files.write(filePath, fileLines);
        filePath = path.resolve("trigger2.json");
        Files.write(filePath, file2Lines);

        assert !workflowTriggerLocalService.isTriggerOutputEmpty(1L, 1L, "patient");
    }

    @Test
    public void shouldReturnIsEmptyResultWhenThereAreNoRecord() throws IOException {
        List<String> fileLines = Collections.singletonList("");
        List<String> file2Lines = Collections.singletonList("");

        String triggersOutputPath = workflowTriggerLocalService.getTriggerOutputPath(1L, 1L, "patient");
        Path path = Paths.get(triggersOutputPath);
        Files.createDirectories(path);
        Path filePath = path.resolve("trigger.json");
        Files.write(filePath, fileLines);
        filePath = path.resolve("trigger2.json");
        Files.write(filePath, file2Lines);

        assert workflowTriggerLocalService.isTriggerOutputEmpty(1L, 1L, "patient");
    }

    @Test
    public void shouldReturnIsEmptyResultWhenThereAreNoFile() throws IOException {
        String triggersOutputPath = workflowTriggerLocalService.getTriggerOutputPath(1L, 1L, "patient");
        Path path = Paths.get(triggersOutputPath);
        Files.createDirectories(path);
        assert workflowTriggerLocalService.isTriggerOutputEmpty(1L, 1L, "patient");
    }

}
