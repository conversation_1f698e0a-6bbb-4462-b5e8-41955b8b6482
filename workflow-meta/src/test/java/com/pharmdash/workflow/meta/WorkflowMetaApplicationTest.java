package com.pharmdash.workflow.meta;

import com.pharmdash.workflow.meta.controller.DataProcessorController;
import com.pharmdash.workflow.meta.controller.WorkflowController;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = WorkflowMetaApplication.class)
public class WorkflowMetaApplicationTest {

    @Autowired
    private ApplicationContext context;

    @Test
    public void contextLoads() {
        assertThat(context.getBean(WorkflowController.class))
            .as("Should be able to locate WorkflowController.")
            .isNotNull();

        assertThat(context.getBean(DataProcessorController.class))
            .as("Should be able to locate DataProcessorController.")
            .isNotNull();
    }
}
