package com.pharmdash.workflow.meta.service.validation;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import com.pharmdash.workflow.dp.javadto.ViolationDTO;
import com.pharmdash.workflow.dp.javadto.WorkflowConfigDTO;
import com.pharmdash.workflow.dp.javadto.WorkflowDTO;
import com.pharmdash.workflow.dp.meta.processorannotations.DataStreamType;
import com.pharmdash.workflow.meta.model.ProcessorMetadata;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import static com.pharmdash.workflow.dp.meta.processorannotations.Channel.DATASET;
import static com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS;
import static com.pharmdash.workflow.dp.meta.processorannotations.Channel.REFERENCE_DATA;
import static com.pharmdash.workflow.dp.meta.processorannotations.DataStreamType.REF_DATA;
import static com.pharmdash.workflow.dp.meta.processorannotations.DataStreamType.SUBJECT;
import static com.pharmdash.workflow.dp.meta.processorannotations.DataStreamType.TABLE;
import static com.pharmdash.workflow.dp.meta.processorannotations.ProcessorCategory.ALGORITHM;
import static com.pharmdash.workflow.dp.meta.processorannotations.ProcessorCategory.IMPORT;
import static com.pharmdash.workflow.dp.meta.processorannotations.ProcessorCategory.REPORT;
import static com.pharmdash.workflow.dp.meta.processorannotations.ProcessorCategory.TRANSFORMATION;
import static com.pharmdash.workflow.meta.utils.WorkflowSetupUtils.channel;
import static com.pharmdash.workflow.meta.utils.WorkflowSetupUtils.channels;
import static com.pharmdash.workflow.meta.utils.WorkflowSetupUtils.link;
import static com.pharmdash.workflow.meta.utils.WorkflowSetupUtils.node;
import static com.pharmdash.workflow.meta.utils.WorkflowSetupUtils.processor;
import static com.pharmdash.workflow.meta.utils.WorkflowSetupUtils.prop;
import static com.pharmdash.workflow.meta.utils.WorkflowSetupUtils.tags;
import static com.pharmdash.workflow.meta.utils.WorkflowSetupUtils.workflow;
import static java.util.Collections.emptyList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class WorkflowValidatorTest {

    private WorkflowDataProcessorValidator workflowDataProcessorValidator;
    private WorkflowItemGroupDependencyValidator workflowItemGroupDependencyValidator;
    private WorkflowValidator validator;

    private static final List<ProcessorMetadata> PROCESSORS = ImmutableList.of(
        processor("ReferenceDataImporter", IMPORT,
            channels(),
            channels(channel(REFERENCE_DATA, REF_DATA.toString(), false))
        ),
        processor("SubjectImporter", IMPORT,
            channels(channel(REFERENCE_DATA, REF_DATA.toString(), true)),
            channels(channel(EVENTS, DataStreamType.SUBJECT.toString(), false))
        ),
        processor("CsvImporter", IMPORT,
            channels(),
            channels(channel(DATASET, DataStreamType.TABLE.toString(), false))
        ),
        processor("Filter", TRANSFORMATION,
            channels(
                channel(DATASET, TABLE.toString(), true),
                channel(EVENTS, SUBJECT.toString(), true)
            ),
            channels(channel(EVENTS, SUBJECT.toString(), false))
        ),
        processor("FilterWithOptionalInput", TRANSFORMATION,
            channels(
                channel(DATASET, TABLE.toString(), false),
                channel(EVENTS, SUBJECT.toString(), true)
            ),
            channels(channel(EVENTS, SUBJECT.toString(), false))
        ),
        processor("Algorithm", ALGORITHM,
            channels(channel(EVENTS, SUBJECT.toString(), true)),
            channels(channel(EVENTS, SUBJECT.toString(), false)),
            tags(ImmutableList.of("eventTagA", "eventTagB"), emptyList()),
            null
        ),
        processor("Report", REPORT,
            channels(channel(EVENTS, SUBJECT.toString(), true)),
            channels()
        ),
        processor("ReportWithProps", REPORT,
            channels(channel(EVENTS, SUBJECT.toString(), true)),
            channels()
        ),
        processor("ReportThatRequiresSubjectTag", REPORT,
            channels(channel(EVENTS, SUBJECT.toString(), true)),
            channels(),
            null,
            tags(emptyList(), ImmutableList.of("subjectTagA"))
        ),
        processor("ReportThatRequiresEventTag", REPORT,
            channels(channel(EVENTS, SUBJECT.toString(), true)),
            channels(),
            null,
            tags(ImmutableList.of("eventTagA"), emptyList())
        ),
        processor("ReportThatRequiresEventTagAndSubjectTag", REPORT,
            channels(channel(EVENTS, SUBJECT.toString(), true)),
            channels(),
            null,
            tags(ImmutableList.of("eventTagA", "eventTagB"), ImmutableList.of("subjectTagA"))
        ),
        processor("ReportThatRequiresUnknownEventTag", REPORT,
            channels(channel(EVENTS, SUBJECT.toString(), true)),
            channels(),
            null,
            tags(ImmutableList.of("eventTagA", "eventTagZ"), ImmutableList.of("subjectTagA"))
        ),
        processor("ProcessorThatOutputSubjectTag", REPORT,
            channels(channel(EVENTS, SUBJECT.toString(), true)),
            channels(channel(EVENTS, SUBJECT.toString(), false)),
            tags(emptyList(), ImmutableList.of("subjectTagA")),
            null
        ),
        processor("ProcessorThatOutputEventTag", REPORT,
            channels(channel(EVENTS, SUBJECT.toString(), true)),
            channels(channel(EVENTS, SUBJECT.toString(), false)),
            tags(ImmutableList.of("eventTagA"), emptyList()),
            null
        )
    );

    @Before
    public void setup() {
        workflowDataProcessorValidator = mock(WorkflowDataProcessorValidator.class);
        when(workflowDataProcessorValidator.validateDataProcessors(any())).thenReturn(emptyList());
        workflowItemGroupDependencyValidator = mock(WorkflowItemGroupDependencyValidator.class);
        when(workflowItemGroupDependencyValidator.validateItemGroupDependencies(any())).thenReturn(emptyList());

        validator = new WorkflowValidator(new ObjectMapper(), workflowDataProcessorValidator, workflowItemGroupDependencyValidator, 100);

    }

    @Test
    public void shouldFailWhenNodeDoesNotHaveValidProcessorId() {
        String node0Name = "Fake processor";
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, node0Name, "FakeProcessor"),
                node(1L, "Report")
            ),
            ImmutableList.of(
                link(0L, 0L, EVENTS, 1L, EVENTS)
            )
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS)
            .stream()
            .filter(v -> v.getMessage().contains("FakeProcessor"))
            .collect(Collectors.toList());

        assertThat(result).hasSize(1);

        assertThat(result).hasOnlyOneElementSatisfying(violation ->
            assertThat(violation)
                .hasFieldOrPropertyWithValue("nodeId", 0L)
                .hasFieldOrPropertyWithValue("message", "Processor FakeProcessor could not be found.")
        );
    }

    @Test
    public void shouldFailWhenNodeDoesNotHaveEnoughInputChannel() {
        String node0Name = "Subject importer";
        String node1Name = "Filter";

        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, node0Name, "SubjectImporter"),
                node(1L, node1Name, "Filter"),
                node(2L, "Report")
            ),
            ImmutableList.of(
                link(0L, 0L, EVENTS, 1L, EVENTS),
                link(1L, 1L, EVENTS, 2L, EVENTS)
            )
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).containsExactlyInAnyOrder(
            ViolationDTO.error(0L, "Input link is missing: " + REFERENCE_DATA),
            ViolationDTO.error(1L, "Input link is missing: " + DATASET)
        );
    }

    @Test
    public void shouldPassWhenNodeDoesNotHaveEnoughInputChannel() {
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "SubjectImporter"),
                node(2L, "FilterWithOptionalInput"),
                node(3L, "Report")
            ),
            ImmutableList.of(
                link(0L, 0L, REFERENCE_DATA, 1L, REFERENCE_DATA),
                link(1L, 1L, EVENTS, 2L, EVENTS),
                link(2L, 2L, EVENTS, 3L, EVENTS)
            )
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).isEmpty();
    }

    @Test
    public void shouldFailIfProcessorConfigFormatIsUnsupported() {
        String node2Name = "Report node";
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "SubjectImporter"),
                node(2L, node2Name, "Report", prop("unsupportedFormat", "value"))
            ),
            ImmutableList.of(
                link(0L, 0L, REFERENCE_DATA, 1L, REFERENCE_DATA),
                link(1L, 1L, EVENTS, 2L, EVENTS)
            )
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).hasOnlyOneElementSatisfying(violation ->
            assertThat(violation)
                .hasFieldOrPropertyWithValue("nodeId", null)
                .hasFieldOrProperty("message")
        );
    }

    @Test
    public void shouldFailIfSourceNodeIdInLinkDoesNotMatchAnyNode() {
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "SubjectImporter"),
                node(2L, "Report")
            ),
            ImmutableList.of(
                link(0L, 0L, REFERENCE_DATA, 1L, REFERENCE_DATA),
                link(1L, 1000L, EVENTS, 2L, EVENTS)
            )
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).hasOnlyOneElementSatisfying(violation ->
            assertThat(violation)
                .hasFieldOrPropertyWithValue("nodeId", 2L)
                .hasFieldOrPropertyWithValue("message", "Dangling link: source or sink node is missing.")
        );
    }

    @Test
    public void shouldFailIfSinkNodeIdInLinkDoesNotMatchAnyNode() {
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "SubjectImporter"),
                node(2L, "Report")
            ),
            ImmutableList.of(
                link(0L, 0L, REFERENCE_DATA, 1L, REFERENCE_DATA),
                link(0L, 0L, REFERENCE_DATA, 1000L, REFERENCE_DATA),
                link(1L, 1L, EVENTS, 2L, EVENTS)
            )
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).hasOnlyOneElementSatisfying(violation ->
            assertThat(violation)
                .hasFieldOrPropertyWithValue("nodeId", 1000L)
                .hasFieldOrPropertyWithValue("message", "Dangling link: source or sink node is missing.")
        );
    }

    @Test
    public void shouldFailOnSelfTargetingLink() {
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "SubjectImporter"),
                node(2L, "Report")
            ),
            ImmutableList.of(
                link(0L, 1L, REFERENCE_DATA, 1L, REFERENCE_DATA),
                link(2L, 1L, EVENTS, 2L, EVENTS)
            )
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).hasOnlyOneElementSatisfying(violation ->
            assertThat(violation)
                .hasFieldOrPropertyWithValue("nodeId", 1L)
                .hasFieldOrPropertyWithValue("message", "Self targeting link: source node and sink node are the same.")
        );
    }

    @Test
    public void shouldFailIfSourceChannelInLinkIsNotSupported() {
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "SubjectImporter"),
                node(2L, "Report")
            ),
            ImmutableList.of(
                link(0L, 0L, "unsupportedChannel", 1L, REFERENCE_DATA),
                link(1L, 1L, EVENTS, 2L, EVENTS)
            )
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).hasOnlyOneElementSatisfying(violation ->
            assertThat(violation)
                .hasFieldOrPropertyWithValue("nodeId", 1L)
                .hasFieldOrPropertyWithValue("message", "Source node does not provide channel: unsupportedChannel")
        );
    }

    @Test
    public void shouldFailIfSinkChannelInLinkIsNotSupported() {
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "SubjectImporter"),
                node(2L, "Report")
            ),
            ImmutableList.of(
                link(0L, 0L, REFERENCE_DATA, 1L, REFERENCE_DATA),
                link(1L, 1L, EVENTS, 2L, EVENTS),
                link(2L, 1L, EVENTS, 2L, "unsupportedChannel")
            )
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).hasOnlyOneElementSatisfying(violation ->
            assertThat(violation)
                .hasFieldOrPropertyWithValue("nodeId", 2L)
                .hasFieldOrPropertyWithValue("message", "Sink node does not accept channel: unsupportedChannel")
        );
    }

    @Test
    public void shouldFailIfSourceChannelAndSinkChannelHasDifferentDataType() {
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "Report")
            ),
            ImmutableList.of(
                link(0L, 0L, REFERENCE_DATA, 1L, EVENTS)
            )
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).hasOnlyOneElementSatisfying(violation ->
            assertThat(violation)
                .hasFieldOrPropertyWithValue("nodeId", 1L)
                .hasFieldOrPropertyWithValue("message", "Source and sink channel is of different data type.")
        );
    }

    @Test
    public void shouldFailIfDependentProcessorsDoesNotGenerateRequiredSubjectTags() {
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "SubjectImporter"),
                node(2L, "ReportThatRequiresSubjectTag")
            ),
            ImmutableList.of(
                link(0L, 0L, REFERENCE_DATA, 1L, REFERENCE_DATA),
                link(1L, 1L, EVENTS, 2L, EVENTS)
            )
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).hasOnlyOneElementSatisfying(voilation ->
            assertThat(voilation)
                .hasFieldOrPropertyWithValue("nodeId", 2L)
                .hasFieldOrPropertyWithValue("message", "Linked processors do not generate required subject tags. Tags missing and the processors generating them are:\n" +
                    "- subjectTagA from (ProcessorThatOutputSubjectTag) node")
        );
    }

    @Test
    public void shouldFailIfDependentProcessorsDoesNotGenerateRequiredEventTags() {
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "SubjectImporter"),
                node(2L, "ReportThatRequiresEventTag")
            ),
            ImmutableList.of(
                link(0L, 0L, REFERENCE_DATA, 1L, REFERENCE_DATA),
                link(1L, 1L, EVENTS, 2L, EVENTS)
            )
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).hasOnlyOneElementSatisfying(voilation ->
            assertThat(voilation)
                .hasFieldOrPropertyWithValue("nodeId", 2L)
                .hasFieldOrPropertyWithValue("message", "Linked processors do not generate required event tags. Tags missing and the processors generating them are:\n" +
                    "- eventTagA from (Algorithm or ProcessorThatOutputEventTag) node")
        );
    }

    @Test
    public void shouldFailIfDependentProcessorsDoesNotGenerateRequiredSubjectAndEventTags() {
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "SubjectImporter"),
                node(2L, "ReportThatRequiresEventTagAndSubjectTag")
            ),
            ImmutableList.of(
                link(0L, 0L, REFERENCE_DATA, 1L, REFERENCE_DATA),
                link(1L, 1L, EVENTS, 2L, EVENTS)
            )
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).containsExactlyInAnyOrder(
            ViolationDTO.error(2L, "Linked processors do not generate required subject tags. Tags missing and the processors generating them are:\n" +
                "- subjectTagA from (ProcessorThatOutputSubjectTag) node"),
            ViolationDTO.error(2L, "Linked processors do not generate required event tags. Tags missing and the processors generating them are:\n" +
                "- eventTagA from (Algorithm or ProcessorThatOutputEventTag) node\n" +
                "- eventTagB from (Algorithm) node")
        );
    }

    @Test
    public void shouldFailIfThereAreNoProcessorsThatGenerateRequiredTag() {
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "SubjectImporter"),
                node(2L, "ReportThatRequiresUnknownEventTag")
            ),
            ImmutableList.of(
                link(0L, 0L, REFERENCE_DATA, 1L, REFERENCE_DATA),
                link(1L, 1L, EVENTS, 2L, EVENTS)
            )
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).containsExactlyInAnyOrder(
            ViolationDTO.error(2L, "Linked processors do not generate required subject tags. Tags missing and the processors generating them are:\n" +
                "- subjectTagA from (ProcessorThatOutputSubjectTag) node"),
            ViolationDTO.error(2L, "Linked processors do not generate required event tags. Tags missing and the processors generating them are:\n" +
                "- eventTagA from (Algorithm or ProcessorThatOutputEventTag) node\n" +
                "- eventTagZ from (-) node")
        );
    }

    @Test
    public void shouldFailIfDependentProcessorsGenerateIncompleteTags() {
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "SubjectImporter"),
                node(2L, "ProcessorThatOutputSubjectTag"),
                node(3L, "ProcessorThatOutputEventTag"),
                node(4L, "ReportThatRequiresEventTagAndSubjectTag")
            ),
            ImmutableList.of(
                link(0L, 0L, REFERENCE_DATA, 1L, REFERENCE_DATA),
                link(1L, 1L, EVENTS, 2L, EVENTS),
                link(2L, 2L, EVENTS, 3L, EVENTS),
                link(3L, 3L, EVENTS, 4L, EVENTS)
            )
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).hasOnlyOneElementSatisfying(voilation ->
            assertThat(voilation)
                .hasFieldOrPropertyWithValue("nodeId", 4L)
                .hasFieldOrPropertyWithValue("message", "Linked processors do not generate required event tags. Tags missing and the processors generating them are:\n" +
                    "- eventTagB from (Algorithm) node")
        );
    }

    @Test
    public void shouldFailIfThereAreNoPredecessorProcessors() {
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(3L, "ReportThatRequiresSubjectTag")
            ),
            emptyList()
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).containsExactlyInAnyOrder(
            ViolationDTO.error(3L, "Input link is missing: " + EVENTS),
            ViolationDTO.error(3L, "Linked processors do not generate required subject tags. Tags missing and the processors generating them are:\n" +
                "- subjectTagA from (ProcessorThatOutputSubjectTag) node")
        );
    }

    @Test
    public void shouldPassfDependentProcessorsGenerateRequiredEventTags() {
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "SubjectImporter"),
                node(2L, "ProcessorThatOutputEventTag"),
                node(3L, "ReportThatRequiresEventTag")
            ),
            ImmutableList.of(
                link(0L, 0L, REFERENCE_DATA, 1L, REFERENCE_DATA),
                link(1L, 1L, EVENTS, 2L, EVENTS),
                link(2L, 2L, EVENTS, 3L, EVENTS)
            )
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).isEmpty();
    }

    @Test
    public void shouldPassIfDependentProcessorsGenerateRequiredSubjectTags() {
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "SubjectImporter"),
                node(2L, "ProcessorThatOutputSubjectTag"),
                node(3L, "ReportThatRequiresSubjectTag")
            ),
            ImmutableList.of(
                link(0L, 0L, REFERENCE_DATA, 1L, REFERENCE_DATA),
                link(1L, 1L, EVENTS, 2L, EVENTS),
                link(3L, 2L, EVENTS, 3L, EVENTS)
            )
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).isEmpty();
    }

    @Test
    public void shouldFailValidation_ifUser_addsProperties_incorrectly() {
        WorkflowDTO someValidWorkflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "SubjectImporter"),
                node(2L, "ProcessorThatOutputSubjectTag"),
                node(3L, "ReportThatRequiresSubjectTag")
            ),
            ImmutableList.of(
                link(0L, 0L, REFERENCE_DATA, 1L, REFERENCE_DATA),
                link(1L, 1L, EVENTS, 2L, EVENTS),
                link(3L, 2L, EVENTS, 3L, EVENTS)
            )
        );

        final HashMap<String, String> props = new HashMap<>() {{
            put("instanceTypes", "foo");
            put("instanceType", "bar");
            put("subnet", "foo");
            put("subnets", "bar");
            put("USE_EMR_CLUSTER", "bar");
        }};

        final WorkflowConfigDTO config = new WorkflowConfigDTO();
        config.setProperties(props);
        someValidWorkflow.setConfig(config);
        List<ViolationDTO> result = validator.validate(someValidWorkflow, PROCESSORS);

        assertThat(result).containsExactlyInAnyOrder(
            ViolationDTO.error(null, "Workflow properties should not contain both 'instanceType' and 'instanceTypes'. Set only on of these workflow properties.")
        );
    }

    @Test
    public void shouldWarn_ifEmrDebugWorkflowPropertyIsSet() {
        WorkflowDTO someValidWorkflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "SubjectImporter"),
                node(2L, "ProcessorThatOutputSubjectTag"),
                node(3L, "ReportThatRequiresSubjectTag")
            ),
            ImmutableList.of(
                link(0L, 0L, REFERENCE_DATA, 1L, REFERENCE_DATA),
                link(1L, 1L, EVENTS, 2L, EVENTS),
                link(3L, 2L, EVENTS, 3L, EVENTS)
            )
        );

        final HashMap<String, String> props = new HashMap<String, String>() {{
            put("extraApplications", "false");
            put("keepJobFlowAliveWhenNoSteps", "true");
            put("abc", "true");
        }};

        final WorkflowConfigDTO config = new WorkflowConfigDTO();
        config.setProperties(props);
        someValidWorkflow.setConfig(config);

        //test
        List<ViolationDTO> result = validator.validate(someValidWorkflow, PROCESSORS);
        assertThat(result).hasSize(2);
        List<String> emrDebugProperties = result.stream()
            .map(ViolationDTO::getDebugWorkflowProperty)
            .collect(Collectors.toList());
        assertThat(emrDebugProperties).hasSameElementsAs(Arrays.asList("extraApplications", "keepJobFlowAliveWhenNoSteps"));
    }

    @Test
    public void shouldNotWarn_ifNoEmrDebugWorkflowPropertyIsSet() {
        WorkflowDTO someValidWorkflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "SubjectImporter"),
                node(2L, "ProcessorThatOutputSubjectTag"),
                node(3L, "ReportThatRequiresSubjectTag")
            ),
            ImmutableList.of(
                link(0L, 0L, REFERENCE_DATA, 1L, REFERENCE_DATA),
                link(1L, 1L, EVENTS, 2L, EVENTS),
                link(3L, 2L, EVENTS, 3L, EVENTS)
            )
        );

        final HashMap<String, String> props = new HashMap<String, String>() {{
            put("abc", "true");
        }};

        final WorkflowConfigDTO config = new WorkflowConfigDTO();
        config.setProperties(props);
        someValidWorkflow.setConfig(config);

        //test
        List<ViolationDTO> result = validator.validate(someValidWorkflow, PROCESSORS);
        assertThat(result).isEmpty();
    }

    @Test
    public void shouldPassIfDependentProcessorsGenerateRequiredTags() {
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter"),
                node(1L, "SubjectImporter"),
                node(2L, "ProcessorThatOutputSubjectTag"),
                node(3L, "ProcessorThatOutputEventTag"),
                node(4L, "Algorithm"),
                node(5L, "ReportThatRequiresEventTagAndSubjectTag")
            ),
            ImmutableList.of(
                link(0L, 0L, REFERENCE_DATA, 1L, REFERENCE_DATA),
                link(1L, 1L, EVENTS, 2L, EVENTS),
                link(2L, 2L, EVENTS, 3L, EVENTS),
                link(3L, 3L, EVENTS, 4L, EVENTS),
                link(4L, 4L, EVENTS, 5L, EVENTS)
            )
        );

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).isEmpty();
    }

    @Test
    public void shouldAlsoValidateDataProcessors() {
        WorkflowDTO workflow = workflow(
            ImmutableList.of(
                node(0L, "ReferenceDataImporter")
            ),
            emptyList()
        );

        validator.validate(workflow, PROCESSORS);

        verify(workflowDataProcessorValidator).validateDataProcessors(workflow);
    }

    @Test
    public void shouldFailIfNumInstancesAboveLimit() {
        WorkflowDTO workflow = workflow(
            emptyList(),
            emptyList()
        );

        final HashMap<String, String> props = new HashMap<String, String>() {{
            put("numInstances", "3000");
        }};
        final WorkflowConfigDTO config = new WorkflowConfigDTO();
        config.setProperties(props);
        workflow.setConfig(config);

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).contains(
            ViolationDTO.error(null, "Number of instances is set too high: 3000. Such high number may incur significant cost. Please set number of instances lower than 100.")
        );
    }

    @Test
    public void shouldPassIfNumInstancesBelowLimit() {
        WorkflowDTO workflow = workflow(
            emptyList(),
            emptyList()
        );

        final HashMap<String, String> props = new HashMap<String, String>() {{
            put("numInstances", "10");
        }};
        final WorkflowConfigDTO config = new WorkflowConfigDTO();
        config.setProperties(props);
        workflow.setConfig(config);

        List<ViolationDTO> result = validator.validate(workflow, PROCESSORS);

        assertThat(result).doesNotContain(
            ViolationDTO.error(null, "Number of instances is set too high: 10. Such high number may incur significant cost. Please set a lower number of instances.")
        );
    }

}
