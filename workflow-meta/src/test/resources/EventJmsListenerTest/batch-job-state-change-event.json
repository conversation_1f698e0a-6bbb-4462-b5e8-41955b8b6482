{"version": "0", "id": "e30d178d-13c4-4db5-8ca0-5d7047b09803", "detail-type": "Batch Job State Change", "source": "aws.batch", "account": "************", "time": "2021-02-08T01:18:02Z", "region": "ap-southeast-2", "resources": ["arn:aws:batch:ap-southeast-2:************:job/2f23c0c9-2524-4b35-a591-1b1f69dd07c6"], "detail": {"jobArn": "arn:aws:batch:ap-southeast-2:************:job/2f23c0c9-2524-4b35-a591-1b1f69dd07c6", "jobName": "report-indexer_workflow_12345_node_6", "jobId": "2f23c0c9-2524-4b35-a591-1b1f69dd07c6", "jobQueue": "arn:aws:batch:ap-southeast-2:************:job-queue/pd-au-int-warehouse-report-indexer-workers-queue", "status": "SUCCEEDED", "attempts": [{"container": {"containerInstanceArn": "arn:aws:ecs:ap-southeast-2:************:container-instance/pd-au-int-warehouse-report-indexer-compute-env_Batch_611bbff3-f1a9-39dd-b1ec-5cb62bb477f4/7e928dc87e16494ab613df7c6386590a", "taskArn": "arn:aws:ecs:ap-southeast-2:************:task/pd-au-int-warehouse-report-indexer-compute-env_Batch_611bbff3-f1a9-39dd-b1ec-5cb62bb477f4/663a557e37d348c1a0372647d31f6b31", "exitCode": 0, "logStreamName": "pd-au-int-warehouse-report-indexer-job/default/663a557e37d348c1a0372647d31f6b31", "networkInterfaces": []}, "startedAt": 1612747040014, "stoppedAt": 1612747081861, "statusReason": "Essential container in task exited"}], "statusReason": "Essential container in task exited", "createdAt": 1612746929168, "retryStrategy": {"attempts": 3, "evaluateOnExit": []}, "startedAt": 1612747040014, "stoppedAt": 1612747081861, "dependsOn": [], "jobDefinition": "arn:aws:batch:ap-southeast-2:************:job-definition/pd-au-int-warehouse-report-indexer-job:8", "parameters": {"nodeId": "nodeId=6", "workflowId": "workflowId=12345"}, "container": {"image": "448934725283.dkr.ecr.ap-southeast-2.amazonaws.com/warehouse-report-indexer:43", "vcpus": 4, "memory": 40000, "command": ["workflowId=12345", "nodeId=6", "env=int-v2", "es.local.writers=2", "role=arn:aws:iam::************:role/service-role/pd-au-int-warehouse-report-indexer-es-snapshot-role"], "jobRoleArn": "arn:aws:iam::************:role/service-role/pd-au-int-warehouse-report-indexer-job-role", "volumes": [], "environment": [{"name": "WAREHOUSE_COMMON_BUCKET", "value": "pd-au-int-common"}, {"name": "ES_BUCKET", "value": "pd-au-int-elastic-search"}], "mountPoints": [], "readonlyRootFilesystem": false, "ulimits": [], "privileged": false, "user": "root", "exitCode": 0, "containerInstanceArn": "arn:aws:ecs:ap-southeast-2:************:container-instance/pd-au-int-warehouse-report-indexer-compute-env_Batch_611bbff3-f1a9-39dd-b1ec-5cb62bb477f4/7e928dc87e16494ab613df7c6386590a", "taskArn": "arn:aws:ecs:ap-southeast-2:************:task/pd-au-int-warehouse-report-indexer-compute-env_Batch_611bbff3-f1a9-39dd-b1ec-5cb62bb477f4/663a557e37d348c1a0372647d31f6b31", "logStreamName": "pd-au-int-warehouse-report-indexer-job/default/663a557e37d348c1a0372647d31f6b31", "networkInterfaces": [], "resourceRequirements": [], "secrets": []}, "timeout": {"attemptDurationSeconds": 3600}, "tags": {"resourceArn": "arn:aws:batch:ap-southeast-2:************:job/2f23c0c9-2524-4b35-a591-1b1f69dd07c6"}, "platformCapabilities": []}}