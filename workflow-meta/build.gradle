plugins {
    id 'org.springframework.boot' version '2.7.16'
    id "com.github.ManifestClasspath" version "0.1.0-RELEASE"
    id 'java'
    id "io.spring.dependency-management"
}

apply plugin: 'io.spring.dependency-management'

version = '0.0.1-SNAPSHOT'

repositories {
    maven {
        url "https://s3-us-west-2.amazonaws.com/dynamodb-local/release"
    }
}

// https://docs.spring.io/spring-boot/gradle-plugin/managing-dependencies.html#managing-dependencies.dependency-management-plugin.customizing
// https://docs.spring.io/spring-boot/appendix/dependency-versions/properties.html
ext['groovy.version'] = '3.0.22' // Override dependency management version
ext['httpclient5.version'] = '5.3.1' // for com.clickhouse:client-v2:0.7.1-patch1
ext['httpcore5.version'] = '5.2.1'   // for com.clickhouse:client-v2:0.7.1-patch1

ext {
    versions = [:]
}

versions += [
    spring_boot: "2.7.16",
    spring_framework: "5.3.37",
    spring_framework_data: "3.1.12"
]

bootRun {
    jvmArgs += jvmArgsList
}

dependencies {
    implementation project(':workflow')

    implementation("org.springframework.boot:spring-boot-starter-web:$versions.spring_boot") {
        exclude group: "org.springframework.boot", module: "spring-boot-starter-logging"
    }
    // This dependency is only required for sprint-boot-start-web 2.7 for fixing a high vulnerability
    // Please remove it if we upgrade to springboot 3
    implementation("org.yaml:snakeyaml:1.33")
    implementation group: 'org.springframework.boot', name: 'spring-boot-starter-tomcat', version: versions.spring_boot

    implementation group: 'org.springframework', name: 'spring-aspects', version: versions.spring_framework
    implementation group: 'org.springframework.retry', name: 'spring-retry', version: '1.2.5.RELEASE'

    if (!project.hasProperty('prod')) {
        implementation "org.springframework.boot:spring-boot-devtools"
    }

    implementation "com.amazonaws:aws-java-sdk-s3:$rootProject.versions.aws"
    implementation "com.amazonaws:aws-java-sdk-sqs:$rootProject.versions.aws"
    implementation "com.amazonaws:aws-java-sdk-sns:$rootProject.versions.aws"
    implementation "com.amazonaws:aws-java-sdk-lambda:$rootProject.versions.aws"
    implementation "com.amazonaws:amazon-sqs-java-messaging-lib:1.0.8"
    implementation "com.amazonaws:aws-java-sdk-ec2:$rootProject.versions.aws"

    implementation platform('software.amazon.awssdk:bom:2.29.37')
    implementation 'software.amazon.awssdk:dynamodb'
    implementation 'software.amazon.awssdk:dynamodb-enhanced'
    implementation 'software.amazon.awssdk:pricing'

    implementation("org.springframework.boot:spring-boot-starter-actuator") {
        exclude group: "org.elasticsearch.client"
        exclude group: "org.elasticsearch"
    }

    implementation("org.springframework.boot:spring-boot-autoconfigure") {
        exclude group: "org.elasticsearch.client"
        exclude group: "org.elasticsearch"
    }

    // check version of httpclient5 and httpcore5 dependencies of this library, which are managed by io.spring.dependency-management
    // then override in above ext block: ext['httpclient5.version'], ext['httpcore5.version']
    // when upgrading spring-boot or this library
    implementation("com.clickhouse:client-v2:0.7.1-patch1")

    implementation "org.elasticsearch:elasticsearch:$rootProject.versions.es"
    implementation "org.elasticsearch.client:elasticsearch-rest-client:$rootProject.versions.es"
    implementation "org.elasticsearch.client:elasticsearch-rest-high-level-client:$rootProject.versions.es"

    implementation "org.springframework:spring-jms:$versions.spring_framework"
    implementation "org.springframework:spring-beans:$versions.spring_framework"
    implementation "org.springframework:spring-aop:$versions.spring_framework"
    implementation "org.springframework:spring-core:$versions.spring_framework"
    implementation "org.springframework:spring-context:$versions.spring_framework"
    implementation "org.springframework.data:spring-data-commons:$versions.spring_framework_data"
    implementation "org.springframework.boot:spring-boot-starter-security"

    // Overwrite transitive log4j dependencies because previous versions have a major security issue.
    // See https://prospection.atlassian.net/wiki/spaces/SYS/pages/2227503134/Log4j+zero+day+vulnerability+CVE-2021-44228
    implementation "org.apache.logging.log4j:log4j-api:$rootProject.versions.log4j"
    implementation "org.apache.logging.log4j:log4j-core:$rootProject.versions.log4j"

    implementation "net.logstash.logback:logstash-logback-encoder:7.2"

    implementation "com.networknt:json-schema-validator:1.0.29"
    implementation "org.jgrapht:jgrapht-core:1.3.1"

    implementation "com.fasterxml.jackson.core:jackson-core:$rootProject.versions.jackson"
    implementation "com.fasterxml.jackson.core:jackson-annotations:$rootProject.versions.jackson"
    implementation "com.fasterxml.jackson.core:jackson-databind:$rootProject.versions.jackson"
    implementation "com.fasterxml.jackson.module:jackson-module-parameter-names:$rootProject.versions.jackson"
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-jdk8:$rootProject.versions.jackson"
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:$rootProject.versions.jackson"

    //Because we are using spark 2.4.6 which is dependence on jackson-module-scala-2.11:2.6.7.1.
    //So we need to implement with latest jackson version
    implementation "com.fasterxml.jackson.module:jackson-module-scala_$rootProject.versions.scala:$rootProject.versions.jackson"

    // unleash for feature toggle
    implementation("io.getunleash:unleash-client-java:7.0.0")

    implementation "org.projectlombok:lombok:1.18.24"
    annotationProcessor "org.projectlombok:lombok:1.18.24"

    implementation "org.javamoney:moneta:1.2.1"

    testImplementation "org.json:json:20201115"
    testImplementation "com.amazonaws:DynamoDBLocal:1.23.0"
    testImplementation "org.assertj:assertj-core"
    testImplementation "org.springframework.boot:spring-boot-test"
    testImplementation("org.springframework.boot:spring-boot-starter-test") {
        exclude group: 'com.vaadin.external.google', module: 'android-json'
        exclude group: "org.springframework.boot", module: "spring-boot-starter-logging"
    }

    testImplementation group: 'org.awaitility', name: 'awaitility', version: '3.0.0'

}

// Copy SQLite4Java native libraries to a path that can later be referenced
// by the 'sqlite4java.library.path' system property. This is required by
// DynamoDB local.
task copyNativeDeps(type: Copy) {
    from(configurations.compileClasspath + configurations.testRuntimeClasspath) {
        include '*.dll'
        include '*.dylib'
        include '*.so'
    }
    into 'build/libs'
}

build {
    dependsOn this.copyNativeDeps
}

test {
    dependsOn this.copyNativeDeps
}

// this task shows all available plugins and their classpath
// useful for debugging which plugins are used in this messy project
task showPluginClasspath {
    doLast {
        project.plugins.each {
            println it
        }
        buildscript.configurations.classpath.each { println it.name }
    }
}
