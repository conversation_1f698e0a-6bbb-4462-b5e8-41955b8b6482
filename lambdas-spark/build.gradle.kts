import org.gradle.api.tasks.testing.logging.TestLogEvent
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id("idea")
    kotlin("jvm")
    alias(libs.plugins.shadow)
}

java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
    }
}

configurations.all {
    // Exclude conflicting logging implementations to use Spring Boot's Logback
    exclude(group = "org.slf4j", module = "slf4j-log4j12")
    exclude(group = "org.slf4j", module = "slf4j-reload4j")
    // Exclude Log4j SLF4J implementations to avoid conflicts with Spring Boot's Logback
    exclude(group = "org.apache.logging.log4j", module = "log4j-slf4j2-impl")
    exclude(group = "org.apache.logging.log4j", module = "log4j-slf4j-impl")
    // Exclude log4j-to-slf4j as it conflicts with log4j-slf4j2-impl
    exclude(group = "org.apache.logging.log4j", module = "log4j-to-slf4j")

    resolutionStrategy {
        force(libs.scala.library)
        force(libs.slf4j.api)
        force(libs.jakatar.servlet.api)
        // Force AWS SDK v2 version
        force("software.amazon.awssdk:bom:${libs.versions.aws.sdk.v2.get()}")

        // Force all AWS SDK v2 components to use the same version
        eachDependency {
            if (requested.group == "software.amazon.awssdk") {
                useVersion(libs.versions.aws.sdk.v2.get())
                because("Force consistent AWS SDK v2 version to avoid NoSuchMethodError")
            }
        }

        // Force all Hadoop components to use the same version
        eachDependency {
            if (requested.group == "org.apache.hadoop") {
                useVersion(libs.versions.hadoop.get())
                because("Force consistent Hadoop version to avoid NoSuchMethodError")
            }
        }
    }
}

dependencies {
    implementation(project(":common"))
    implementation(project(":kotlin-spark"))

    implementation(libs.slf4j.api)

    implementation(libs.aws.lambda.core)
    implementation(libs.aws.lambda.runtime.client)
    implementation(libs.aws.sdk.s3)

    implementation(libs.spark.sql)

    implementation(libs.scala.library)
    implementation(libs.jackson.module.scala)
    implementation(libs.jackson.module.kotlin)
    implementation(libs.jackson.datatype.jsr310)

    testRuntimeOnly(libs.junit.platform.launcher)
    testImplementation(libs.junit.jupiter.api)
    testImplementation(libs.junit.jupiter.engine)
    testImplementation(libs.mockito.core)
    testImplementation(libs.mockito.kotlin)
}

tasks.shadowJar {
    isZip64 = true

    // Important step to reduce jar file by removing unused classes
    minimize()

    configurations = listOf(project.configurations.runtimeClasspath.get())

    // Remove files that add weight but are not used at runtime
    exclude(
        "assets/**",
        "webapps/**",
        "org/apache/spark/ui/static/**",
        "win*/**",
        "darwin/**",
    )

    // Keep only necessary Spring components
    exclude {
        // kotlin-spark module have spring-context dependencies which we just need the annotation at run time
        // hence this is to remove unwanted classes from spring
        it.path.startsWith("org/springframework/")
                && !it.path.startsWith("org/springframework/stereotype")
                && it.path != "org/springframework/beans/factory/annotation/Value.class"
    }

    // Print size information
    doLast {
        val file = File("${project.layout.buildDirectory.get()}/libs/${archiveFileName.get()}")
        val sizeInMB = file.length() / (1024 * 1024)
        println("Shadow JAR size: ${sizeInMB}MB (${file.length()} bytes)")
    }
}

tasks.withType<Test> {
    useJUnitPlatform()
    testLogging {
        events(TestLogEvent.FAILED)
    }

    // Add JVM arguments for Spark 4.0.0 compatibility with JDK 17
    jvmArgs(
        "--add-opens=java.base/java.lang=ALL-UNNAMED",
        "--add-opens=java.base/java.lang.invoke=ALL-UNNAMED",
        "--add-opens=java.base/java.lang.reflect=ALL-UNNAMED",
        "--add-opens=java.base/java.io=ALL-UNNAMED",
        "--add-opens=java.base/java.net=ALL-UNNAMED",
        "--add-opens=java.base/java.nio=ALL-UNNAMED",
        "--add-opens=java.base/java.util=ALL-UNNAMED",
        "--add-opens=java.base/java.util.concurrent=ALL-UNNAMED",
        "--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED",
        "--add-opens=java.base/sun.nio.ch=ALL-UNNAMED",
        "--add-opens=java.base/sun.nio.cs=ALL-UNNAMED",
        "--add-opens=java.base/sun.security.action=ALL-UNNAMED",
        "--add-opens=java.base/sun.util.calendar=ALL-UNNAMED",
        "--add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED"
    )
}

tasks.getByName<Jar>("jar") {
    enabled = false
}

tasks.withType<KotlinCompile> {
    compilerOptions {
        freeCompilerArgs.set(listOf("-Xjsr305=strict"))
        jvmTarget.set(JvmTarget.JVM_17)
    }
}
