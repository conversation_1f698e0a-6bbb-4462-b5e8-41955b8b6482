import org.gradle.api.tasks.testing.logging.TestLogEvent
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id("idea")
    id("scala")
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.plugins.serialization)
}

java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
    }
}

configurations.all {
    resolutionStrategy {
        force(libs.scala.library)
        force(libs.slf4j.api)
        // Spark 4.0.0 uses Jakarta Servlet API 5.0.0
        force(libs.jakatar.servlet.api)
        // Force AWS SDK v2 version
        force("software.amazon.awssdk:bom:${libs.versions.aws.sdk.v2.get()}")

        // Force all AWS SDK v2 components to use the same version
        eachDependency {
            if (requested.group == "software.amazon.awssdk") {
                useVersion(libs.versions.aws.sdk.v2.get())
                because("Force consistent AWS SDK v2 version to avoid NoSuchMethodError")
            }
        }

        // Force all Hadoop components to use the same version
        eachDependency {
            if (requested.group == "org.apache.hadoop") {
                useVersion(libs.versions.hadoop.get())
                because("Force consistent Hadoop version to avoid NoSuchMethodError")
            }
        }
    }
}

dependencies {
    implementation(project(":common"))
    implementation(project(":scala-spark"))

    implementation(platform(libs.kotlin.bom))
    implementation(libs.bundles.kotlin.core)

    implementation(libs.slf4j.api)

    implementation(libs.spring.context)

    implementation(libs.aws.sdk.s3)

    implementation(libs.bundles.spark.core)

    testRuntimeOnly(libs.junit.platform.launcher)
    testImplementation(libs.junit.jupiter.api)
    testImplementation(libs.junit.jupiter.engine)
    testImplementation(libs.mockito.core)
    testImplementation(libs.mockito.inline)
    testImplementation(libs.mockito.kotlin)
    testImplementation(libs.assertj.core)
}

tasks.withType<Test> {
    useJUnitPlatform()
    testLogging {
        events(TestLogEvent.FAILED)
    }

    // Add JVM arguments for Spark 4.0.0 compatibility with JDK 17
    jvmArgs(
        "--add-opens=java.base/java.lang=ALL-UNNAMED",
        "--add-opens=java.base/java.lang.invoke=ALL-UNNAMED",
        "--add-opens=java.base/java.lang.reflect=ALL-UNNAMED",
        "--add-opens=java.base/java.io=ALL-UNNAMED",
        "--add-opens=java.base/java.net=ALL-UNNAMED",
        "--add-opens=java.base/java.nio=ALL-UNNAMED",
        "--add-opens=java.base/java.util=ALL-UNNAMED",
        "--add-opens=java.base/java.util.concurrent=ALL-UNNAMED",
        "--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED",
        "--add-opens=java.base/sun.nio.ch=ALL-UNNAMED",
        "--add-opens=java.base/sun.nio.cs=ALL-UNNAMED",
        "--add-opens=java.base/sun.security.action=ALL-UNNAMED",
        "--add-opens=java.base/sun.util.calendar=ALL-UNNAMED",
        "--add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED"
    )
}

tasks.withType<KotlinCompile> {
    compilerOptions {
        jvmTarget.set(JvmTarget.JVM_17)
        freeCompilerArgs.addAll("-Xjsr305=strict")
    }
}
