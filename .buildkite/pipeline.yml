env:
  PROJECT_NAME:      "workflow-meta"
  IMAGE_REPO:        "448934725283.dkr.ecr.ap-southeast-2.amazonaws.com"
  IMAGE:             "${IMAGE_REPO}/${PROJECT_NAME}"
  TAG_VERSION:       "${BUILDKITE_BUILD_NUMBER}"
  TAG_LATEST:        "${BUILDKITE_BRANCH}_latest"
  TAG_BUILDER:       "${BUILDKITE_BRANCH}_builder"
  DATADOG_AGENT_URL: "${DATADOG_AGENT_URL}"
  DOCKER_OPTS:       "--net=host --userns=host"

anchors:
  common_build: &common_build
    commit: "${BUILDKITE_COMMIT}"
    branch: "${BUILDKITE_BRANCH}"
  common_env: &common_env
    BUILDKITE_PULL_REQUEST: "${BUILDKITE_PULL_REQUEST}"
    BUILDKITE_PULL_REQUEST_BASE_BRANCH: "${BUILDKITE_PULL_REQUEST_BASE_BRANCH}"
    BUILDKITE_PULL_REQUEST_REPO: "${BUILDKITE_PULL_REQUEST_REPO}"
    PROJECT_NAME: "${PROJECT_NAME}"
    IMAGE_REPO: "${IMAGE_REPO}"
    IMAGE: "${IMAGE}"
    TAG_VERSION: "${TAG_VERSION}"
    TAG_LATEST: "${TAG_LATEST}"
    TAG_BUILDER: "${TAG_BUILDER}"
    DOCKER_OPTS: "${DOCKER_OPTS}"

steps:

  #############################
  ##########  SETUP  ##########
  #############################

  - label: ":docker: :ecr: Setup Docker Registry"
    commands:
      - "<NAME_EMAIL>:teamprospection/master-pipeline.git"
      - "cd ./master-pipeline/.buildkite/scripts && make -s setup_registry"
    agents:
        queue: "pd-16cpu"

  - wait

  #############################
  ##########  BUILD  ##########
  #############################

  - label: ":docker: :gradle: Docker Build"
    commands:
      - "make -s pull"
      - "make -s build"
      - "make -s push"
    agents:
        queue: "pd-16cpu"

  - wait

  - label: ":git: git tag"
    branches:
      - "master"
      - "hotfix/*"
    commands:
      - "make -s git_tag"

  - wait

  #############################
  ##########  TEST   ##########
  #############################

  - label: ":junit: :sonarcloud: Unit Test"
    commands:
      - "make -s pull"
      - "make app_test BRANCH=${BUILDKITE_BRANCH} PROVIDER=${BUILDKITE_PIPELINE_PROVIDER} PULLREQUEST_KEY=${BUILDKITE_PULL_REQUEST} PULLREQUEST_BASE=${BUILDKITE_PULL_REQUEST_BASE_BRANCH}"
    plugins:
      - test-collector#v1.0.0:
          files: "*/build/test-results/test/TEST-*.xml"
          format: "junit"
          api-token-env-name: "WAREHOUSE_BK_TEST_ANALYTICS_TOKEN"
    artifact_paths:
        - "workflow-meta/build/reports/**/*"
    agents:
        queue: "pd-16cpu"

  - wait: ~
    if: build.branch == "master"

  #############################
  ########## NONPROD ##########
  #############################

  - trigger: "${BUILDKITE_PIPELINE_SLUG}-nonprod"
    label: ":buildkite: :pipeline: Deploy to Non-Production AU"
    async: true
    build:
      <<: *common_build
      message: "${BUILDKITE_MESSAGE} (au)"
      env:
        <<: *common_env
        COUNTRY: "au"

  - trigger: "${BUILDKITE_PIPELINE_SLUG}-nonprod"
    label: ":buildkite: :pipeline: Deploy to Non-Production US"
    async: true
    build:
      <<: *common_build
      message: "${BUILDKITE_MESSAGE} (us)"
      env:
        <<: *common_env
        COUNTRY: "us"

  - trigger: "${BUILDKITE_PIPELINE_SLUG}-nonprod"
    label: ":buildkite: :pipeline: Deploy to Non-Production JP"
    async: true
    build:
      <<: *common_build
      message: "${BUILDKITE_MESSAGE} (jp)"
      env:
        <<: *common_env
        COUNTRY: "jp"

  ######################################
  ########## UAT / Production ##########
  ######################################

  - trigger: "${BUILDKITE_PIPELINE_SLUG}-deploy"
    label: ":buildkite: :pipeline: Deploy to UAT/Production AU"
    branches:
      - "master"
      - "hotfix/*"
    async: true
    build:
      <<: *common_build
      message: "${BUILDKITE_MESSAGE} (au)"
      env:
        <<: *common_env
        COUNTRY: "au"

  - trigger: "${BUILDKITE_PIPELINE_SLUG}-deploy"
    label: ":buildkite: :pipeline: Deploy to UAT/Production US"
    branches:
      - "master"
      - "hotfix/*"
    async: true
    build:
      <<: *common_build
      message: "${BUILDKITE_MESSAGE} (us)"
      env:
        <<: *common_env
        COUNTRY: "us"

  - trigger: "${BUILDKITE_PIPELINE_SLUG}-deploy"
    label: ":buildkite: :pipeline: Deploy to UAT/Production JP"
    branches:
      - "master"
      - "hotfix/*"
    async: true
    build:
      <<: *common_build
      message: "${BUILDKITE_MESSAGE} (jp)"
      env:
        <<: *common_env
        COUNTRY: "jp"
