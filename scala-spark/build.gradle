import org.gradle.api.tasks.testing.logging.TestLogEvent

plugins {
    id("java-library")
    id("scala")
}

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}

// Add configurations to be consistent with other modules
configurations.configureEach {
    resolutionStrategy {
        force(libs.scala.library)
        force(libs.slf4j.api)
    }
}

dependencies {
    implementation(libs.spark.sql)
    implementation(libs.spark.excel)
    implementation(libs.scala.logging)

    testImplementation(libs.bundles.scala.test)
    testRuntimeOnly(libs.junit.platform.launcher)
}

test {
    useJUnitPlatform()
    testLogging {
        events(TestLogEvent.FAILED)
    }

    // Add the same JVM arguments for Spark 4.0.0 compatibility with JDK 17 as in other modules
    jvmArgs(
            "--add-opens=java.base/java.lang=ALL-UNNAMED",
            "--add-opens=java.base/java.lang.invoke=ALL-UNNAMED",
            "--add-opens=java.base/java.lang.reflect=ALL-UNNAMED",
            "--add-opens=java.base/java.io=ALL-UNNAMED",
            "--add-opens=java.base/java.net=ALL-UNNAMED",
            "--add-opens=java.base/java.nio=ALL-UNNAMED",
            "--add-opens=java.base/java.util=ALL-UNNAMED",
            "--add-opens=java.base/java.util.concurrent=ALL-UNNAMED",
            "--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED",
            "--add-opens=java.base/sun.nio.ch=ALL-UNNAMED",
            "--add-opens=java.base/sun.nio.cs=ALL-UNNAMED",
            "--add-opens=java.base/sun.security.action=ALL-UNNAMED",
            "--add-opens=java.base/sun.util.calendar=ALL-UNNAMED",
            "--add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED"
    )
}
