-- date restricted view which should match technical model (TM will only have events between start of data and end of data dates)
with events as (select *
                from pus_events
                where date_parse(evstdate, '%d/%m/%Y') >= date '2006-01-01'
                  and date_parse(evstdate, '%d/%m/%Y') <= date '2022-12-31'
                  and new_mast_enc_nhi is not null
                  and new_mast_enc_nhi <> ''

)
    ,
     duplicate_events_pus as (select event_id
                              FROM "pus_events"
                              group by event_id
                              having count(*) > 1)

SELECT 'Gastro', 'PUS Events', 'Min Date', min(date_parse(evstdate, '%d/%m/%Y')), null
FROM "events"
union
SELECT 'Gastro', 'PUS Events', 'Max Date', max(date_parse(evstdate, '%d/%m/%Y')), null
FROM "events"
union
SELECT 'Gastro', 'PUS Events', 'Valid Row Count', null, count(*)
FROM "events"
union
SELECT 'Gastro', 'PUS Events', 'Unique event ids', null, count(distinct event_id)
FROM "events"
union
SELECT 'Gastro', 'PUS Events', 'Count Subjects', null, count(distinct "new_mast_enc_nhi")
FROM "events"

-- unrestricted raw checks against PUS table
union
select 'Gastro', 'PUS Events', 'Full Row Count', null, count(*)
from pus_events
union
select 'Gastro', 'PUS Events', 'Missing subject id', null, count(*)
from pus_events
where new_mast_enc_nhi is null
   or new_mast_enc_nhi = ''
union
select 'Gastro', 'PUS Events', 'Missing date', null, count(*)
from pus_events
where evstdate is null
   or evstdate = ''
union
select 'Gastro', 'PUS Events', 'Missing code', null, count(*)
from pus_events
where event_type is null or event_type=''
union
select 'Gastro', 'PUS Events', 'Duplicate Event Ids', null, count(*)
from duplicate_events_pus
order by 1, 2, 3;
