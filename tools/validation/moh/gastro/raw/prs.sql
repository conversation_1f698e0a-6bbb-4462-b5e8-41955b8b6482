-- date restricted view which should match technical model (TM will only have events between start of data and end of data dates)
with events as (select distinct new_enc_nhi         	      ,
                                new_mast_enc_nhi    	      ,
                                event_id            	      ,
                                gender              	      ,
                                age_at_admission    	      ,
                                dom_cd              	      ,
                                dhb_dom             	      ,
                                event_type          	      ,
                                end_type            	      ,
                                event_start_datetime	      ,
                                event_end_datetime  	      ,
                                evntlvd             	      ,
                                local_id            	      ,
                                adm_src             	      ,
                                adm_type            	      ,
                                nz_res              	      ,
                                agency_type         	      ,
                                fac_type            	      ,
                                hlthspec            	      ,
                                los                 	      ,
                                total_niv_hours     	      ,
                                total_icu_hours     	      ,
                                hours_on_ventilation	      ,
                                accident_flag       	      ,
                                pccl                	      ,
                                ccl                 	      ,
                                date_psychiatric_leave_ends	  ,
                                psychiatric_leave_end_type	  ,
                                evstdate            	      ,
                                evendate            	      ,
                                year_month_birth    	      ,
                                short_stay_ed_flag  	      ,
                                dep13               	      ,
                                dep18
                from prs_events
                where date_parse(evstdate, '%d/%m/%Y') >= date '2006-01-01'
                  and date_parse(evstdate, '%d/%m/%Y') <= date '2022-12-31'
                  and new_mast_enc_nhi is not null
                  and new_mast_enc_nhi <> ''

)
    ,
     duplicate_events_prs as (select event_id
                              FROM "prs_events"
                              group by event_id
                              having count(*) > 1)

SELECT 'Gastro', 'PRS Events', 'Min Date', min(date_parse(evstdate, '%d/%m/%Y')), null
FROM "events"
union
SELECT 'Gastro', 'PRS Events', 'Max Date', max(date_parse(evstdate, '%d/%m/%Y')), null
FROM "events"
union
SELECT 'Gastro', 'PRS Events', 'Valid Row Count', null, count(*)
FROM "events"
union
SELECT 'Gastro', 'PRS Events', 'Unique event ids', null, count(distinct event_id)
FROM "events"
union
SELECT 'Gastro', 'PRS Events', 'Count Subjects', null, count(distinct "new_mast_enc_nhi")
FROM "events"

-- unrestricted raw checks against PRS table
union
select 'Gastro', 'PRS Events', 'Full Row Count', null, count(*)
from prs_events
union
select 'Gastro', 'PRS Events', 'Missing subject id', null, count(*)
from prs_events
where new_mast_enc_nhi is null
   or new_mast_enc_nhi = ''
union
select 'Gastro', 'PRS Events', 'Missing date', null, count(*)
from prs_events
where evstdate is null
   or evstdate = ''
union
select 'Gastro', 'PRS Events', 'Missing code', null, count(*)
from prs_events
where event_type is null or event_type=''
union
select 'Gastro', 'PRS Events', 'Duplicate Event Ids', null, count(*)
from duplicate_events_prs
order by 1, 2, 3;
