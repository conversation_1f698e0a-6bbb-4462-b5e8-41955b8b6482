with dates as
         (select 'phh', min(date_parse(date_dispensed, '%d/%m/%Y')) as "min date",
                 max(date_parse(date_dispensed, '%d/%m/%Y')) as "max date"
          from phh
          union
          select 'prs_events', min(date_parse(evstdate, '%d/%m/%Y')) as "min date",
                 max(date_parse(evstdate, '%d/%m/%Y')) as "max date"
          from prs_events
          union
          select 'pus_events', min(date_parse(evstdate, '%d/%m/%Y')) as "min date",
                 max(date_parse(evstdate, '%d/%m/%Y')) as "max date"
          from pus_events
          -- diags don't have date - derived from hospital events
--           union
--           select min(date_parse(evstdate, '%d/%m/%Y')) as "min date",
--                  max(date_parse(evstdate, '%d/%m/%Y')) as "max date"
--           from prs_diags
--           union
--           select min(date_parse(evstdate, '%d/%m/%Y')) as "min date",
--                  max(date_parse(evstdate, '%d/%m/%Y')) as "max date"
--           from pus_diags
          union
          select 'mos_coded', min(date_parse(dod, '%d/%m/%Y')) as "min date", max(date_parse(dod, '%d/%m/%Y')) as "max date"
          from mos_coded
          union
          select 'mos_uncoded', min(date_parse(dod, '%d/%m/%Y')) as "min date", max(date_parse(dod, '%d/%m/%Y')) as "max date"
          from mos_uncoded
          union
          select 'nap', min(date_parse(substr(datetime_of_service, 1, 9), '%d%b%Y')) as "min date",
                 max(date_parse(substr(datetime_of_service, 1, 9), '%d%b%Y')) as "max date"
          from nap)
select *
from dates;
