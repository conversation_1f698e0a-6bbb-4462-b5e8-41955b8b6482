-- date formatting https://prestodb.io/docs/current/functions/datetime.html

select 'phh' as "table", 'event before birth' as "check", count(*) as "count" from phh where date_parse(date_dispensed, '%d/%m/%Y') < date_parse(year_month_birth, '%b-%y') and date_dispensed<>'' and year_month_birth<>''
union
select 'prs_events' as "table", 'event before birth' as "check", count(*) as "count" from prs_events where date_parse(evstdate, '%d/%m/%Y') < date_parse(year_month_birth, '%b-%y') and evstdate<>'' and year_month_birth<>''
union
select 'pus_events' as "table", 'event before birth' as "check", count(*) as "count" from pus_events where date_parse(evstdate, '%d/%m/%Y') < date_parse(year_month_birth, '%b-%y') and evstdate<>'' and year_month_birth<>''
union
select 'mos_coded' as "table", 'event before birth' as "check", count(*) as "count" from mos_coded where date_parse(dod, '%d/%m/%Y') < date_parse(year_month_birth, '%b-%y') and dod<>'' and year_month_birth<>''
union
select 'mos_uncoded' as "table", 'event before birth' as "check", count(*) as "count" from mos_uncoded where date_parse(dod, '%d/%m/%Y') < date_parse(year_month_birth, '%b-%y') and dod<>'' and year_month_birth<>''
union
select 'nap' as "table", 'event before birth' as "check", count(*) as "count" from nap where date_parse(substr(datetime_of_service, 1, 9), '%d%b%Y') < date_parse(year_month_birth, '%b-%y') and datetime_of_service<>'' and year_month_birth<>''

order by 1,2;
