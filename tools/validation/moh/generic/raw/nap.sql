with events as (select distinct new_enc_nhi         	   ,
                                new_mast_enc_nhi    	   ,
                                event_type          	   ,
                                gender              	   ,
                                age_at_visit        	   ,
                                ethnicg1            	   ,
                                ethnicg2            	   ,
                                ethnicg3            	   ,
                                domicile_code       	   ,
                                sent_domicile_code  	   ,
                                facility            	   ,
                                agency              	   ,
                                accident_flag       	   ,
                                alcohol_involved_flag	   ,
                                health_provider_type	   ,
                                health_specialty_code	   ,
                                mode_of_delivery_code	   ,
                                location            	   ,
                                service_type        	   ,
                                purchase_unit       	   ,
                                attendance_code     	   ,
                                volume              	   ,
                                unit_of_measure     	   ,
                                idf_uom             	   ,
                                triage_level        	   ,
                                datetime_of_departure	   ,
                                datetime_of_event_end	   ,
                                datetime_of_first_contact,
                                datetime_of_presentation,
                                datetime_of_service 	   ,
                                year_month_birth    	   ,
                                ethnicgp            	   ,
                                dep13               	   ,
                                dep18
                from nap
                where date_parse(substr(datetime_of_service,1,9), '%d%b%Y') >= date '1956-12-05'
                  and date_parse(substr(datetime_of_service,1,9), '%d%b%Y') <= date '2022-12-31'
                  and new_mast_enc_nhi is not null
                  and new_mast_enc_nhi <> ''

)

SELECT 'Ovarian', 'NAP Events', 'Min Date', min(date_parse(substr(datetime_of_service,1,9), '%d%b%Y')), null
FROM "events"
union
SELECT 'Ovarian', 'NAP Events', 'Max Date', max(date_parse(substr(datetime_of_service,1,9), '%d%b%Y')), null
FROM "events"
union
SELECT 'Ovarian', 'NAP Events', 'Valid Row Count', null, count(*)
FROM "events"
union
SELECT 'Ovarian', 'NAP Events', 'Count Subjects', null, count(distinct "new_mast_enc_nhi")
FROM "events"

-- unrestricted raw checks against NAP table
union
select 'Ovarian', 'NAP Events', 'Full Row Count', null, count(*)
from nap
union
select 'Ovarian', 'NAP Events', 'Missing subject id', null, count(*)
from nap
where new_mast_enc_nhi is null
   or new_mast_enc_nhi = ''
union
select 'Ovarian', 'NAP Events', 'Missing date', null, count(*)
from nap
where datetime_of_service is null
   or datetime_of_service = ''
union
select 'Ovarian', 'NAP Events', 'Missing code', null, count(*)
from nap
where purchase_unit is null or purchase_unit=''

order by 1, 2, 3;

