with events as (select *
                from mos_coded
                where date_parse(dod, '%d/%m/%Y') >= date '1956-12-05'
                  and date_parse(dod, '%d/%m/%Y') <= date '2022-12-31'
                  and new_mast_enc_nhi is not null
                  and new_mast_enc_nhi <> '')
SELECT 'Ovarian', 'MOS Coded', 'Min Date', min(date_parse(dod, '%d/%m/%Y')), null
FROM "events"
union
SELECT 'Ovarian', 'MOS Coded', 'Max Date', max(date_parse(dod, '%d/%m/%Y')), null
FROM "events"
union
SELECT 'Ovarian', 'MOS Coded', 'Count Rows', null, count(*)
FROM "events"
union
SELECT 'Ovarian', 'MOS Coded', 'Count Subjects', null, count(distinct "new_mast_enc_nhi")
FROM "events"
union
select 'Ovarian', 'MOS Coded', 'Missing subject id', null, count(*) from mos_coded where new_mast_enc_nhi is null or new_mast_enc_nhi=''
union
select 'Ovarian', 'MOS Coded', 'Missing date', null, count(*) from mos_coded where dod is null or dod=''
union
select 'Ovarian', 'MOS Coded', 'Missing code', null, count(*) from mos_coded where bdm_death_cause_1 is null or bdm_death_cause_1=''

order by 1, 2, 3;


