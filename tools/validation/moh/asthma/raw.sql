SELECT 'Asthma', 'NAP Events', 'Min Date', min(date_parse(substr(datetime_of_service,1,10),'%Y-%m-%d')), null FROM "nap"
union
SELECT 'Asthma', 'NAP Events', 'Max Date', max(date_parse(substr(datetime_of_service,1,10),'%Y-%m-%d')), null FROM "nap"
union
SELECT 'Asthma', 'NAP Events', 'Count Rows',  null, count(*) FROM "nap"
union
SELECT 'Asthma', 'NAP Events', 'Count Subjects',  null, count(distinct "new_master_encrypted_hcu_id") FROM "nap"
order by 1,2,3


SELECT min(date_parse(evnt_start_date,'%d/%m/%Y')) FROM "nz_moh_gsk_asthma_and_copd_20221017"."pus_events";
-- 1982-08-24 00:00:00.000
SELECT max(date_parse(evnt_start_date,'%d/%m/%Y')) FROM "nz_moh_gsk_asthma_and_copd_20221017"."pus_events";
-- 2022-06-29 00:00:00.000



SELECT min(date_parse(evnt_start_date,'%d/%m/%Y')) FROM "nz_moh_gsk_asthma_and_copd_20221017"."prs_events";
-- 2015-10-30 00:00:00.000
SELECT max(date_parse(evnt_start_date,'%d/%m/%Y')) FROM "nz_moh_gsk_asthma_and_copd_20221017"."prs_events";
-- 2017-07-22 00:00:00.000


SELECT min(date_parse(date_dispensed,'%d/%m/%Y')) FROM "nz_moh_gsk_asthma_and_copd_20221017"."phh";
-- 2012-07-01 00:00:00.000
SELECT max(date_parse(date_dispensed,'%d/%m/%Y')) FROM "nz_moh_gsk_asthma_and_copd_20221017"."phh";
-- 2022-06-30 00:00:00.000
