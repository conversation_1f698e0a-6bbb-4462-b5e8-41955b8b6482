/**
 * See https://prospection.atlassian.net/wiki/spaces/BP/pages/2569175206/Workflow+Groovy+Script+Testing+-+from+Intellij for more information.
 *
 * This is a utility script that lets you quickly test groovy code that you want to put in the DynamicScriptProcessor node.
 *
 * The is an associated run script ".run/groovy_script_trigger_tagging.xml" for Intellij which is configured to NOT invoke
 * a build - so this lets you test your script very quickly (as compared to running a unit test which will invoke a build
 * of the entire project).
 *
 Groovy resources:

 DSL reference
 https://prospection.atlassian.net/wiki/spaces/BP/pages/1690992664/Dash2+DynamicScriptProcessor+Groovy+Script+Node+DSL+User+Manual

 Code

 Groovy script to let you quickly develop the code - use the intellij run config provided
 warehouse/tools/

 JUnit tests that exercise groovy code - these are much slower to work with than the groovy script
 warehouse/workflow/src/test/groovy/com/pharmdash/workflow/dp/dynamicscriptdsl/recipes

 Gotchas
 Validation in the admin console is not 100% accurate - scripts may still have the wrong syntax and missing imports - test your workflow with a small set of subjects to be sure
 Beware some syntax works in Intellij but not in workflow

 */


import com.pharmdash.workflow.common.DateUtil
import com.pharmdash.workflow.dp.dynamicscriptdsl.SubjectScope
import com.pharmdash.workflow.dp.javadto.Event4J
import com.pharmdash.workflow.dp.javadto.Subject4J
import com.pharmdash.workflow.dp.javadto.SubjectInfo4J

import java.sql.Date

// --------------------- implicit imports which are available in the runtime ---------------------
// you do not need to add these in the workflow

// --------------------- your imports goes here ---------------------
// you will need to add these (the ones you use) in the workflow

// --------------------- implicit imports which are available in the runtime ---------------------
// you do not need to add these in the workflow

// --------------------- your imports goes here ---------------------
// you will need to add these (the ones you use) in the workflow

// -------------------------- end imports ---------------------------

// system info
println "Groovy Version: " + GroovySystem.getVersion()
println " JVM: " + System.getProperty("java.version")
println " Vendor: " + System.getProperty("java.vm.vendor")
println " OS: " + System.getProperty("os.name")
println ""

// script harness
def actual = getSubjects().collect { subject ->
    def closure = {
        // -------- config start --------
        def interestedGroups = ["A", "B"]
        def daysRange = 10
        def outputTag = "sumTag"
        def amountTag = "amountTag"
        // -------- config end --------

        // -------- groovy script start --------
        def withinRange = { event, currentEvent, daysBack ->
            def distance = DateUtil.distanceInDays(event.getDate(), currentEvent.getDate())
            return distance <= daysBack && distance >= 0
        }

        subject.events.each { event ->
            // calculate the totalAmount for the events that are in the interested groups,
            // and happened within the last X days compared to the event date
            def totalAmount = subject.events
                .findAll(e -> {
                    e.groups.intersect(interestedGroups)
                        && withinRange(e, event, daysRange)
                })
                .sum(e -> {
                    if (!e.getTagValues(amountTag).isEmpty()) {
                        e.tags[amountTag].first().toInteger()
                    } else {
                        0
                    }
                })

            if (totalAmount >= 0) {
                event.tag(outputTag, totalAmount.toString())
            }
        }

        // end groovy script here
    } as Object


    closure.delegate = new SubjectScope(subject)
    closure.call()

    subject
}
// remove deleted subjects
    .findAll { !it.isToBeDeleted() }
// print remaining subjects
    .each { printSubject it }

// --------------------- sample subjects ---------------------

static def getSubjects() {
    Integer subjectId = 1;
    Integer eventId = 1;
    List<Subject4J> subjects = [];

    // configure your required subjects and events here
    {
        Subject4J subject1 = Subject4J.builder().id((subjectId++).toString())
            .info(SubjectInfo4J.builder()
                .dateOfBirth(date('1972-03-01'))
                .endOfDataset(date('2022-12-01'))
                .build())
            .build();

        subject1.setEvents(
            [
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-01'))
                    .groups(['A'])
                    .tags(['amountTag': ['1']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-02'))
                    .groups(['A'])
                    .tags(['amountTag': ['4']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-08'))
                    .groups(['X'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-09-01'))
                    .groups(['A'])
                    .tags(['amountTag': ['3']])
                    .subject(subject1)
                    .build(),

            ])

        Subject4J subject2 = Subject4J.builder().id((subjectId++).toString())
            .info(SubjectInfo4J.builder()
                .dateOfBirth(date('1972-03-01'))
                .endOfDataset(date('2022-12-01'))
                .build())
            .build();

        subject2.setEvents(
            [
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-01'))
                    .groups(['A'])
                    .tags(['amountTag': ['1']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2021-08-02'))
                    .groups(['A'])
                    .tags(['amountTag': ['4']])
                    .subject(subject1)
                    .build()
            ])

        Subject4J subject3 = Subject4J.builder().id((subjectId++).toString())
            .info(SubjectInfo4J.builder()
                .dateOfBirth(date('1972-03-01'))
                .endOfDataset(date('2022-12-01'))
                .build())
            .build();

        subject3.setEvents(
            [
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-01'))
                    .groups(['A'])
                    .tags(['amountTag': ['1']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-02'))
                    .groups(['B'])
                    .tags(['amountTag': ['4']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2021-08-01'))
                    .groups(['A'])
                    .tags(['amountTag': ['1']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2021-08-01'))
                    .groups(['B'])
                    .tags(['amountTag': ['4']])
                    .subject(subject1)
                    .build()
            ])

        Subject4J subject4 = Subject4J.builder().id((subjectId++).toString())
            .info(SubjectInfo4J.builder()
                .dateOfBirth(date('1972-03-01'))
                .endOfDataset(date('2022-12-01'))
                .build())
            .build();

        subject4.setEvents(
            [
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-01'))
                    .groups(['Z'])
                    .subject(subject1)
                    .build(),

            ])

        subjects.add(subject1)
        subjects.add(subject2)
        subjects.add(subject3)
        subjects.add(subject4)
    }

    return subjects
}

// tests

def amountTagsForSubjects = actual.collect({ subject ->
    subject.getEvents().collect {
        [it.groups, it.date.toString(), it.tags["amountTag"]?.first(), it.tags["sumTag"]?.first()]
    }
})

// assert the results when events are within the given days range and there is uninterested group X also in middle
assert amountTagsForSubjects[0] == [
    [['A'], '2020-08-01', '1', '1'],
    [['A'], '2020-08-02', '4', '5'],
    [['X'], '2020-08-08', null, '5'],
    [['A'], '2020-09-01', '3', '3']
]

// assert the results when events are outside the given days range
assert amountTagsForSubjects[1] == [
    [['A'], '2020-08-01', '1', '1'],
    [['A'], '2021-08-02', '4', '4']
]

// assert the results when some interested events happen on the same day
assert amountTagsForSubjects[2] == [
    [['A'], '2020-08-01', '1', '1'],
    [['B'], '2020-08-02', '4', '5'],
    [['A'], '2021-08-01', '1', '5'],
    [['B'], '2021-08-01', '4', '5']
]

// assert the results when some interested events happen on the same day
assert amountTagsForSubjects[3] == [
    [['Z'], '2020-08-01', null, null],
]


// utilities
static def printSubject(Subject4J subject) {
    println subject.getId()
    println "  " + subject.getInfo()
    subject.getEvents().each(event -> println "    " + event)
}

static def date(String date) {
    Date.valueOf(date)
}
