/**
 * See https://prospection.atlassian.net/wiki/spaces/BP/pages/2569175206/Workflow+Groovy+Script+Testing+-+from+Intellij for more information.
 *
 * This is a utility script that lets you quickly test groovy code that you want to put in the DynamicScriptProcessor node.
 *
 * The is an associated run script ".run/groovy_script_trigger_tagging.xml" for Intellij which is configured to NOT invoke
 * a build - so this lets you test your script very quickly (as compared to running a unit test which will invoke a build
 * of the entire project).
 *
 Groovy resources:

 DSL reference
 https://prospection.atlassian.net/wiki/spaces/BP/pages/1690992664/Dash2+DynamicScriptProcessor+Groovy+Script+Node+DSL+User+Manual

 Code

 Groovy script to let you quickly develop the code - use the intellij run config provided
 warehouse/tools/

 JUnit tests that exercise groovy code - these are much slower to work with than the groovy script
 warehouse/workflow/src/test/groovy/com/pharmdash/workflow/dp/dynamicscriptdsl/recipes

 Gotchas
 Validation in the admin console is not 100% accurate - scripts may still have the wrong syntax and missing imports - test your workflow with a small set of subjects to be sure
 Beware some syntax works in Intellij but not in workflow

 */

import com.pharmdash.workflow.dp.dynamicscriptdsl.SubjectScope
import com.pharmdash.workflow.dp.javadto.Event4J
import com.pharmdash.workflow.dp.javadto.Subject4J
import com.pharmdash.workflow.dp.javadto.SubjectInfo4J
import com.pharmdash.workflow.common.DateUtil

import java.time.YearMonth

// --------------------- implicit imports which are available in the runtime ---------------------
// you do not need to add these in the workflow
import static com.pharmdash.workflow.dp.dynamicscriptdsl.SubjectPredicates.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.SubjectScope.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.EventPredicates.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.DateOperations.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.TagOperations.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.CollectionOperator.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.NumberOperator.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.DateOperator.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.Utils.*


// --------------------- your imports goes here ---------------------
// you will need to add these (the ones you use) in the workflow

import java.sql.Date
import java.time.Period
import java.time.LocalDate
import com.pharmdash.workflow.dp.trigger.*
import static com.pharmdash.workflow.dp.trigger.TriggerUtils.*


// -------------------------- end imports ---------------------------

// system info
println "Groovy Version: " + GroovySystem.getVersion()
println " JVM: " + System.getProperty("java.version")
println " Vendor: " + System.getProperty("java.vm.vendor")
println " OS: " + System.getProperty("os.name")
println ""

// script harness
getSubjects().collect { subject ->
    def closure = {
        // TODO: start groovy script here
        def interestedGroups = ["Dummy"]
        def monthRange = 0
        def outputTag = "sumFirazyr1M"
        def amountTag = "monthlyDose"
        // -------- config end --------

        // -------- groovy script start --------
        def lookBackWithinMonths = { YearMonth start, YearMonth end, int range ->
            start <= end && start >= end.minusMonths(range)
        }

        subject.events.findAll {event -> !interestedGroups.intersect(event.groups)}
            .each { event ->
            // calculate the totalAmount for the events that are in the interested groups,
            // and happened within the last X days compared to the event date
            def totalAmount = subject.events
                .findAll{ Event4J dummy ->
                    dummy.groups.intersect(interestedGroups)
                    && lookBackWithinMonths(dummy.date.toYearMonth(), event.date.toYearMonth(), monthRange)
                }
                .sum(e -> {
                    if (!e.getTagValues(amountTag).isEmpty()) {
                        e.tags[amountTag].first().toDouble()
                    } else {
                        0
                    }
                })

            if (totalAmount >= 0) {
                event.tag(outputTag, totalAmount.toString())
            }
        }

        // TODO: end groovy script here
    } as Object


    closure.delegate = new SubjectScope(subject)
    closure.call()

    subject
}
// remove deleted subjects
    .findAll { !it.isToBeDeleted() }
// print remaining subjects
    .each { printSubject it }

// --------------------- sample subjects ---------------------

static def getSubjects() {
    Integer subjectId = 1;
    Integer eventId = 1;
    List<Subject4J> subjects = [];

    // configure your required subjects and events here
    {
        Subject4J subject = Subject4J.builder().id((subjectId++).toString())
            .info(SubjectInfo4J.builder()
                .dateOfBirth(date('1972-03-01'))
                .endOfDataset(date('2022-12-01'))
                .build())
            .build();

        subject.setEvents(
            [
                Event4J.builder()
                    .id(eventId++)
                    .groups(['b'])
                    .date(date('2023-01-01')) // sum = 10
                    .subject(subject)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .groups(['Dummy'])
                    .date(date('2023-01-31'))
                    .tags(['monthlyDose': ['10']])
                    .subject(subject)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .groups(['b'])
                    .date(date('2023-02-01')) // sum = 110
                    .subject(subject)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .groups(['Dummy'])
                    .date(date('2023-02-28'))
                    .tags(['monthlyDose': ['100']])
                    .subject(subject)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .groups(['b'])
                    .date(date('2023-03-01')) // sum = 121
                    .subject(subject)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .groups(['Dummy'])
                    .date(date('2023-03-31'))
                    .tags(['monthlyDose': ['11']])
                    .subject(subject)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .groups(['a'])
                    .date(date('2023-04-04')) // sum = 120
                    .subject(subject)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .groups(['a'])
                    .date(date('2023-04-05')) // sum = 120
                    .subject(subject)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .groups(['Dummy'])
                    .date(date('2023-04-30'))
                    .tags(['monthlyDose': ['9']])
                    .subject(subject)
                    .build(),
            ])
        subjects.add(subject)
    }

    return subjects

}

// utilities

static def printSubject(Subject4J subject) {
    println subject.getId()
    println "  " + subject.getInfo()
    subject.getEvents().each(event -> println "    " + event)
}

static def date(String date) {
    Date.valueOf(date)
}
