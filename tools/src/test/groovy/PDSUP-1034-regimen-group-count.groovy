/**
 * See https://prospection.atlassian.net/wiki/spaces/BP/pages/2569175206/Workflow+Groovy+Script+Testing+-+from+Intellij for more information.
 *
 * This is a utility script that lets you quickly test groovy code that you want to put in the DynamicScriptProcessor node.
 *
 * The is an associated run script ".run/groovy_script_trigger_tagging.xml" for Intellij which is configured to NOT invoke
 * a build - so this lets you test your script very quickly (as compared to running a unit test which will invoke a build
 * of the entire project).
 *
 Groovy resources:

 DSL reference
 https://prospection.atlassian.net/wiki/spaces/BP/pages/1690992664/Dash2+DynamicScriptProcessor+Groovy+Script+Node+DSL+User+Manual

 Code

 Groovy script to let you quickly develop the code - use the intellij run config provided
 warehouse/tools/

 JUnit tests that exercise groovy code - these are much slower to work with than the groovy script
 warehouse/workflow/src/test/groovy/com/pharmdash/workflow/dp/dynamicscriptdsl/recipes

 Gotchas
 Validation in the admin console is not 100% accurate - scripts may still have the wrong syntax and missing imports - test your workflow with a small set of subjects to be sure
 Beware some syntax works in Intellij but not in workflow

 */

import org.junit.jupiter.api.Test

// --------------------- implicit imports which are available in the runtime ---------------------
// you do not need to add these in the workflow
import com.pharmdash.workflow.dp.dynamicscriptdsl.SubjectScope
import com.pharmdash.workflow.dp.javadto.Event4J
import com.pharmdash.workflow.dp.javadto.Subject4J
import com.pharmdash.workflow.dp.javadto.SubjectInfo4J

// --------------------- your imports goes here ---------------------
// you will need to add these (the ones you use) in the workflow

import java.sql.Date

// -------------------------- end imports ---------------------------

// system info
println "Groovy Version: " + GroovySystem.getVersion()
println " JVM: " + System.getProperty("java.version")
println " Vendor: " + System.getProperty("java.vm.vendor")
println " OS: " + System.getProperty("os.name")
println ""

class RegimenGroupCount {


    public static final String REGIMEN_GROUP_COUNT = "regimenGroupCount"
    public static final String TAG_VALUE = "true"

    static void main(String[] args) {
    }

    // script harness
    static Subject4J subjectFunction(Subject4J subject) {
        def closure = {
            // -------- config start --------
            /** Assumptions:
             * 1. Each event has a single regimen tag value. If more than one regimen tag value is present, an error will be thrown
             * 2. regimen_others is not valid for this groovy and will give an error
             * 3. regimen_groupA+* is not valid for this groovy and will give an error
             **/
            def tagName = "regimenGroupCount"

            // -------- config end --------

            // groovy starts - Please do not touch the code below

            def regimenTag = "regimen"
            def othersSuffix = "_others"
            def customOthersSuffix = "*"
            subject.events.each { event ->
                def tags = event.tags

                if (tags.containsKey(regimenTag)) {
                    def regimenVal = tags[regimenTag]
                    if (regimenVal.size() > 1) {
                        throw new IllegalArgumentException("Event ${event.id} has more than one regimen tag")
                    } else {
                        def regimen = regimenVal[0]
                        if (regimen.endsWith(othersSuffix)) {
                            throw new IllegalArgumentException("Event ${event.id} has _others in regimen tag")
                        }
                        if (regimen.endsWith(customOthersSuffix)) {
                            throw new IllegalArgumentException("Event ${event.id} has * in regimen tag")
                        }
                        def count = (regimen =~ /\+/).count + 1
                        event.tags[tagName] = [count.toString()]
                    }
                }
            }
        }

        // groovy end here

        closure.delegate = new SubjectScope(subject)
        closure.call()

        subject
    }

    @Test
    void should_add_regimen_group_count_from_regimen_tag() {
        def testSubject = Subject4J.builder().id("1")
            .info(SubjectInfo4J.builder()
                .build())
            .events([
                Event4J.builder()
                    .id(1)
                    .groups(['A'])
                    .date(date('2023-02-01'))
                    .tags(
                        [
                            'regimen': ['regimen_A']
                        ]
                    )
                    .build(),
                Event4J.builder()
                    .id(2)
                    .groups(['B'])
                    .date(date('2023-02-02'))
                    .tags(['regimen': ['regimen_B+C']])
                    .build(),
                Event4J.builder()
                    .id(3)
                    .groups(['C'])
                    .date(date('2023-02-03'))
                    .build()
            ])
            .build()

        def actualSubject = subjectFunction(testSubject)
        assert actualSubject.events.size() == 3
        assert actualSubject.events[0].tags[REGIMEN_GROUP_COUNT] == ['1']
        assert actualSubject.events[1].tags[REGIMEN_GROUP_COUNT] == ['2']
        assert actualSubject.events[2].tags[REGIMEN_GROUP_COUNT] == null
    }

    @Test
    void should_throw_exception_when_more_than_one_regimens_in_event() {
        def testSubject = Subject4J.builder().id("1")
            .info(SubjectInfo4J.builder()
                .build())
            .events([
                Event4J.builder()
                    .id(1)
                    .groups(['A'])
                    .date(date('2023-02-01'))
                    .tags(
                        [
                            'regimen': ['regimen_A', 'regimen_B']
                        ]
                    )
                    .build()
            ])
            .build()

        try {
            subjectFunction(testSubject)
            assert false
        } catch (IllegalArgumentException e) {
            assert e.message == "Event 1 has more than one regimen tag"
        }
    }

    @Test
    void should_throw_exception_when_regimens_others_in_event() {
        def testSubject = Subject4J.builder().id("1")
            .info(SubjectInfo4J.builder()
                .build())
            .events([
                Event4J.builder()
                    .id(1)
                    .groups(['A'])
                    .date(date('2023-02-01'))
                    .tags(
                        [
                            'regimen': ['regimen_others']
                        ]
                    )
                    .build()
            ])
            .build()

        try {
            subjectFunction(testSubject)
            assert false
        } catch (IllegalArgumentException e) {
            assert e.message == "Event 1 has _others in regimen tag"
        }
    }

    @Test
    void should_throw_exception_when_custom_regimens_others_in_event() {
        def testSubject = Subject4J.builder().id("1")
            .info(SubjectInfo4J.builder()
                .build())
            .events([
                Event4J.builder()
                    .id(1)
                    .groups(['A'])
                    .date(date('2023-02-01'))
                    .tags(
                        [
                            'regimen': ['regimen_A+*']
                        ]
                    )
                    .build()
            ])
            .build()

        try {
            subjectFunction(testSubject)
            assert false
        } catch (IllegalArgumentException e) {
            assert e.message == "Event 1 has * in regimen tag"
        }
    }

    // utilities
    static def printSubject(Subject4J subject) {
        println subject.getId()
        println "  " + subject.getInfo()
        subject.getEvents().each(event -> println "    " + event)
    }

    static def date(String date) {
        Date.valueOf(date)
    }

}
