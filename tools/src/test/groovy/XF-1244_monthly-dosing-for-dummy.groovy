/**
 * See https://prospection.atlassian.net/wiki/spaces/BP/pages/2569175206/Workflow+Groovy+Script+Testing+-+from+Intellij for more information.
 *
 * This is a utility script that lets you quickly test groovy code that you want to put in the DynamicScriptProcessor node.
 *
 * The is an associated run script ".run/groovy_script_trigger_tagging.xml" for Intellij which is configured to NOT invoke
 * a build - so this lets you test your script very quickly (as compared to running a unit test which will invoke a build
 * of the entire project).
 *
 Groovy resources:

 DSL reference
 https://prospection.atlassian.net/wiki/spaces/BP/pages/1690992664/Dash2+DynamicScriptProcessor+Groovy+Script+Node+DSL+User+Manual

 Code

 Groovy script to let you quickly develop the code - use the intellij run config provided
 warehouse/tools/

 JUnit tests that exercise groovy code - these are much slower to work with than the groovy script
 warehouse/workflow/src/test/groovy/com/pharmdash/workflow/dp/dynamicscriptdsl/recipes

 Gotchas
 Validation in the admin console is not 100% accurate - scripts may still have the wrong syntax and missing imports - test your workflow with a small set of subjects to be sure
 Beware some syntax works in Intellij but not in workflow

 */


import com.pharmdash.workflow.common.DateUtil
import com.pharmdash.workflow.dp.dynamicscriptdsl.SubjectScope
import com.pharmdash.workflow.dp.javadto.Event4J
import com.pharmdash.workflow.dp.javadto.Subject4J
import com.pharmdash.workflow.dp.javadto.SubjectInfo4J
import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import org.apache.tools.ant.taskdefs.Local

import java.awt.Event
import java.text.SimpleDateFormat
import java.util.stream.Collectors

// --------------------- implicit imports which are available in the runtime ---------------------
// you do not need to add these in the workflow
import static com.pharmdash.workflow.dp.dynamicscriptdsl.SubjectPredicates.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.SubjectScope.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.EventPredicates.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.DateOperations.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.TagOperations.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.CollectionOperator.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.NumberOperator.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.DateOperator.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.Utils.*


// --------------------- your imports goes here ---------------------
// you will need to add these (the ones you use) in the workflow

import java.sql.Date
import java.time.Period
import java.time.LocalDate
import com.pharmdash.workflow.dp.trigger.*
import static com.pharmdash.workflow.dp.trigger.TriggerUtils.*


// -------------------------- end imports ---------------------------

// system info
println "Groovy Version: " + GroovySystem.getVersion()
println " JVM: " + System.getProperty("java.version")
println " Vendor: " + System.getProperty("java.vm.vendor")
println " OS: " + System.getProperty("os.name")
println ""

// script harness
def actual = getSubjects().collect { subject ->
    def closure = {
        // TODO: start groovy script here
        def interestedGroup = 'jp_firazyr'
        def interestedEvents = subject.events.findAll { it.groups.contains(interestedGroup) }
        if (interestedEvents.size() < 2) {
            return
        }
        def jsonSlurper = new JsonSlurper()
        def lastInterestedEvent = interestedEvents.last()
        def dummyEvents = subject.events.findAll { it.groups.contains('Dummy')
            && it.date.toLocalDate().isBefore(lastInterestedEvent.date.toLocalDate().plusDays(lastInterestedEvent.tags['daysSupply'].first().toInteger())) }

        // Map of current date to next date
        def createDateMap = { List<Event4J> events ->
            def dateMap = [:]

            for (int i = 0; i < events.size() - 1; i++) {
                def currentDate = events[i].date
                def nextDate = events[i + 1].date
                if (currentDate != nextDate) {
                    dateMap[currentDate] = nextDate
                }
            }

            return dateMap
        }

        def calculateDateUsageOfEventInSameMonth = { Date currentDate, Date nextDate ->
            if (!nextDate) {
                // return days left from currentDate to the end of month
                return DateUtil.distanceInDays(currentDate.toLocalDate(), currentDate.toYearMonth().atEndOfMonth()) + 1
            }
            if (currentDate.toYearMonth() == nextDate.toYearMonth()) {
                // return days distance from currentDate to the end of month
                return DateUtil.distanceInDays(currentDate, nextDate)
            }
            // currentDate is probably from previous month, usage in this month should start from 1st day to the nextDate
            return nextDate.toLocalDate().dayOfMonth - 1
        }

        def calculateMonthlyDoseFromSameMonthEvents = { List<Event4J> events ->
            def dateMap = createDateMap(events)
            events.collect { Event4J event ->
                String dailyDose = jsonSlurper.parseText(event.tags['dosing'].first())?['dailyDose']
                def daysLeft = calculateDateUsageOfEventInSameMonth(event.date, dateMap[event.date])
                return daysLeft * dailyDose.toDouble()
            }.sum()
        }

        def calculateMonthlyDoseFromPreviousEvents = { events, Date date ->
            def dailyDose = events.collect { Event4J event ->
                jsonSlurper.parseText(event.tags['dosing'].first())?['dailyDose'].toDouble()
            }.sum()
            return date.toYearMonth().lengthOfMonth() * dailyDose
        }
        // Main logic
        def eventMapByYearMonth = interestedEvents.groupBy { it.date.toYearMonth() }
        dummyEvents.each { dummy ->
            if (eventMapByYearMonth.containsKey(dummy.date.toYearMonth())) {
                def sameMonthEvents = eventMapByYearMonth[dummy.date.toYearMonth()]
                if (sameMonthEvents.first().date.toLocalDate().dayOfMonth != 1) {
                    // There might be another ongoing drug from previous month
                    // Need to find its dosing usage in this month as well
                    def prevEvents = interestedEvents
                        .findAll { it.date.toLocalDate().isBefore(sameMonthEvents.first().date.toLocalDate()) }
                        .groupBy { it.date }
                    if (prevEvents.size() > 0) {
                        sameMonthEvents = prevEvents[prevEvents.keySet().last()] + sameMonthEvents
                    }
                }
                dummy.tags['monthlyDose'] = [calculateMonthlyDoseFromSameMonthEvents(sameMonthEvents).toString()]
            } else {
                def prevEvents = interestedEvents
                    .findAll { it.date.toLocalDate().isBefore(dummy.date.toLocalDate()) }
                    .groupBy { it.date }
                dummy.tags['monthlyDose'] = [calculateMonthlyDoseFromPreviousEvents(prevEvents[prevEvents.keySet().last()], dummy.date).toString()]
            }
        }
        // TODO: end groovy script here
    } as Object


    closure.delegate = new SubjectScope(subject)
    closure.call()

    subject
}
// remove deleted subjects
    .findAll { !it.isToBeDeleted() }
// print remaining subjects
    .each { printSubject it }

// --------------------- sample subjects ---------------------

static def getSubjects() {
    Integer subjectId = 1;
    Integer eventId = 1;
    List<Subject4J> subjects = [];

    // configure your required subjects and events here
    {
        Subject4J subject1 = Subject4J.builder().id((subjectId++).toString())
            .info(SubjectInfo4J.builder()
                .dateOfBirth(date('1972-03-01'))
                .endOfDataset(date('2022-12-01'))
                .build())
            .build()

        subject1.setEvents(
            [
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2022-04-01'))
                    .groups(['jp_firazyr'])
                    .tags(['daysSupply': ['29'],'dosing': [JsonOutput.toJson(["event":"jp_firazyr","totalDose":"5.0","dailyDose":"1"])]])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2022-04-30'))
                    .groups(['jp_firazyr'])
                    .tags(['daysSupply': ['93'], 'dosing': [JsonOutput.toJson(["event":"jp_firazyr","totalDose":"10.0","dailyDose":"2"])]])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2022-04-30'))
                    .groups(['jp_firazyr'])
                    .tags(['daysSupply': ['93'], 'dosing': [JsonOutput.toJson(["event":"jp_firazyr","totalDose":"5.0","dailyDose":"3"])]])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2022-04-30'))
                    .groups(['Dummy'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2022-05-31'))
                    .groups(['Dummy'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2022-06-30'))
                    .groups(['Dummy'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2022-07-31'))
                    .groups(['Dummy'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2022-08-01'))
                    .groups(['jp_firazyr'])
                    .tags(['daysSupply': ['153'], 'dosing': [JsonOutput.toJson(["event":"jp_firazyr","totalDose":"10.0","dailyDose":"1"])]])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2022-08-31'))
                    .groups(['Dummy'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2022-09-30'))
                    .groups(['Dummy'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2022-10-31'))
                    .groups(['Dummy'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2022-11-30'))
                    .groups(['Dummy'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2022-12-31'))
                    .groups(['Dummy'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-01-01'))
                    .groups(['jp_firazyr'])
                    .tags(['daysSupply': ['153'], 'dosing': [JsonOutput.toJson(["event":"jp_firazyr","totalDose":"10.0","dailyDose":"4"])]])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-01-01'))
                    .groups(['jp_firazyr'])
                    .tags(['daysSupply': ['153'], 'dosing': [JsonOutput.toJson(["event":"jp_firazyr","totalDose":"10.0","dailyDose":"3"])]])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-01-31'))
                    .groups(['Dummy'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-02-28'))
                    .groups(['Dummy'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-03-31'))
                    .groups(['Dummy'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-04-30'))
                    .groups(['Dummy'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-05-31'))
                    .groups(['Dummy'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-06-30'))
                    .groups(['Dummy'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-07-31'))
                    .groups(['Dummy'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-08-31'))
                    .groups(['Dummy'])
                    .subject(subject1)
                    .build(),
            ])

        Subject4J subject2 = Subject4J.builder().id((subjectId++).toString())
            .info(SubjectInfo4J.builder()
                .dateOfBirth(date('1972-03-01'))
                .endOfDataset(date('2022-12-01'))
                .build())
            .build()
        subject2.setEvents([
            Event4J.builder()
                .id(eventId++)
                .date(date('2023-01-31'))
                .groups(['jp_firazyr'])
                .tags(['daysSupply': ['153'], 'dosing': [JsonOutput.toJson(["event":"jp_firazyr","totalDose":"10.0","dailyDose":"3"])]])
                .subject(subject1)
                .build(),
            Event4J.builder()
                .id(eventId++)
                .date(date('2023-01-31'))
                .groups(['jp_firazyr'])
                .tags(['daysSupply': ['153'], 'dosing': [JsonOutput.toJson(["event":"jp_firazyr","totalDose":"10.0","dailyDose":"1"])]])
                .subject(subject1)
                .build(),
            Event4J.builder()
                .id(eventId++)
                .date(date('2023-01-31'))
                .groups(['Dummy'])
                .subject(subject1)
                .build(),
            Event4J.builder()
                .id(eventId++)
                .date(date('2023-02-11'))
                .groups(['jp_firazyr'])
                .tags(['daysSupply': ['153'], 'dosing': [JsonOutput.toJson(["event":"jp_firazyr","totalDose":"10.0","dailyDose":"2"])]])
                .subject(subject1)
                .build(),
            Event4J.builder()
                .id(eventId++)
                .date(date('2023-02-20'))
                .groups(['jp_firazyr'])
                .tags(['daysSupply': ['153'], 'dosing': [JsonOutput.toJson(["event":"jp_firazyr","totalDose":"10.0","dailyDose":"1"])]])
                .subject(subject1)
                .build(),
            Event4J.builder()
                .id(eventId++)
                .date(date('2023-02-28'))
                .groups(['Dummy'])
                .subject(subject1)
                .build()
        ])

        subjects.add(subject1)
        subjects.add(subject2)
    }

    return subjects

}

// test
def monthlyDosesOfSubject1 =actual[0].getEvents()
    .findAll { event -> event.groups.contains('Dummy') }
    .collect { event -> [event.date.toString(), event.tags['monthlyDose']?.first()] }
assert monthlyDosesOfSubject1 == [
    ['2022-04-30', (1 * 29 + 2 + 3).toDouble().toString()],
    ['2022-05-31', ((2 + 3) * 31).toDouble().toString()],
    ['2022-06-30', ((2 + 3) * 30).toDouble().toString()],
    ['2022-07-31', ((2 + 3) * 31).toDouble().toString()],
    ['2022-08-31', (1 * 31).toDouble().toString()],
    ['2022-09-30', (1 * 30).toDouble().toString()],
    ['2022-10-31', (1 * 31).toDouble().toString()],
    ['2022-11-30', (1 * 30).toDouble().toString()],
    ['2022-12-31', (1 * 31).toDouble().toString()],
    ['2023-01-31', ((4 + 3) * 31).toDouble().toString()],
    ['2023-02-28', ((4 + 3) * 28).toDouble().toString()],
    ['2023-03-31', ((4 + 3) * 31).toDouble().toString()],
    ['2023-04-30', ((4 + 3) * 30).toDouble().toString()],
    ['2023-05-31', ((4 + 3) * 31).toDouble().toString()],
    ['2023-06-30', null],
    ['2023-07-31', null],
    ['2023-08-31', null]
]

def monthlyDosesOfSubject2 =actual[1].getEvents()
    .findAll { event -> event.groups.contains('Dummy') }
    .collect { event -> [event.date.toString(), event.tags['monthlyDose']?.first()] }
assert monthlyDosesOfSubject2 == [
    ['2023-01-31', (3 * 1 + 1 * 1).toDouble().toString()],
    ['2023-02-28', ((3 + 1) * 10 + 2 * 9 + 1 * 9).toDouble().toString()]
]

// utilities

static def printSubject(Subject4J subject) {
    println subject.getId()
    println "  " + subject.getInfo()
    subject.getEvents().each(event -> println "    " + event)
}

static def date(String date) {
    Date.valueOf(date)
}
