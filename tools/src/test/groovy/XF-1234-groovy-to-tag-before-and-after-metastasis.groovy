/**
 * See https://prospection.atlassian.net/wiki/spaces/BP/pages/2569175206/Workflow+Groovy+Script+Testing+-+from+Intellij for more information.
 *
 * This is a utility script that lets you quickly test groovy code that you want to put in the DynamicScriptProcessor node.
 *
 * The is an associated run script ".run/groovy_script_trigger_tagging.xml" for Intellij which is configured to NOT invoke
 * a build - so this lets you test your script very quickly (as compared to running a unit test which will invoke a build
 * of the entire project).
 *
 Groovy resources:

 DSL reference
 https://prospection.atlassian.net/wiki/spaces/BP/pages/1690992664/Dash2+DynamicScriptProcessor+Groovy+Script+Node+DSL+User+Manual

 Code

 Groovy script to let you quickly develop the code - use the intellij run config provided
 warehouse/tools/

 JUnit tests that exercise groovy code - these are much slower to work with than the groovy script
 warehouse/workflow/src/test/groovy/com/pharmdash/workflow/dp/dynamicscriptdsl/recipes

 Gotchas
 Validation in the admin console is not 100% accurate - scripts may still have the wrong syntax and missing imports - test your workflow with a small set of subjects to be sure
 Beware some syntax works in Intellij but not in workflow

 */

import com.pharmdash.workflow.dp.dynamicscriptdsl.SubjectScope
import com.pharmdash.workflow.dp.javadto.Event4J
import com.pharmdash.workflow.dp.javadto.Subject4J
import com.pharmdash.workflow.dp.javadto.SubjectInfo4J

import java.sql.Date

// --------------------- implicit imports which are available in the runtime ---------------------
// you do not need to add these in the workflow

// --------------------- your imports goes here ---------------------
// you will need to add these (the ones you use) in the workflow

// --------------------- implicit imports which are available in the runtime ---------------------
// you do not need to add these in the workflow

// --------------------- your imports goes here ---------------------
// you will need to add these (the ones you use) in the workflow

// -------------------------- end imports ---------------------------

// system info
println "Groovy Version: " + GroovySystem.getVersion()
println " JVM: " + System.getProperty("java.version")
println " Vendor: " + System.getProperty("java.vm.vendor")
println " OS: " + System.getProperty("os.name")
println ""

// script harness
def actual = getSubjects().collect { subject ->
    def closure = {
        // -------- config start --------
        def indexTag = "indexTag"
        def lineTag = "lineTag"
        def interestedGroups = ['A', 'B', 'C']
        def outputTag = 'outputTag'


        // -------- config end --------
        // start groovy script here
        def previousLineNo = null;
        subject.getEvents().findAll(event -> event.groups.intersect(interestedGroups))
            .reverse().each { event ->
            if (!event.getTagValues(lineTag).isEmpty()) {
                // journey no
                def lineTagNo = event.getTagValues(lineTag).first()
                if (!event.getTagValues(indexTag).isEmpty()) {
                    // had previous metastasis
                    previousLineNo = lineTagNo
                }
                if (previousLineNo != null && previousLineNo == lineTagNo) {
                    event.tag(outputTag, '1')
                } else {
                    event.tag(outputTag, '0')
                }
            }

        }

        // end groovy script here
    } as Object


    closure.delegate = new SubjectScope(subject)
    closure.call()

    subject
}
// remove deleted subjects
    .findAll { !it.isToBeDeleted() }
// print remaining subjects
    .each { printSubject it }

// --------------------- sample subjects ---------------------

static def getSubjects() {
    Integer subjectId = 1;
    Integer eventId = 1;
    List<Subject4J> subjects = [];

    // configure your required subjects and events here
    {
        Subject4J subject1 = Subject4J.builder().id((subjectId++).toString())
            .info(SubjectInfo4J.builder()
                .dateOfBirth(date('1972-03-01'))
                .endOfDataset(date('2022-12-01'))
                .build())
            .build();

        subject1.setEvents(
            [
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-01'))
                    .groups(['A'])
                    .tags('lineTag': ['1'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-02'))
                    .groups(['A'])
                    .tags(['lineTag': ['1']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-03'))
                    .groups(['B'])
                    .tags(['lineTag': ['2']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-04'))
                    .groups(['B'])
                    .tags(['indexTag': ['1'], 'lineTag': ['2']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-05'))
                    .groups(['B'])
                    .tags(['indexTag': ['1'], 'lineTag': ['2']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-06'))
                    .groups(['C'])
                    .tags(['indexTag': ['1'], 'lineTag': ['3']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-07'))
                    .groups(['C'])
                    .tags(['indexTag': ['1'], 'lineTag': ['3']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-08'))
                    .groups(['C'])
                    .tags(['indexTag': ['1'], 'lineTag': ['3']])
                    .subject(subject1)
                    .build(),
            ])


        subjects.add(subject1)

        Subject4J subject2 = Subject4J.builder().id((subjectId++).toString())
            .info(SubjectInfo4J.builder()
                .dateOfBirth(date('1972-03-01'))
                .endOfDataset(date('2022-12-01'))
                .build())
            .build();

        subject2.setEvents(
            [
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-01'))
                    .groups(['X'])
                    .subject(subject1)
                    .build()
            ])


        subjects.add(subject2)
        Subject4J subject3 = Subject4J.builder().id((subjectId++).toString())
            .info(SubjectInfo4J.builder()
                .dateOfBirth(date('1972-03-01'))
                .endOfDataset(date('2022-12-01'))
                .build())
            .build();

        subject3.setEvents(
            [
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-01'))
                    .groups(['X'])
                    .tags(['indexTag': ['1'], 'lineTag': ['1']])
                    .subject(subject1)
                    .build()
            ])


        subjects.add(subject3)

        Subject4J subject4 = Subject4J.builder().id((subjectId++).toString())
            .info(SubjectInfo4J.builder()
                .dateOfBirth(date('1972-03-01'))
                .endOfDataset(date('2022-12-01'))
                .build())
            .build();

        subject4.setEvents(
            [
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-01'))
                    .groups(['A'])
                    .tags('lineTag': ['1'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-02'))
                    .groups(['A'])
                    .tags(['lineTag': ['1']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-03'))
                    .groups(['B'])
                    .tags(['lineTag': ['2']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-04'))
                    .groups(['X'])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-05'))
                    .groups(['B'])
                    .tags(['indexTag': ['1'], 'lineTag': ['2']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-06'))
                    .groups(['B'])
                    .tags(['indexTag': ['1'], 'lineTag': ['2']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-07'))
                    .groups(['A'])
                    .tags(['indexTag': ['1'], 'lineTag': ['3']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-08'))
                    .groups(['A'])
                    .tags(['indexTag': ['1'], 'lineTag': ['3']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-09'))
                    .groups(['C'])
                    .tags(['indexTag': ['1'], 'lineTag': ['4']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-10'))
                    .groups(['C'])
                    .tags(['indexTag': ['1'], 'lineTag': ['4']])
                    .subject(subject1)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2020-08-11'))
                    .groups(['C'])
                    .tags(['indexTag': ['1'], 'lineTag': ['4']])
                    .subject(subject1)
                    .build(),
            ])
        subjects.add(subject4)
    }

    return subjects
}

// tests

def metastasisForSubjects = actual.collect({ subject ->
    subject.getEvents().collect {
        [it.groups, it.date.toString(), it.tags['lineTag']?.first(), it.tags['indexTag']?.first(), it.tags['outputTag']?.first()]
    }
})

// assert the results are adjusted when topicalCycle changes
assert metastasisForSubjects[0] == [
    [['A'], '2020-08-01', '1', null, '0'],
    [['A'], '2020-08-02', '1', null, '0'],
    [['B'], '2020-08-03', '2', null, '1'],
    [['B'], '2020-08-04', '2', '1', '1'],
    [['B'], '2020-08-05', '2', '1', '1'],
    [['C'], '2020-08-06', '3', '1', '1'],
    [['C'], '2020-08-07', '3', '1', '1'],
    [['C'], '2020-08-08', '3', '1', '1']
]

assert metastasisForSubjects[1] == [
    [['X'], '2020-08-01', null, null, null],
]


assert metastasisForSubjects[2] == [
    [['X'], '2020-08-01', '1', '1', null],
]

assert metastasisForSubjects[3] == [
    [['A'], '2020-08-01', '1', null, '0'],
    [['A'], '2020-08-02', '1', null, '0'],
    [['B'], '2020-08-03', '2', null, '1'],
    [['X'], '2020-08-04', null, null, null],
    [['B'], '2020-08-05', '2', '1', '1'],
    [['B'], '2020-08-06', '2', '1', '1'],
    [['A'], '2020-08-07', '3', '1', '1'],
    [['A'], '2020-08-08', '3', '1', '1'],
    [['C'], '2020-08-09', '4', '1', '1'],
    [['C'], '2020-08-10', '4', '1', '1'],
    [['C'], '2020-08-11', '4', '1', '1']
]

// utilities
static def printSubject(Subject4J subject) {
    println subject.getId()
    println "  " + subject.getInfo()
    subject.getEvents().each(event -> println "    " + event)
}

static def date(String date) {
    Date.valueOf(date)
}
