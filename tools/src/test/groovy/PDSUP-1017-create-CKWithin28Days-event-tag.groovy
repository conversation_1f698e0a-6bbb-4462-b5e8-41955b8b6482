/**
 * See https://prospection.atlassian.net/wiki/spaces/BP/pages/2569175206/Workflow+Groovy+Script+Testing+-+from+Intellij for more information.
 *
 * This is a utility script that lets you quickly test groovy code that you want to put in the DynamicScriptProcessor node.
 *
 * The is an associated run script ".run/groovy_script_trigger_tagging.xml" for Intellij which is configured to NOT invoke
 * a build - so this lets you test your script very quickly (as compared to running a unit test which will invoke a build
 * of the entire project).
 *
 Groovy resources:

 DSL reference
 https://prospection.atlassian.net/wiki/spaces/BP/pages/1690992664/Dash2+DynamicScriptProcessor+Groovy+Script+Node+DSL+User+Manual

 Code

 Groovy script to let you quickly develop the code - use the intellij run config provided
 warehouse/tools/

 JUnit tests that exercise groovy code - these are much slower to work with than the groovy script
 warehouse/workflow/src/test/groovy/com/pharmdash/workflow/dp/dynamicscriptdsl/recipes

 Gotchas
 Validation in the admin console is not 100% accurate - scripts may still have the wrong syntax and missing imports - test your workflow with a small set of subjects to be sure
 Beware some syntax works in Intellij but not in workflow

 */

// --------------------- implicit imports which are available in the runtime ---------------------
// you do not need to add these in the workflow
import com.pharmdash.workflow.dp.dynamicscriptdsl.SubjectScope
import com.pharmdash.workflow.dp.javadto.Event4J
import com.pharmdash.workflow.dp.javadto.Subject4J
import com.pharmdash.workflow.dp.javadto.SubjectInfo4J

// --------------------- your imports goes here ---------------------
// you will need to add these (the ones you use) in the workflow

import java.sql.Date

// -------------------------- end imports ---------------------------

// system info
println "Groovy Version: " + GroovySystem.getVersion()
println " JVM: " + System.getProperty("java.version")
println " Vendor: " + System.getProperty("java.vm.vendor")
println " OS: " + System.getProperty("os.name")
println ""

// script harness
def actual = getSubjects().collect { subject ->
    def closure = {
        // TODO: start groovy script here
        def tagName = "CKWithin28Days"
        def LDL = "jp_ck_labo"

        def plusDays = { Date date, int days ->
            Date.valueOf(date.toLocalDate().plusDays(days))
        }

        def findEarlierEvent = { String group, Event4J event, List<Event4J> events ->
            def after28Days = plusDays(event.date, 28)

            events.findAll {
                event.groups.contains(group)
                && it.groups.contains(group)
                && it.date.after(event.date)
                && it.date.before(after28Days)
                && event.id != it.id }
            .min{it.date}
        }

        subject.events.forEach { event ->
            def latestEvent = findEarlierEvent(LDL, event, subject.events)
            if (latestEvent != null) {
                event.tags[tagName] = [latestEvent.date.toString()]
            }
        }

        // TODO: end groovy script here
    } as Object


    closure.delegate = new SubjectScope(subject)
    closure.call()

    subject
}
// remove deleted subjects
    .findAll { !it.isToBeDeleted() }
// print remaining subjects
    .each { printSubject it }

// --------------------- sample subjects ---------------------

static List<Subject4J> getSubjects() {
    Integer subjectId = 1;
    Integer eventId = 1;

    // configure your required subjects and events here
    List<Subject4J> subjects = [
        Subject4J.builder().id((subjectId++).toString())
            .info(SubjectInfo4J.builder()
                .build())
            .events([
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-02-01'))
                    .groups(['jp_ck_labo'])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-02-02'))
                    .groups(['A'])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-02-03'))
                    .groups(['jp_ck_labo'])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-02-03'))
                    .groups(['jp_ck_labo'])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-02-05'))
                    .groups(['jp_ck_labo'])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-02-12'))
                    .groups(['A'])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-02-17'))
                    .groups(['jp_ck_labo'])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-03-17'))
                    .groups(['jp_ck_labo'])
                    .build(),
            ])
            .build()
    ]

    subjects.forEach {
        it.events.forEach { event ->
            event.subject = it
        }
    }

    return subjects

}

// test
def actualSubjectEventsAfterGroovy = actual.collect({ subject ->
    subject.getEvents().collect {
        [it.id, it.tags['CKWithin28Days']]
    }
})

println "Actual Output: " + actualSubjectEventsAfterGroovy[0]

assert actualSubjectEventsAfterGroovy[0][0] == [1, ['2023-02-03']]
assert actualSubjectEventsAfterGroovy[0][1] == [2, null]
assert actualSubjectEventsAfterGroovy[0][2] == [3, ['2023-02-05']]
assert actualSubjectEventsAfterGroovy[0][3] == [4, ['2023-02-05']]
assert actualSubjectEventsAfterGroovy[0][4] == [5, ['2023-02-17']]
assert actualSubjectEventsAfterGroovy[0][5] == [6, null]
assert actualSubjectEventsAfterGroovy[0][6] == [7, null]
assert actualSubjectEventsAfterGroovy[0][7] == [8, null]

// utilities

static def printSubject(Subject4J subject) {
    println subject.getId()
    println "  " + subject.getInfo()
    subject.getEvents().each(event -> println "    " + event)
}

static def date(String date) {
    Date.valueOf(date)
}
