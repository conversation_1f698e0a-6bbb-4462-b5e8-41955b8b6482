/**
 * See https://prospection.atlassian.net/wiki/spaces/BP/pages/2569175206/Workflow+Groovy+Script+Testing+-+from+Intellij for more information.
 *
 * This is a utility script that lets you quickly test groovy code that you want to put in the DynamicScriptProcessor node.
 *
 * The is an associated run script ".run/groovy_script_trigger_tagging.xml" for Intellij which is configured to NOT invoke
 * a build - so this lets you test your script very quickly (as compared to running a unit test which will invoke a build
 * of the entire project).
 *
 Groovy resources:

 DSL reference
 https://prospection.atlassian.net/wiki/spaces/BP/pages/1690992664/Dash2+DynamicScriptProcessor+Groovy+Script+Node+DSL+User+Manual

 Code

 Groovy script to let you quickly develop the code - use the intellij run config provided
 warehouse/tools/

 JUnit tests that exercise groovy code - these are much slower to work with than the groovy script
 warehouse/workflow/src/test/groovy/com/pharmdash/workflow/dp/dynamicscriptdsl/recipes

 Gotchas
 Validation in the admin console is not 100% accurate - scripts may still have the wrong syntax and missing imports - test your workflow with a small set of subjects to be sure
 Beware some syntax works in Intellij but not in workflow

 */

// --------------------- implicit imports which are available in the runtime ---------------------
// you do not need to add these in the workflow
import com.pharmdash.workflow.dp.dynamicscriptdsl.SubjectScope
import com.pharmdash.workflow.dp.javadto.Event4J
import com.pharmdash.workflow.dp.javadto.Subject4J
import com.pharmdash.workflow.dp.javadto.SubjectInfo4J

// --------------------- your imports goes here ---------------------
// you will need to add these (the ones you use) in the workflow

import java.sql.Date

// -------------------------- end imports ---------------------------

// system info
println "Groovy Version: " + GroovySystem.getVersion()
println " JVM: " + System.getProperty("java.version")
println " Vendor: " + System.getProperty("java.vm.vendor")
println " OS: " + System.getProperty("os.name")
println ""

// script harness
def actual = getSubjects().collect { subject ->
    def closure = {
        // TODO: start groovy script here
        def triggerNameSuffix = '_trigger'
        def tagDateMapping = [
            'jan_trigger': '2023-01-01',
            'feb_trigger': '2023-02-01',
            'mar_trigger': '2023-03-01',
            'apr_trigger': '2023-04-01',
            'may_trigger': '2023-05-01',
            'jun_trigger': '2023-06-01',
            'jul_trigger': '2023-07-01',
            'aug_trigger': '2023-08-01',
            'sep_trigger': '2023-09-01',
            'oct_trigger': '2023-10-01',
            'nov_trigger': '2023-11-01',
            'dec_trigger': '2023-12-01'
        ]

        subject.info.tags.each { tag, values ->
            // check if the tag is a trigger tag and has a value
            if (tag.endsWith(triggerNameSuffix) && values?.size() > 0 && values[0] != null) {
                def eventDate = tagDateMapping[tag]
                if (eventDate) {
                    subject.events << Event4J.builder()
                        .code(values[0])
                        .date(Date.valueOf(eventDate))
                        .groups([tag])
                        .build()
                }
            }
        }

        // TODO: end groovy script here
    } as Object


    closure.delegate = new SubjectScope(subject)
    closure.call()

    subject
}
// remove deleted subjects
    .findAll { !it.isToBeDeleted() }
// print remaining subjects
    .each { printSubject it }

// --------------------- sample subjects ---------------------

static List<Subject4J> getSubjects() {
    Integer subjectId = 1;
    Integer eventId = 1;

    // configure your required subjects and events here
    List<Subject4J> subjects = [
        Subject4J.builder().id((subjectId++).toString())
            .info(SubjectInfo4J.builder()
                .tags(['jan_trigger': ['jantrig001'], 'feb_trigger': ['febtrig004'], 'mar_trigger': ['martrig015']])
                .build())
            .events([
                Event4J.builder()
                    .id(eventId++)
                    .groups(['a'])
                    .date(date('2023-02-01'))
                    .build(),
            ])
            .build(),
        Subject4J.builder().id((subjectId++).toString())
            .info(SubjectInfo4J.builder()
                .tags(['jan_trigger': [null]])
                .build())
            .events([
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2022-01-03'))
                    .groups(['A'])
                    .build(),
            ])
            .build(),
        Subject4J.builder().id((subjectId++).toString())
            .info(SubjectInfo4J.builder()
                .tags(['jan_trigger': ['jantrig001']])
                .build())
            .events([
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2022-02-03'))
                    .groups(['A'])
                    .build(),
            ])
            .build(),
    ]

    subjects.forEach {
        it.events.forEach { event ->
            event.subject = it
        }
    }

    return subjects

}

// test
def actualSubjectEventsAfterGroovy = actual.collect({ subject ->
    subject.getEvents().collect {
        [subject.id, it.code, it.date.toString(), it.groups]
    }
})

println "Actual Output: " + actualSubjectEventsAfterGroovy[0]

assert actualSubjectEventsAfterGroovy[0] == [
    ['1', null, '2023-02-01', ['a']],
    ['1', 'jantrig001', '2023-01-01', ['jan_trigger']],
    ['1', 'febtrig004', '2023-02-01', ['feb_trigger']],
    ['1', 'martrig015', '2023-03-01', ['mar_trigger']]
]

println "Actual Output: " + actualSubjectEventsAfterGroovy[1]
assert actualSubjectEventsAfterGroovy[1] == [
    ['2', null, '2022-01-03', ['A']]
]

println "Actual Output: " + actualSubjectEventsAfterGroovy[2]
assert actualSubjectEventsAfterGroovy[2] == [
    ['3', null, '2022-02-03', ['A']],
    ['3', 'jantrig001', '2023-01-01', ['jan_trigger']]
]


// utilities

static def printSubject(Subject4J subject) {
    println subject.getId()
    println "  " + subject.getInfo()
    subject.getEvents().each(event -> println "    " + event)
}

static def date(String date) {
    Date.valueOf(date)
}
