/**
 * See https://prospection.atlassian.net/wiki/spaces/BP/pages/2569175206/Workflow+Groovy+Script+Testing+-+from+Intellij for more information.
 *
 * This is a utility script that lets you quickly test groovy code that you want to put in the DynamicScriptProcessor node.
 *
 * The is an associated run script ".run/groovy_script_trigger_tagging.xml" for Intellij which is configured to NOT invoke
 * a build - so this lets you test your script very quickly (as compared to running a unit test which will invoke a build
 * of the entire project).
 *
 Groovy resources:

 DSL reference
 https://prospection.atlassian.net/wiki/spaces/BP/pages/1690992664/Dash2+DynamicScriptProcessor+Groovy+Script+Node+DSL+User+Manual

 Code

 Groovy script to let you quickly develop the code - use the intellij run config provided
 warehouse/tools/

 JUnit tests that exercise groovy code - these are much slower to work with than the groovy script
 warehouse/workflow/src/test/groovy/com/pharmdash/workflow/dp/dynamicscriptdsl/recipes

 Gotchas
 Validation in the admin console is not 100% accurate - scripts may still have the wrong syntax and missing imports - test your workflow with a small set of subjects to be sure
 Beware some syntax works in Intellij but not in workflow

 */

// --------------------- implicit imports which are available in the runtime ---------------------
// you do not need to add these in the workflow
import com.pharmdash.workflow.dp.dynamicscriptdsl.SubjectScope
import com.pharmdash.workflow.dp.javadto.Event4J
import com.pharmdash.workflow.dp.javadto.Subject4J
import com.pharmdash.workflow.dp.javadto.SubjectInfo4J

// --------------------- your imports goes here ---------------------
// you will need to add these (the ones you use) in the workflow

import java.sql.Date

// -------------------------- end imports ---------------------------

// system info
println "Groovy Version: " + GroovySystem.getVersion()
println " JVM: " + System.getProperty("java.version")
println " Vendor: " + System.getProperty("java.vm.vendor")
println " OS: " + System.getProperty("os.name")
println ""

// script harness
def actual = getSubjects().collect { subject ->
    def closure = {
        // TODO: start groovy script here
        def comedicationDrugTag = 'ComedicationDrug'
        def targetDrugTag = 'TargetDrug'

        def getEventDateGroups = { targetTag ->
            subject.events.findAll { it.tags.get(targetTag) != null }.groupBy { it.date }
        }

        def eventDateGroups = getEventDateGroups( targetDrugTag)
        subject.events.each { event ->
            if (event.tags.get(targetDrugTag) != null) {
                def targetDrug = event.tags.TargetDrug[0]
                def comedicationEvents = eventDateGroups[event.date].findAll { it.tags.TargetDrug[0] != targetDrug }
                if (comedicationEvents) {
                    event.tags[comedicationDrugTag] = comedicationEvents.collect { it.tags.TargetDrug[0] }
                } else {
                    event.tags[comedicationDrugTag] = ["Mono"]
                }
            }
        }

        // TODO: end groovy script here
    } as Object


    closure.delegate = new SubjectScope(subject)
    closure.call()

    subject
}
// remove deleted subjects
    .findAll { !it.isToBeDeleted() }
// print remaining subjects
    .each { printSubject it }

// --------------------- sample subjects ---------------------

static List<Subject4J> getSubjects() {
    Integer subjectId = 1;
    Integer eventId = 1;

    // configure your required subjects and events here
    List<Subject4J> subjects = [
        Subject4J.builder().id((subjectId++).toString())
            .info(SubjectInfo4J.builder()
                .build())
            .events([
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-02-01'))
                    .tags(['TargetDrug': [
                        'A'
                    ]
                    ])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-02-01'))
                    .tags(['TargetDrug': [
                        'B'
                    ]
                    ])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-02-01'))
                    .tags(['TargetDrug': [
                        'C'
                    ]
                    ])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-02-08'))
                    .tags(['TargetDrug': [
                        'A'
                    ]
                    ])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-02-08'))
                    .tags(['TargetDrug': [
                        'B'
                    ]
                    ])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-02-12'))
                    .tags(['TargetDrug': [
                        'D'
                    ]
                    ])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .date(date('2023-02-02'))
                    .build(),
            ])
            .build()
    ]

    subjects.forEach {
        it.events.forEach { event ->
            event.subject = it
        }
    }

    return subjects

}

// test
def actualSubjectEventsAfterGroovy = actual.collect({ subject ->
    subject.getEvents().collect {
        [subject.id, it.tags['TargetDrug'], it.tags['ComedicationDrug']]
    }
})

println "Actual Output: " + actualSubjectEventsAfterGroovy[0]

assert actualSubjectEventsAfterGroovy[0][0] == ['1', ['A'], ['B', 'C']]
assert actualSubjectEventsAfterGroovy[0][1] == ['1', ['B'], ['A', 'C']]
assert actualSubjectEventsAfterGroovy[0][2] == ['1', ['C'], ['A', 'B']]
assert actualSubjectEventsAfterGroovy[0][3] == ['1', ['A'], ['B']]
assert actualSubjectEventsAfterGroovy[0][4] == ['1', ['B'], ['A']]
assert actualSubjectEventsAfterGroovy[0][5] == ['1', ['D'], ["Mono"]]
assert actualSubjectEventsAfterGroovy[0][6] == ['1', null, null]

// utilities

static def printSubject(Subject4J subject) {
    println subject.getId()
    println "  " + subject.getInfo()
    subject.getEvents().each(event -> println "    " + event)
}

static def date(String date) {
    Date.valueOf(date)
}
