/**
 * See https://prospection.atlassian.net/wiki/spaces/BP/pages/2569175206/Workflow+Groovy+Script+Testing+-+from+Intellij for more information.
 *
 * This is a utility script that lets you quickly test groovy code that you want to put in the DynamicScriptProcessor node.
 *
 * The is an associated run script ".run/groovy_script_trigger_tagging.xml" for Intellij which is configured to NOT invoke
 * a build - so this lets you test your script very quickly (as compared to running a unit test which will invoke a build
 * of the entire project).
 *
 Groovy resources:

 DSL reference
 https://prospection.atlassian.net/wiki/spaces/BP/pages/1690992664/Dash2+DynamicScriptProcessor+Groovy+Script+Node+DSL+User+Manual

 Code

 Groovy script to let you quickly develop the code - use the intellij run config provided
 warehouse/tools/

 JUnit tests that exercise groovy code - these are much slower to work with than the groovy script
 warehouse/workflow/src/test/groovy/com/pharmdash/workflow/dp/dynamicscriptdsl/recipes

 Gotchas
 Validation in the admin console is not 100% accurate - scripts may still have the wrong syntax and missing imports - test your workflow with a small set of subjects to be sure
 Beware some syntax works in Intellij but not in workflow

 */


import com.pharmdash.workflow.common.DateUtil
import com.pharmdash.workflow.dp.dynamicscriptdsl.SubjectScope
import com.pharmdash.workflow.dp.javadto.Event4J
import com.pharmdash.workflow.dp.javadto.Subject4J
import com.pharmdash.workflow.dp.javadto.SubjectInfo4J
import org.junit.jupiter.api.Test

// --------------------- implicit imports which are available in the runtime ---------------------
// you do not need to add these in the workflow

import java.sql.Date

// --------------------- your imports goes here ---------------------
// you will need to add these (the ones you use) in the workflow

// -------------------------- end imports ---------------------------

// system info
println "Groovy Version: " + GroovySystem.getVersion()
println " JVM: " + System.getProperty("java.version")
println " Vendor: " + System.getProperty("java.vm.vendor")
println " OS: " + System.getProperty("os.name")
println ""

class BTKiMaintainJourneyNo {

    static void main(String[] args) {
    }

    // script harness
    static Subject4J subjectFunction(Subject4J subject) {
        def closure = {
            // -------- config start --------
            def gapAllowedInDays = 30
//            def interestedBtkiGroups = ["regimen_pbs_ibrutinib_imbruvica_cll+*", "regimen_pbs_zanubrutinib_brukinsa_cll+*", "Acalabrutinib_mono"]
//            def interestedNonBtkiGroups = [
//                "regimen_pbs_venetoclax_venclexta_cll+pbs_obinutuzumab_gazyva_cll+*",
//                "regimen_pbs_idelalisib_zydelig_cll+*",
//                "regimen_pbs_ofatumamab_arzerra+*",
//                "regimen_pbs_obinutuzumab_gazyva_cll+*",
//                "regimen_pbs_chlorambucil+*",
//                "V absorbing V+C and V+I and V mono",
//                "Subtotal  - F C Combination Regimens",
//                "regimen_pbs_acalabrutinib_calquence_cll+pbs_obinutuzumab_gazyva_cll+*",
//                "Fake_claim"]
            def interestedBtkiGroups = ["a", "b", "c"]
            def interestedNonBtkiGroups = ["d", "e"]

            // -------- config end --------
            // start groovy script here
            def interestedGroups = interestedBtkiGroups + interestedNonBtkiGroups

            def isDrugBtki = { Event4J event ->
                event.groups.intersect(interestedBtkiGroups).size() > 0
            }

            def isSameBtkiDrugInGivenBtkiEvents = { Event4J event1, Event4J event2 ->
                event1.groups.intersect(event2.groups).intersect(interestedBtkiGroups).size() > 0
            }

            def isJourneyDifferentBetweenEvents = { Event4J event1, Event4J event2 ->
                event1.tags["journeyNo"]?.first()?.toInteger() != event2.tags["journeyNo"]?.first()?.toInteger()
            }

            def buildMapEventJourneyNameVsNo = { List<Event4J> interestEventsList ->
                def map = [:]
                interestEventsList.each { event ->
                    def journeyName = event.getTagValues("journeyName").first()
                    if (!map.containsKey(journeyName)) {
                        map[journeyName] = event.tags["journeyNo"]?.first()?.toInteger()
                    }
                }
                map
            }

            def interestedEvents = subject.events.findAll { event -> event.groups.intersect(interestedGroups) }

            // Create a map to store the first btki event with the same journeyNo
            def btkiVisitedMap = [:] as Map<String, Date>

            def putIfAbsent = { key, value ->
                if (!btkiVisitedMap.containsKey(key)) {
                    btkiVisitedMap.put(key, value)
                }
            }

            def eventsForWhichToChangeJourney = interestedEvents
                .collate(2, 1)
                .collect(eventPair -> {
                    if (eventPair.size() == 2) {
                        def currentEvent = eventPair.get(0)
                        def nextEvent = eventPair.get(1)
                        if (isDrugBtki(currentEvent) && isDrugBtki(nextEvent)) {
                            putIfAbsent(currentEvent.tags["journeyName"].first(), currentEvent.date)
                            // find 1st event before current event which has same journeyNo and journeyName
                            def firstEventHasSameJourneyNoDate = btkiVisitedMap.get(currentEvent.tags["journeyName"].first())
                            if (firstEventHasSameJourneyNoDate &&
                                DateUtil.distanceInDays(firstEventHasSameJourneyNoDate, nextEvent.date) < gapAllowedInDays &&
                                !isSameBtkiDrugInGivenBtkiEvents(currentEvent, nextEvent) &&
                                isJourneyDifferentBetweenEvents(currentEvent, nextEvent) &&
                                !btkiVisitedMap.containsKey(nextEvent.tags["journeyName"].first())) {
                                putIfAbsent(nextEvent.tags["journeyName"].first(), nextEvent.date)
                                return nextEvent.id
                            }
                        }
                    }
                })
                .findAll { it != null }

            // modify journeyNo for events
            def toSubtract = 0
            subject.events.forEach { event ->
                if (eventsForWhichToChangeJourney.contains(event.id)) {
                    ++toSubtract
                }
                if (event.tags["journeyNo"]?.first()) {
                    def journeyNo = event.tags["journeyNo"]?.first().toInteger() - toSubtract
                    event.getTags().put("journeyNo", [journeyNo.toString()])
                }
            }

            def groupVsJourneyNo = buildMapEventJourneyNameVsNo(interestedEvents)
            interestedEvents
                .forEach { event ->
                    if (event.groups.intersect(interestedGroups).size() > 0) {
                        def key = event.groups.intersect(interestedGroups).first()
                        event.getTags().put("journeyLatestLineNo", [groupVsJourneyNo[key].toString()])
                    }
                }

            // end groovy script here
        }

        closure.delegate = new SubjectScope(subject)
        closure.call()

        subject
    }

    @Test
    void test_2_different_btki_with_gap_within_range() {
        def testSubject = Subject4J.builder().id("1")
                .info(SubjectInfo4J.builder()
                        .dateOfBirth(date('1972-03-01'))
                        .endOfDataset(date('2022-12-01'))
                        .build())
                .events([
                        Event4J.builder()
                                .id(1)
                                .groups(['a'])
                                .date(date('2023-01-03'))
                                .tags(['journeyNo': ['1'], 'journeyName': ['a'], 'journeyLatestLineNo': ['1']])
                                .build(),
                        Event4J.builder()
                                .id(2)
                                .groups(['a'])
                                .date(date('2023-01-04'))
                                .tags(['journeyNo': ['1'], 'journeyName': ['a'], 'journeyLatestLineNo': ['1']])
                                .build(),
                        Event4J.builder()
                            .id(3)
                            .groups(['b'])
                            .date(date('2023-01-29'))
                            .tags(['journeyNo': ['2'], 'journeyName': ['b'], 'journeyLatestLineNo': ['2']])
                            .build(),
                ])
                .build()

        def actualSubject = subjectFunction(testSubject)
        printSubject(actualSubject)
        assert actualSubject.events.size() == 3
        testEvent(actualSubject.events.get(0), '1', 'a', date('2023-01-03'), '1')
        testEvent(actualSubject.events.get(1), '1', 'a', date('2023-01-04'), '1')
        testEvent(actualSubject.events.get(2), '1', 'b', date('2023-01-29'), '1')
    }

    @Test
    void test_2_different_btki_with_gap_out_of_range() {
        def testSubject = Subject4J.builder().id("1")
            .info(SubjectInfo4J.builder()
                .dateOfBirth(date('1972-03-01'))
                .endOfDataset(date('2022-12-01'))
                .build())
            .events([
                Event4J.builder()
                    .id(1)
                    .groups(['a'])
                    .date(date('2023-01-03'))
                    .tags(['journeyNo': ['1'], 'journeyName': ['a'], 'journeyLatestLineNo': ['1']])
                    .build(),
                Event4J.builder()
                    .id(2)
                    .groups(['a'])
                    .date(date('2023-02-03'))
                    .tags(['journeyNo': ['1'], 'journeyName': ['a'], 'journeyLatestLineNo': ['1']])
                    .build(),
                Event4J.builder()
                    .id(3)
                    .groups(['a'])
                    .date(date('2023-12-09'))
                    .tags(['journeyNo': ['1'], 'journeyName': ['a'], 'journeyLatestLineNo': ['1']])
                    .build(),
                Event4J.builder()
                    .id(4)
                    .groups(['b'])
                    .date(date('2024-01-06'))
                    .tags(['journeyNo': ['2'], 'journeyName': ['b'], 'journeyLatestLineNo': ['2']])
                    .build(),
            ])
            .build()

        def actualSubject = subjectFunction(testSubject)
        printSubject(actualSubject)
        assert actualSubject.events.size() == 4
        testEvent(actualSubject.events.get(0), '1', 'a', date('2023-01-03'), '1')
        testEvent(actualSubject.events.get(1), '1', 'a', date('2023-02-03'), '1')
        testEvent(actualSubject.events.get(2), '1', 'a', date('2023-12-09'), '1')
        testEvent(actualSubject.events.get(3), '2', 'b', date('2024-01-06'), '2')
    }

    @Test
    void test_2_different_btki_with_gap_within_range_but_interrupt_by_non_btki_interested_group() {
        def testSubject = Subject4J.builder().id("1")
            .info(SubjectInfo4J.builder()
                .dateOfBirth(date('1972-03-01'))
                .endOfDataset(date('2022-12-01'))
                .build())
            .events([
                Event4J.builder()
                    .id(1)
                    .groups(['a'])
                    .date(date('2023-01-03'))
                    .tags(['journeyNo': ['1'], 'journeyName': ['a'], 'journeyLatestLineNo': ['1']])
                    .build(),
                Event4J.builder()
                    .id(2)
                    .groups(['a'])
                    .date(date('2023-01-04'))
                    .tags(['journeyNo': ['1'], 'journeyName': ['a'], 'journeyLatestLineNo': ['1']])
                    .build(),
                Event4J.builder()
                    .id(3)
                    .groups(['d'])
                    .date(date('2023-01-05'))
                    .tags(['journeyNo': ['2'], 'journeyName': ['d'], 'journeyLatestLineNo': ['2']])
                    .build(),
                Event4J.builder()
                    .id(3)
                    .groups(['b'])
                    .date(date('2023-01-29'))
                    .tags(['journeyNo': ['3'], 'journeyName': ['b'], 'journeyLatestLineNo': ['3']])
                    .build(),
            ])
            .build()

        def actualSubject = subjectFunction(testSubject)
        printSubject(actualSubject)
        assert actualSubject.events.size() == 4
        testEvent(actualSubject.events.get(0), '1', 'a', date('2023-01-03'), '1')
        testEvent(actualSubject.events.get(1), '1', 'a', date('2023-01-04'), '1')
        testEvent(actualSubject.events.get(2), '2', 'd', date('2023-01-05'), '2')
        testEvent(actualSubject.events.get(3), '3', 'b', date('2023-01-29'), '3')
    }

    @Test
    void test_2_different_btki_should_ignore_re_initiation() {
        def testSubject = Subject4J.builder().id("1")
            .info(SubjectInfo4J.builder()
                .dateOfBirth(date('1972-03-01'))
                .endOfDataset(date('2022-12-01'))
                .build())
            .events([
                Event4J.builder()
                    .id(1)
                    .groups(['a'])
                    .date(date('2023-01-03'))
                    .tags(['journeyNo': ['1'], 'journeyName': ['a'], 'journeyLatestLineNo': ['1']])
                    .build(),
                Event4J.builder()
                    .id(2)
                    .groups(['b'])
                    .date(date('2023-01-04'))
                    .tags(['journeyNo': ['2'], 'journeyName': ['b'], 'journeyLatestLineNo': ['2']])
                    .build(),
                Event4J.builder()
                    .id(3)
                    .groups(['a'])
                    .date(date('2023-01-05'))
                    .tags(['journeyNo': ['2'], 'journeyName': ['b'], 'journeyLatestLineNo': ['2']])
                    .build(),
                Event4J.builder()
                    .id(4)
                    .groups(['c'])
                    .date(date('2023-01-20'))
                    .tags(['journeyNo': ['3'], 'journeyName': ['c'], 'journeyLatestLineNo': ['3']])
                    .build(),
            ])
            .build()

        def actualSubject = subjectFunction(testSubject)
        printSubject(actualSubject)
        assert actualSubject.events.size() == 4
        testEvent(actualSubject.events.get(0), '1', 'a', date('2023-01-03'), '1')
        testEvent(actualSubject.events.get(1), '1', 'b', date('2023-01-04'), '1')
        testEvent(actualSubject.events.get(2), '1', 'b', date('2023-01-05'), '1')
        testEvent(actualSubject.events.get(3), '1', 'c', date('2023-01-20'), '1')
    }

    static def testEvent(Event4J event, String journeyNo, String journeyName, Date expectedDate, String latestLineNo) {
        assert event != null
        assert event.tags["journeyNo"]?.first() == journeyNo
        assert event.tags["journeyName"]?.first() == journeyName
        assert event.tags["journeyLatestLineNo"]?.first() == latestLineNo
        assert event.date == expectedDate
    }

    // utilities
    static def printSubject(Subject4J subject) {
        println subject.getId()
        println "  " + subject.getInfo()
        subject.getEvents().each(event -> println "    " + event)
    }

    static def date(String date) {
        Date.valueOf(date)
    }

}
