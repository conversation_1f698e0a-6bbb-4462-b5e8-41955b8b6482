/**
 * See https://prospection.atlassian.net/wiki/spaces/BP/pages/2569175206/Workflow+Groovy+Script+Testing+-+from+Intellij for more information.
 *
 * This is a utility script that lets you quickly test groovy code that you want to put in the DynamicScriptProcessor node.
 *
 * The is an associated run script ".run/groovy_script_trigger_tagging.xml" for Intellij which is configured to NOT invoke
 * a build - so this lets you test your script very quickly (as compared to running a unit test which will invoke a build
 * of the entire project).
 *
 Groovy resources:

 DSL reference
 https://prospection.atlassian.net/wiki/spaces/BP/pages/1690992664/Dash2+DynamicScriptProcessor+Groovy+Script+Node+DSL+User+Manual

 Code

 Groovy script to let you quickly develop the code - use the intellij run config provided
 warehouse/tools/

 JUnit tests that exercise groovy code - these are much slower to work with than the groovy script
 warehouse/workflow/src/test/groovy/com/pharmdash/workflow/dp/dynamicscriptdsl/recipes

 Gotchas
 Validation in the admin console is not 100% accurate - scripts may still have the wrong syntax and missing imports - test your workflow with a small set of subjects to be sure
 Beware some syntax works in Intellij but not in workflow

 */

import com.pharmdash.workflow.dp.dynamicscriptdsl.SubjectScope
import com.pharmdash.workflow.dp.javadto.Event4J
import com.pharmdash.workflow.dp.javadto.Subject4J
import com.pharmdash.workflow.dp.javadto.SubjectInfo4J

// --------------------- implicit imports which are available in the runtime ---------------------
// you do not need to add these in the workflow
import static com.pharmdash.workflow.dp.dynamicscriptdsl.SubjectPredicates.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.SubjectScope.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.EventPredicates.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.DateOperations.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.TagOperations.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.CollectionOperator.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.NumberOperator.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.DateOperator.*
import static com.pharmdash.workflow.dp.dynamicscriptdsl.Utils.*


// --------------------- your imports goes here ---------------------
// you will need to add these (the ones you use) in the workflow

import java.sql.Date
import java.time.Period
import java.time.LocalDate
import com.pharmdash.workflow.dp.trigger.*
import static com.pharmdash.workflow.dp.trigger.TriggerUtils.*


// -------------------------- end imports ---------------------------

// system info
println "Groovy Version: " + GroovySystem.getVersion()
println " JVM: " + System.getProperty("java.version")
println " Vendor: " + System.getProperty("java.vm.vendor")
println " OS: " + System.getProperty("os.name")
println ""

// script harness
def actual = getSubjects().collect { subject ->
    def closure = {
        // Maintenance is defined by the continuation of regimens that are subsets of a previously defined parent regimen.
        // Assuming regimen algorithm is already run with explicit item groups only. So we know exactly what item groups are
        // in each regimen.
        // This Groovy does 2 things:
        // - Summarise regimens by new definition. E.g. A+* should contain all regimens that has A.
        // - Adjusts maintenance regimens into parent regimens as defined above.

        // these are regimens that we want to summarise into
        List<List<String>> parentRegimenItemGroups = [
            ["A", "*"],
            ["B", "C"]
        ]

        def parentRegimenDefinitions = parentRegimenItemGroups.collect { itemGroups ->
            def regimenDefinition = [:]
            regimenDefinition.name = "regimen_" + itemGroups.join("+")
            regimenDefinition.itemGroups = itemGroups.findAll { it != "*" }
            regimenDefinition.includeOthers = itemGroups.contains("*")
            return regimenDefinition
        }

        // parse regimen string into item groups
        // unsafe if item group contains "+"
        // note if regimen ends with *, it will not match any parent regimen
        def parseRegimen = { String regimen ->
            def regimenWithoutPrefix = regimen.startsWith("regimen_") ? regimen.minus("regimen_") : regimen
            return regimenWithoutPrefix.split("\\+").toList()
        }

        def findMatchingParentRegimenDefinition = { List<String> itemGroups ->
            return parentRegimenDefinitions.find { parentRegimenDefinition ->
                def containsAllDefinedItemGroups = itemGroups.containsAll(parentRegimenDefinition.itemGroups)
                def canContainOtherItemGroupsOrBeExact = (parentRegimenDefinition.includeOthers || itemGroups.size() == parentRegimenDefinition.itemGroups.size())
                return containsAllDefinedItemGroups && canContainOtherItemGroupsOrBeExact
            }
        }

        def replaceRegimen = { Event4J event, String regimenToReplace, regimenDefinition ->
            event.groups -= regimenToReplace
            event.groups += regimenDefinition.name
            event.tags["regimen"] = [regimenDefinition.name]
        }

        // keep track of the parent regimen we encountered and narrow down the item groups subset as maintenance progresses
        def previousParentRegimen = [:]
        previousParentRegimen.regimenDefinition = null
        previousParentRegimen.itemGroupsSubset = null

        subject.events.each {
            def regimen = it.getTagValues("regimen")[0]
            if (regimen) {
                def itemGroups = parseRegimen(regimen)

                // check if we're trying to continue maintenance on an existing parent regimen
                if (previousParentRegimen.itemGroupsSubset && previousParentRegimen.itemGroupsSubset.containsAll(itemGroups)) {
                    // this narrows down applicable item groups in the subset while the maintenance continues
                    previousParentRegimen.itemGroupsSubset = itemGroups
                    replaceRegimen(it, regimen, previousParentRegimen.regimenDefinition)
                } else {
                    def parentRegimen = findMatchingParentRegimenDefinition(itemGroups)
                    if (parentRegimen) {
                        // update parent regimen if we encounter a new regimen that strictly matches regimen definition
                        previousParentRegimen.regimenDefinition = parentRegimen
                        previousParentRegimen.itemGroupsSubset = itemGroups
                        replaceRegimen(it, regimen, previousParentRegimen.regimenDefinition)
                    } else {
                        // not a maintenance and we are not seeing a new parent regimen yet
                        previousParentRegimen.regimenDefinition = null
                        previousParentRegimen.itemGroupsSubset = null
                    }
                }
            } else {
                // if there isn't a regimen, then event is not interesting
            }
        }

    } as Object


    closure.delegate = new SubjectScope(subject)
    closure.call()

    subject
}
// remove deleted subjects
    .findAll { !it.isToBeDeleted() }
// print remaining subjects
    .each { printSubject it }

// --------------------- sample subjects ---------------------

static def getSubjects() {
    Integer subjectId = 1;
    Integer eventId = 1;
    List<Subject4J> subjects = [];

    // configure your required subjects and events here
    {
        Subject4J subject = Subject4J.builder().id((subjectId++).toString())
            .info(SubjectInfo4J.builder()
                .dateOfBirth(date('1972-03-01'))
                .endOfDataset(date('2022-12-01'))
                .build())
            .build();

        subject.setEvents(
            [
                Event4J.builder()
                    .id(eventId++)
                    .groups(['regimen_A'])
                    .date(date('2000-01-01'))
                    .subject(subject)
                    .tags(['regimen': ['regimen_A']])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .groups(['regimen_A+B+C'])
                    .date(date('2000-01-01'))
                    .subject(subject)
                    .tags(['regimen': ['regimen_A+B+C']])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .groups(['D'])
                    .date(date('2000-01-01'))
                    .subject(subject)
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .groups(['regimen_B+C'])
                    .date(date('2000-01-01'))
                    .subject(subject)
                    .tags(['regimen': ['regimen_B+C']])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .groups(['regimen_C'])
                    .date(date('2000-01-01'))
                    .subject(subject)
                    .tags(['regimen': ['regimen_C']])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .groups(['regimen_B'])
                    .date(date('2000-01-01'))
                    .subject(subject)
                    .tags(['regimen': ['regimen_B']])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .groups(['regimen_B+C'])
                    .date(date('2000-01-01'))
                    .subject(subject)
                    .tags(['regimen': ['regimen_B+C']])
                    .build(),
                Event4J.builder()
                    .id(eventId++)
                    .groups(['regimen_C'])
                    .date(date('2000-01-01'))
                    .subject(subject)
                    .tags(['regimen': ['regimen_C']])
                    .build(),
            ])
        subjects.add(subject)
    }

    return subjects

}

// test
def regimenTags = actual[0].getEvents().collect {
    return it.getTagValues("regimen")
}

assert regimenTags == [
    ['regimen_A+*'],
    ['regimen_A+*'],
    [],
    ['regimen_A+*'],
    ['regimen_A+*'],
    ['regimen_B'],
    ['regimen_B+C'],
    ['regimen_B+C']
]

// utilities

static def printSubject(Subject4J subject) {
    println subject.getId()
    println "  " + subject.getInfo()
    subject.getEvents().each(event -> println "    " + event)
}

static def date(String date) {
    Date.valueOf(date)
}
