package com.pharmdash.workflow

import com.pharmdash.workflow.dp.DataProcessor
import com.pharmdash.workflow.dp.javadto.{NodeDTO, ViolationDTO}
import com.pharmdash.workflow.dp.meta.processorannotations._
import org.apache.logging.log4j.scala.Logging

import java.lang.annotation.Annotation
import javax.validation.Valid
import javax.validation.constraints.{NotBlank, NotNull}

case class Node(id: Long,
                @NotBlank name: String,
                @NotNull @Valid dataProcessor: DataProcessor,
                shouldResume: Boolean = false,
                skippable: Boolean = false,
                upstreamNodesHash: String = "",
                upstreamDataset: Option[WorkflowDataset] = None,
                dto: NodeDTO = null,
               ) extends Logging {

    @NotNull
    var inputLinks: List[Link] = List()

    // You are on your own!
    var outputLinks: List[Link] = List()

    def inputChannels: Map[String, DataStreamType] = toMap(getAnnotations(classOf[Input]).map(el => (el.name(), el.streamType())))

    def requiredInputChannels: Seq[DataStreamType] = getAnnotations(classOf[Input]).filter(_.required()).map(_.streamType())

    def outputChannels: Map[String, DataStreamType] = toMap(getAnnotations(classOf[Output]).map(el => el.name() -> el.streamType()))

    private def getAnnotations[A <: Annotation](annoClass: Class[A]) = {
        dataProcessor.getClass.getAnnotationsByType(annoClass)
    }

    def execute()(implicit context: WorkflowContext): Unit = {
        val executionContext = WorkflowContext(context.spark, context.applicationConfig, context.workflow, Some(this), featureToggles = context.featureToggles)

        logger.info(s"Adding $this to execution DAG.")
        outputData = dataProcessor.doProcess(inputData)(executionContext)

        val cacheToFileStore = context.workflow.getWorkflowProperty("cacheToFileStore").exists(_.toBoolean)
        val isAutomateCachingEnabled = !context.workflow.getWorkflowProperty("disabledAutomateCaching").exists(_.toBoolean)
        val enableAutomateCaching = context.featureToggles.getOrElse(FeatureToggles.EnableAutomateCachingOnWorkflowGeneration, false)

        if (enableAutomateCaching && isAutomateCachingEnabled) {
            outputLinks
                .groupBy(_.sourceChannel)
                .foreach(branch => {
                    if (isBranchMatchCachingCondition(branch)) {
                        logger.info(s"Caching output $branch on $this.")
                        outputData(branch._1).cache(cacheToFileStore)
                    }
                })
        }

    }

    def validate()(implicit context: ValidationContext): Seq[ViolationDTO] = {
        val contextWithNode = context.copy(node = Some(this))
        try {
            dataProcessor.validate()(contextWithNode)
        } catch {
            // make sure node exception doesn't break the whole validation process
            case e: Exception =>
                logger.error(s"Error validating $this", e)
                Seq(ViolationDTO.error(id, s"Error validating $this - ${e.getMessage}.\nContact support devs if the problem persists"))
        }
    }

    /**
     * Once executed, a node will have data.
     *
     * Map represent channel result objects.
     */
    var outputData: Map[String, Results] = Map()

    /**
     * Input data for each channel is retrieved from all source links.
     *
     * Map represent channel result objects.
     */
    def inputData: Map[String, Results] = {
        if (shouldResume) {
            Map.empty
        } else {
            inputLinks.foldLeft(Map[String, Results]()) { (map, link) => map + (link.sinkChannel -> link.result()) }
        }
    }

    def nodePath(): Set[Long] = {
        inputLinks.map(_.source).flatMap(_.nodePath()).toSet[Long] + id
    }

    def dependencyIds(): Set[Long] = {
        nodePath() - id
    }

    def childNodes(): List[Node] = outputLinks.map(_.sink)

    def getId: Long = id

    def getName: String = name

    def getDataProcessor: DataProcessor = dataProcessor

    def isRootNode: Boolean = {
        inputLinks.isEmpty
    }

    def isTerminationNode: Boolean = {
        getAnnotations(classOf[Processor])
            .exists(el => el.value().equals(ProcessorCategory.REPORT)
                || el.value().equals(ProcessorCategory.EXPORT))
    }

    def upStreamNodeHash: String = upstreamNodesHash

    private def isBranchMatchCachingCondition(links: (String, List[Link]))(implicit context: WorkflowContext) = {
        links._2.size > 1 && context.workflow.hasMoreThanOneBranchesContainATerminationNode(links._2)
    }


    private def toMap(values: Seq[(String, DataStreamType)]): Map[String, DataStreamType] = values.groupBy(_._1).map(_._2.head)

    override def toString = s"Node($id, $name)"
}
