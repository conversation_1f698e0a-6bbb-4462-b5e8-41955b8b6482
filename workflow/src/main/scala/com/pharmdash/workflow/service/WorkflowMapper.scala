package com.pharmdash.workflow.service

import com.fasterxml.jackson.databind.JsonMappingException
import com.pharmdash.workflow._
import com.pharmdash.workflow.common.WorkflowUtil.createProcessor
import com.pharmdash.workflow.dp.DataProcessor
import com.pharmdash.workflow.dp.javadto.{DatasetDTO, NodeDTO, WorkflowConfigDTO, WorkflowDTO}
import com.pharmdash.workflow.dp.validation.ValidationPaths._
import com.pharmdash.workflow.dp.validation.exceptions._
import com.prospection.arch2.util.SerializableLogging

import java.util.Collections
import scala.collection.JavaConverters._
import scala.collection.mutable

object WorkflowMapper extends SerializableLogging {

    def nodeFromDTO(dto: NodeDTO, datasetDto: DatasetDTO): Node = {
        Node(dto.getId, dto.getName, processor(dto),
            dto.getShouldResume,
            dto.getSkippable,
            dto.getUpstreamNodesHash,
            Option(datasetDto).map(datasetFromDTO),
            dto
        )
    }

    def workflowFromDTO(dto: WorkflowDTO, applicationConfig: java.util.Map[String, String] = Collections.emptyMap()): Workflow = {
        assertFieldsNotNull(dto)

        // Keep the previous order since we need the original index for the front end to know which node is which.
        val nodes = dto.getNodes.asScala
            .map { node => nodeFromDTO(node, dto.findUpstreamDataset(node.getUpstreamDatasetId)) }
        val nodesById: Map[Long, Node] = nodes.map(node => node.id -> node).toMap

        // add source and sink channel links to each node
        var links = Seq[Link]()
        dto.getLinks.asScala.foreach { linkDTO =>
            if (!nodesById.contains(linkDTO.getSourceNodeId)) throw new WorkflowValidationException(linkPath(linkDTO), s"could not find source node with id=${linkDTO.getSourceNodeId} & channel=${linkDTO.getSourceChannel}")
            if (!nodesById.contains(linkDTO.getSinkNodeId)) throw new WorkflowValidationException(linkPath(linkDTO), s"could not find sink node with id=${linkDTO.getSinkNodeId} & channel=${linkDTO.getSinkChannel}")

            val source = nodesById(linkDTO.getSourceNodeId)
            val sink = nodesById(linkDTO.getSinkNodeId)

            val link = Link(linkDTO.getId, source, linkDTO.getSourceChannel, sink, linkDTO.getSinkChannel)

            source.outputLinks = source.outputLinks :+ link
            sink.inputLinks = sink.inputLinks :+ link
            links = links :+ link
        }

        Workflow(dto.getId, dto.getDescription, nodes, links.toList, workflowConfigFromDTO(dto.getConfig, applicationConfig.asScala.toMap), dto.isDryRun)
    }

    private def assertFieldsNotNull(dto: WorkflowDTO): Unit = {
        if (dto == null) {
            throw new WorkflowValidationException("Workflow cannot be empty")
        }
        if (dto.getLinks == null || dto.getLinks.isEmpty && dto.getNodes.size() > 1) {
            throw new WorkflowValidationException("Links cannot be empty")
        }
        if (dto.getNodes == null || dto.getNodes.isEmpty) {
            throw new WorkflowValidationException("Nodes cannot be empty")
        }
        if (dto.getConfig == null) {
            throw new WorkflowValidationException("Workflow config cannot be empty")
        }
        if (dto.getDatasets == null) {
            throw new WorkflowValidationException("Workflow datasets cannot be empty")
        }
    }

    private def workflowConfigFromDTO(dto: WorkflowConfigDTO, applicationConfig: Map[String, String] = Map.empty): WorkflowConfig = {
        val properties: mutable.Map[String, String] = mutable.Map()
        applicationConfig.foreach(entry => properties(entry._1) = entry._2)
        dto.getProperties.asScala.toMap.foreach(entry => properties(entry._1) = entry._2)
        WorkflowConfig(Some(dto.getRefDataVersionId), Some(dto.getRefDataManagementVersion), Some(dto.getRefDataPath), properties.toMap, Some(dto.getTerritoryFilePath))
    }

    private def datasetFromDTO(dto: DatasetDTO): WorkflowDataset = {
        val versions = Option(dto.getVersions).map(_.asScala.toSet).getOrElse(Set.empty)
        WorkflowDataset(dto.getId, dto.getName, dto.getSourcePath, dto.getScalingFactor, versions, Some(dto.getRecencyIncompleteMonths), Some(dto.getRecencyIncompleteMonthsForCensoring))
    }


    @throws(classOf[WorkflowInitException])
    private def processor(dto: NodeDTO): DataProcessor = {
        try {
            createProcessor(dto.getResource, s"${dto.getProperties.getValue}")
        } catch {
            case _: ClassNotFoundException => throw new DataProcessorNotFoundException(nodePath(dto), s"Processor ${dto.getResource} could not be found.")
            case jsonError: JsonMappingException =>
                val msg = s"Unable to create processor on node with id=${dto.getId} & name=${dto.getId} & resource=${dto.getResource} '$jsonError' value='${dto.getProperties.getValue}'"
                logger.error(msg, jsonError)
                throw new DataProcessorInitException(nodePath(dto), msg)
            case e: Throwable =>
                val msg = s"Unable to create the node with id=${dto.getId} & name=${dto.getName} & resource=${dto.getResource} '$e'"
                logger.error(msg, e)
                throw new WorkflowValidationException(nodePath(dto), msg)
        }
    }
}
