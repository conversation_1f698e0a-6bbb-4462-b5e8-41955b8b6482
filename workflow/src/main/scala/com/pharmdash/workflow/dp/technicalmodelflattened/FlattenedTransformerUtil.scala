package com.pharmdash.workflow.dp.technicalmodelflattened

import com.pharmdash.workflow.{Results, WorkflowContext}
import com.pharmdash.workflow.dp.meta.processorannotations.Channel
import org.apache.spark.sql.{Dataset, Row, SaveMode}

object FlattenedTransformerUtil {

    def getSubjectsAndEventsDatasets(inputData: Map[String, Results])(implicit context: WorkflowContext): (Dataset[Row], Dataset[Row]) = {
        val subjects = inputData(Channel.EVENTS).subjects.get

        val subjectsDataset = SubjectFlattenedTransformer.transform(subjects)

        val eventsDataset = EventFlattenedTransformer.transform(subjects)

        (subjectsDataset, eventsDataset)
    }

    def write(transformedSubjects: Dataset[Row], path: String): Unit = {
        transformedSubjects
            .write
            .mode(SaveMode.Overwrite)
            .options(Map("compression" -> "zstd"))
            .format("parquet")
            .save(path)
    }

}
