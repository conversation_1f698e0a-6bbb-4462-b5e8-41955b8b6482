package com.pharmdash.workflow.dp

import com.pharmdash.workflow.common.BetterJson.formats
import com.pharmdash.workflow.common.DateUtil
import com.pharmdash.workflow.common.TechnicalModelEncoders._
import com.pharmdash.workflow.dp.DataProcessor._
import com.pharmdash.workflow.dp.PersistenceProcessor.PersistenceDefinition.{NoDaysSupply, UseDaysSupplyAndDropOffAfterDaysSupply, UseDaysSupplyAndDropOffAtEventDate}
import com.pharmdash.workflow.dp.PersistenceProcessor._
import com.pharmdash.workflow.dp.meta._
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.PropertyType.{BOOLEAN, INTEGER, WORKFLOW_GROUP_SELECTOR}
import com.pharmdash.workflow.dp.meta.propertyannotations._
import com.pharmdash.workflow.model.{Event, Subject}
import com.pharmdash.workflow.{Results, WorkflowContext}
import org.apache.spark.sql.Dataset
import org.json4s.jackson.Serialization.write

import java.sql.Date

object PersistenceProcessor {

    final val PersistenceTag = "persistence"

    case class PersistenceStruct(definitionOfDropOff: Int,
                                 event: String,
                                 length: Int,
                                 censoredAfter: Boolean,
                                 streakNumber: Int,
                                 death: Boolean)

    private case class PersistenceEventInternalStruct(event: Event, group: String, streakEndDate: Date = null, lastEventDaysSupply: Int = 0, dropOffMonths: Int = 0, streak_number: Int = 0) {
        def isOverlapping(date: Date, dropOffDefinition: Int): Boolean = {
            DateUtil.distanceInDays(streakEndDate, date) < dropOffDefinition
        }

        def merge(eventToMerge: PersistenceEventInternalStruct): PersistenceEventInternalStruct = {
            val laterEndEvent = if (streakEndDate.compareTo(eventToMerge.streakEndDate) <= 0)
                eventToMerge
            else this

            this.copy(streakEndDate = laterEndEvent.streakEndDate, lastEventDaysSupply = laterEndEvent.lastEventDaysSupply)
        }
    }

    object PersistenceDefinition {
        final val NoDaysSupply = "Don’t use days supply"
        final val UseDaysSupplyAndDropOffAtEventDate = "Extend drop off definition by days supply, drop off at event date"
        final val UseDaysSupplyAndDropOffAfterDaysSupply = "Extend drop off definition by days supply, drop off at event date + days supply"
    }
}

@RequireItemGroupConfiguration
@Processor(ProcessorCategory.ALGORITHM)
@Name("Persistence Report Algorithm")
@Description("Add tags necessary for persistence report")
@Input(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT, tags = new Tags(eventTags = Array(PersistenceTag)))
class PersistenceProcessor(
                              @Many(WORKFLOW_GROUP_SELECTOR)
                              interestedEvents: Seq[String],

                              @Many(INTEGER)
                              dropOffMonths: Seq[Integer],

                              @Single
                              @Description(
                                  """
                                    <div><b>1. Don’t use days supply</b></div>
                                    <div>Drop off = Last event (Eg: if last event on 10th Jan, then calculated Drop off = 10th Jan)</div>
                                    <br>

                                    <div><b>2. Extend drop off definition by days supply, drop off at event date</b></div>
                                    <div>Algorithm will find the last event + days supply + drop off definition.</div>
                                    <div>Drop off = Last event. (Eg: if last event on 10th Jan, days supply 30 days and Drop off definition 2 months; calculated Drop off = 10th Jan)</div>
                                    <br>

                                    <div><b>3. Extend drop off definition by days supply, drop off at event date + days supply</b></div>
                                    <div>Algorithm will find the last event + days supply + drop off definition.</div>
                                    <div>Drop off = Last event + days supply (Eg: if last event on 10th Jan, days supply 30 days and Drop off definition 2 months; calculated Drop off = 9th Feb)</div>
                                    """
                              )
                              @Values(Array(NoDaysSupply, UseDaysSupplyAndDropOffAtEventDate, UseDaysSupplyAndDropOffAfterDaysSupply))
                              @Default(NoDaysSupply)
                              persistenceDefinition: String = NoDaysSupply,

                              @Description("The default algorithm overestimates persistence when initiation dates are near the reporting period's end, due to handling of patients with insufficient follow-up. To address this, we will censor patients if the time from event date to end date is less than the drop-off period, regardless of intervening events. This will reduce the persistence bias at the data's end. Please check <a target=\"_blank\" href=\"https://prospection.atlassian.net/l/cp/K8mXT38g\">this confluence page</a> for more information.")
                              @NotRequired
                              @Single(BOOLEAN) @Default("false") censorPatientsEventsBetweenEndOfDataAndDropOff: Boolean = false
                          )

    extends DataProcessor with Serializable {

    override def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {
        val subjects: Dataset[Subject] = inputData(EVENTS).subjects.get

        val persistenceCalculationStrategy: PersistenceCalculationStrategy =
            if (persistenceDefinition == UseDaysSupplyAndDropOffAtEventDate) new UseDaysSupplyAndDropOffAtEventDateStrategy
            else if (persistenceDefinition == UseDaysSupplyAndDropOffAfterDaysSupply) new UseDaysSupplyAndDropOffAfterDurationStrategy
            else new NoDaysSupplyStrategy

        val results = subjects.map(subject => {
            subject
                .events
                .filter(_.groups.intersect(interestedEvents).nonEmpty)
                .flatMap(event => event.groups
                    .filter(interestedEvents.contains(_))
                    .flatMap(group => {
                        val daysSupply = persistenceCalculationStrategy.getDaysSupply(event)
                        val streakEndDate = persistenceCalculationStrategy.getStreakEndDate(event)

                        Array(
                            PersistenceEventInternalStruct(event, group, streakEndDate = streakEndDate, lastEventDaysSupply = daysSupply),
                            PersistenceEventInternalStruct(event, MarketEventId, streakEndDate = streakEndDate, lastEventDaysSupply = daysSupply)
                        )
                    }))
                .groupBy(_.group)
                // calculate streaks for each event
                .flatMap { case (_, eventsOfGroup) => {
                    val sortedEvents = eventsOfGroup.sortBy(_.event.id)

                    dropOffMonths.flatMap(dropOffInMonths => {
                        val dropOffInDays = dropOffInMonths * 30

                        sortedEvents
                            .foldLeft[Array[PersistenceEventInternalStruct]](Array())((streaks, currentEvent) => {
                                if (streaks.length == 0) {
                                    Array(currentEvent)
                                } else {
                                    val lastStreak = streaks.last
                                    val dropOffDefinition = persistenceCalculationStrategy.getDropOffDefinition(dropOffInDays, lastStreak.lastEventDaysSupply)

                                    if (lastStreak.isOverlapping(currentEvent.event.date, dropOffDefinition)) {
                                        streaks(streaks.length - 1) = lastStreak.merge(currentEvent)
                                        streaks
                                    } else {
                                        // create new a streak starting from the current event
                                        streaks :+ currentEvent
                                    }
                                }
                            })
                            .zipWithIndex
                            .map { case (streak, index) => streak.copy(
                                dropOffMonths = dropOffInMonths,
                                streak_number = index
                            )
                            }
                    })
                }
                }.foreach(streak => {
                    val persistenceTagValue = streak.event.tags.getOrElse(PersistenceTag, Seq())

                    val censoredAfter = DateUtil.distanceInDays(streak.streakEndDate, subject.endOfData) < streak.dropOffMonths * 30
                    val deathWithInDropOff = if (subject.info.calculatedDateOfDeath != null)
                        DateUtil.distanceInDays(streak.streakEndDate, subject.info.calculatedDateOfDeath) < streak.dropOffMonths * 30
                    else
                        false

                    val length = getStreakLength(streak, subject.endOfData)

                    streak.event.tags = streak.event.tags + (PersistenceTag ->
                        (persistenceTagValue :+ write(
                            PersistenceStruct(
                                definitionOfDropOff = streak.dropOffMonths,
                                event = streak.group,
                                length = length,
                                censoredAfter = censoredAfter,
                                streakNumber = streak.streak_number,
                                death = deathWithInDropOff
                            ))))
                })

            subject
        })

        Map((EVENTS, inputData(EVENTS).replaceSubjects(results)))
    }

    trait PersistenceCalculationStrategy {
        def getDaysSupply(event: Event): Int

        def getStreakEndDate(event: Event): Date

        def getDropOffDefinition(dropOffInDays: Int, daysSupply: Int): Int
    }

    class NoDaysSupplyStrategy(implicit nodeId: String) extends PersistenceCalculationStrategy with Serializable {
        override def getDaysSupply(event: Event): Int = 0

        override def getStreakEndDate(event: Event): Date = event.date

        override def getDropOffDefinition(dropOffInDays: Int, daysSupply: Int): Int = dropOffInDays
    }

    class UseDaysSupplyAndDropOffAtEventDateStrategy(implicit nodeId: String) extends PersistenceCalculationStrategy with Serializable {
        override def getDaysSupply(event: Event): Int = extractDaysSupply(event)

        override def getStreakEndDate(event: Event): Date = event.date

        override def getDropOffDefinition(dropOffInDays: Int, daysSupply: Int): Int = dropOffInDays + daysSupply
    }

    class UseDaysSupplyAndDropOffAfterDurationStrategy(implicit nodeId: String) extends PersistenceCalculationStrategy with Serializable {
        override def getDaysSupply(event: Event): Int = extractDaysSupply(event)

        override def getStreakEndDate(event: Event): Date = DateUtil.addDays(event.date, getDaysSupply(event))

        override def getDropOffDefinition(dropOffInDays: Int, daysSupply: Int): Int = dropOffInDays
    }

    private def extractDaysSupply(event: Event)(implicit nodeId: String) = {
        val daysSupplyTag = event.tags.get("daysSupply")
        if (daysSupplyTag.isEmpty || daysSupplyTag.get.isEmpty || daysSupplyTag.get.head == null) {
            throw contextedException("daysSupply tag should exist for this persistence definition: " + persistenceDefinition)
                .addContextValue("eventId", event.id.toString)
        }
        daysSupplyTag.get.head.toInt
    }

    private def getStreakLength(streak: PersistenceEventInternalStruct, endOfData: Date): Int = {
        val cutOffDate = if (censorPatientsEventsBetweenEndOfDataAndDropOff)
            Option(DateUtil.addDays(endOfData, -streak.dropOffMonths * 30))
        else Option.empty

        val adjustedStreakEndDate = cutOffDate
            .map(cutOff => DateUtil.earlier(streak.streakEndDate, cutOff))
            .getOrElse(streak.streakEndDate)

        // Streak start date (or head event date) can be after the adjusted streak end date if censorPatientsEventsBetweenEndOfDataAndDropOff is true
        // and the streak start date is close to the endOfData (check the calculation of cutOffDate and adjustedStreakEndDate)
        // If so, we set the streak length to 0 here and censor after month 0 (through the normal censorAfter calculation outside this method).
        if (streak.event.date.compareTo(adjustedStreakEndDate) > 0) 0
        else DateUtil.distanceInWholeApproximatedMonths(streak.event.date, adjustedStreakEndDate)
    }

    override def getConsumedItemGroups(): Seq[String] = {
        interestedEvents
    }
}

