package com.pharmdash.workflow.dp.filter

import com.pharmdash.workflow.common.DateUtil
import com.pharmdash.workflow.common.TechnicalModelEncoders._
import com.pharmdash.workflow.dp.DataProcessor
import com.pharmdash.workflow.dp.javadto.ViolationDTO
import com.pharmdash.workflow.dp.meta.Description
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.{NotRequired, Single}
import com.pharmdash.workflow.model.{Event, Subject, SubjectInfo}
import com.pharmdash.workflow.{Results, ValidationContext, WorkflowContext}
import org.apache.spark.sql.Dataset

import java.time.YearMonth
import java.time.format.DateTimeParseException


@Processor(ProcessorCategory.FILTER)
@Description(
    "<p>" +
        "Delete all <i>events</i> outside of specified data range from each subject. If a subject has all of its events deleted, the subject will also be deleted." +
        "</p>" +
        "<p>" +
        "<i>startOfData, startOfDataset, endOfData and endOfDataset</i> will also be changed to the first day of the Start Month, or the last day of the End Month, respectively. Unless the startOfData is already later than Start Month, or the endOfData is already earlier than End Month." +
        "</p>" +
        "<p>" +
        "<b>WARNING:</b> This script alters subjects' medical history. The altered data may not be suited for some report types(e.g. Journey or ICSD)" +
        "</p>" +
        "<p>" +
        "Example: if Start Month is set to 2020-01, all events before 2020-01-01 will be delete, all events on or after 2020-01-01 will be preserved. Similarly, if End Month is set to 2020-01, all events after 2020-01-31 will be delete, all events on or before 2020-01-31 will be preserved." +
        "</p>" +
        "<p>" +
        "T minus notation here means <i>count backward x month from endOfDataset</i>. For example if endOfDataset is 2020-12, then T-1 means 2020-11, T-11 means 2020-01 and T-12 means 2019-12." +
        "</p>"
)
@Input(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
class DataRangeFilter(
                         @Single
                         @NotRequired
                         @Description("This field accepts both static month(YYYY-MM, e.g. \"2010-03\") and \"T-\" notation(e.g. \"T-3\"). If configured, events earlier than the first day of this month will be deleted. ")
                         startMonth: String = null,

                         @Single
                         @NotRequired
                         @Description("This field accepts both static month(YYYY-MM, e.g. \"2010-03\") and \"T-\" notation(e.g. \"T-3\"). If configured, events later than the last day of this month will be deleted.")
                         endMonth: String = null,
                     ) extends DataProcessor with Serializable {

    def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {
        val subjects: Dataset[Subject] = inputData(EVENTS).subjects.get
        if (subjects.isEmpty) {
            return inputData
        }

        val endOfDataset = YearMonth.from(subjects.first().endOfDataset.toLocalDate)

        val startYm = toYearMonth(startMonth, endOfDataset)
        val endYm = toYearMonth(endMonth, endOfDataset)

        val updatedSubjects = subjects
            .map(subject => {
                subject.copy(
                    info = updateStartAndEndOfData(startYm, endYm, subject.info),
                    events = subject.events.filter(isWithinBounds(_, startYm, endYm))
                )
            })
            .filter(_.events.nonEmpty)

        Map(EVENTS -> inputData(EVENTS).replaceSubjects(updatedSubjects))
    }

    def updateStartAndEndOfData(startYm: Option[YearMonth], endYm: Option[YearMonth], info: SubjectInfo): SubjectInfo = {
        var updated = info

        startYm
            .map(ym => DateUtil.toDate(ym.atDay(1)))
            .foreach(start =>
                updated = updated.copy(
                    startOfData = DateUtil.later(info.startOfData, start),
                    startOfDataset = DateUtil.later(info.startOfData, start),
                )
            )

        endYm
            .map(ym => DateUtil.toDate(ym.atEndOfMonth()))
            .foreach(end =>
                updated = updated.copy(
                    endOfData = DateUtil.earlier(info.endOfData, end),
                    endOfDataset = DateUtil.earlier(info.endOfData, end),
                )
            )

        updated
    }

    def isWithinBounds(e: Event, start: Option[YearMonth], end: Option[YearMonth]) = {
        def eventYearMonth = YearMonth.from(e.date.toLocalDate)
        (start.isEmpty || !start.get.isAfter(eventYearMonth)) &&
            (end.isEmpty || !end.get.isBefore(eventYearMonth))
    }

    def toYearMonth(monthStr: String, endOfDataset: YearMonth) = {
        Option(monthStr)
            .filter(!isBlank(_))
            .map(str =>
                if (isTMinusNotation(str)) {
                    def tMinusMonth = extractTMinusMonths(str)
                    endOfDataset.minusMonths(tMinusMonth)
                } else {
                    YearMonth.parse(str)
                }
            )
    }

    override def validate()(implicit context: ValidationContext): Seq[ViolationDTO] = {

        def violations: Seq[Option[ViolationDTO]] = Seq(
            startAndEndCannotBothBeBlank(),
            invalidMonthLiteral(startMonth, "Start Month is in invalid format. Refer to the field description for the acceptable format."),
            invalidMonthLiteral(endMonth, "End Month is in invalid format. Refer to the field description for the acceptable format."),
            endBeforeStart()
        )

        violations.flatten
    }

    def endBeforeStart()(implicit context: ValidationContext): Option[ViolationDTO] = {
        if (!isBlank(startMonth) && !isBlank(endMonth)) {
            if (isTMinusNotation(startMonth) && isTMinusNotation(endMonth) && extractTMinusMonths(startMonth) < extractTMinusMonths(endMonth)) {
                return Some(ViolationDTO.error(context.nodeId, "End Month is before Start Month."))
            }
            if (isYearMonthNotation(startMonth) && isYearMonthNotation(endMonth) && YearMonth.parse(startMonth).isAfter(YearMonth.parse(endMonth))) {
                return Some(ViolationDTO.error(context.nodeId, "End Month is before Start Month."))
            }
        }
        None
    }

    def invalidMonthLiteral(monthStr: String, errorMessage: String)(implicit context: ValidationContext): Option[ViolationDTO] = {
        if (!isBlank(monthStr) && invalidFormat(monthStr)) {
            Some(ViolationDTO.error(context.nodeId, errorMessage))
        } else {
            None
        }
    }

    def startAndEndCannotBothBeBlank()(implicit context: ValidationContext): Option[ViolationDTO] = {
        if (isBlank(startMonth) && isBlank(endMonth)) {
            Some(ViolationDTO.error(context.nodeId, "Start Month and End Month can't be empty at the same time"))
        } else {
            None
        }
    }

    def isBlank(str: String) = !Option(str).exists(_.trim.nonEmpty)

    def invalidFormat(str: String) = !isTMinusNotation(str) && !isYearMonthNotation(str)

    def isTMinusNotation(str: String) = "\\A[Tt]-(\\d+)\\z".r.findFirstMatchIn(str.trim).nonEmpty
    def extractTMinusMonths(str: String) = "\\A[Tt]-(\\d+)\\z".r.findFirstMatchIn(str.trim).get.group(1).toInt

    def isYearMonthNotation(str: String) = {
        try {
            YearMonth.parse(str)
            true
        } catch {
            case e: DateTimeParseException => false
        }
    }
}
