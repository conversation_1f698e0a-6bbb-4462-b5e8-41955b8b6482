package com.pharmdash.workflow.dp.exporter

import com.pharmdash.workflow.dp.DataProcessor
import com.pharmdash.workflow.dp.exporter.Checkpoint.{DataProfilesFolder, ExportsFolder}
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.PropertyType.BOOLEAN
import com.pharmdash.workflow.dp.meta.propertyannotations.{Default, NotRequired, Single}
import com.pharmdash.workflow.dp.meta.{Description, Name}
import com.pharmdash.workflow.model.Subject
import com.pharmdash.workflow.{DataProfile, Results, Tag, WorkflowContext}
import org.apache.spark.sql.{Dataset, SaveMode, SparkSession}

import scala.util.Try

object Checkpoint {
    val ExportsFolder = "exports"
    val DataProfilesFolder = "data-profiles"
}

@Processor(ProcessorCategory.EXPORT)
@Name("Checkpoint")
@Description("Add a checkpoint in the workflow so that you can check what the subjects look like at this point")
@Input(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
class Checkpoint(
                    @Single(BOOLEAN)
                    @NotRequired
                    @Default("false")
                    @Name("Generate upstream Event and Subject Tags to select in Reporting nodes")
                    writeDataProfile: Boolean = false,

                    @Single(BOOLEAN)
                    @NotRequired
                    @Default("false")
                    @Name("Override checkpoint to not be resumable")
                    overrideCheckpointNotResumable: Boolean = false
                )
    extends DataProcessor with Serializable {

    override def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {
        if (overrideCheckpointNotResumable) {
            // FIXME: why can't the workflow executor use this to determine resumability?
            // there is no business logic for it here;
            // it is just so that scala compiler doesn't remove it similar to checkpointAthenaUrlPlaceholder.
        }

        val node = context.node.get
        val checkpointExportsUrl = target(ExportsFolder, node.upStreamNodeHash)
        val dataProfileUrl = target(DataProfilesFolder, node.upStreamNodeHash)

        implicit val spark: SparkSession = context.spark

        // FIXME: Checkpoint execution logic is not cohesive
        // see WorkflowDTO for initial Checkpoint execution logic
        if (node.shouldResume) {
            Map((EVENTS, Results(Some(spark.read.parquet(checkpointExportsUrl)))))
        } else if (context.dryRun) {
            import spark.implicits._

            val subjects = inputData(EVENTS).subjects.get

            subjects
                .write
                .mode(SaveMode.Overwrite)
                .parquet(checkpointExportsUrl)

            val refreshedSubjects = spark.read.parquet(checkpointExportsUrl).as[Subject]
            if (writeDataProfile) {
                val subjectTags = toTagSeq(refreshedSubjects.map(_.info.tags))
                val eventTags = toTagSeq(refreshedSubjects.map(_.events).flatMap(_.map(_.tags)))

                spark.createDataset(Seq(DataProfile(subjectTags, eventTags)))
                    .write
                    .mode(SaveMode.Overwrite)
                    .json(dataProfileUrl)
            }

            Map(EVENTS -> inputData(EVENTS).replaceSubjects(refreshedSubjects))
        } else {
            // skip this node if not in dry run mode
            inputData
        }
    }

    def target(folder: String, upstreamNodeHash: String)(implicit context: WorkflowContext): String = {
        s"${context.applicationConfig.warehouseBasePath}/checkpoint/$folder/upstreamnodehash=$upstreamNodeHash"
    }

    def toTagSeq(tags: Dataset[Map[String, Seq[String]]] )(implicit spark: SparkSession): Seq[Tag] = {
        import spark.implicits._
        tags.flatMap(tag => tag.keys
            .map(key => Tag(key, determineType(tag(key).map(getType), returnEmptyWhenSeqIsEmpty = true))))
            .distinct().collect()
            .groupBy(_.tagName).values
            .map(tags => Tag(tags.head.tagName, determineType(tags.map(_.tagType))))
            .toSeq
    }

    def getType(tagValue: String): String = {
        if (Try { tagValue.toLong }.toOption.isDefined) {
            return "Long"
        }
        if (Try { tagValue.toDouble }.toOption.isDefined) {
            return "Double"
        }
        "String"
    }

    def determineType(types: Seq[String], returnEmptyWhenSeqIsEmpty: Boolean = false): String = {
        if (types.contains("String")) {
            return "String"
        }
        if (types.contains("Double")) {
            return "Double"
        }
        if (types.contains("Long")) {
            return "Long"
        }
        if (returnEmptyWhenSeqIsEmpty) {
            return "Empty"
        }
        "String"
    }
}
