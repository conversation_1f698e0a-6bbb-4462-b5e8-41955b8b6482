package com.pharmdash.workflow.dp.etl.us.forian.claimprovider

import com.pharmdash.workflow.dp.DataProcessor
import com.pharmdash.workflow.dp.config.ForianEtlConfig
import com.pharmdash.workflow.dp.etl.EtlUtils
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL.ForianDataset.ForianDataset
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL._
import com.pharmdash.workflow.dp.etl.us.forian.ForianEtlPaths
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.{Nested, Single}
import com.pharmdash.workflow.dp.meta.{Description, Name}
import com.pharmdash.workflow.{Results, WorkflowContext}
import org.apache.spark.sql.{Dataset, SaveMode}

@Processor(ProcessorCategory.ETL)
@Name("Forian Claim Provider ETL")
@Description("Read Forian Claim Provider data from S3")
class ForianClaimProviderProcessor(
                                      @Single @Nested(classOf[ForianEtlConfig])
                                      forianEtlConfig: ForianEtlConfig
                                  ) extends DataProcessor {
    /**
     * Process input data
     *
     * @param inputData input stream to process
     * @return map of streams
     */
    override def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {

        val etlParams = ForianETLParams(forianEtlConfig.getStartOfDataset, forianEtlConfig.getEndOfDataset, forianEtlConfig.getRawDataBucket, forianEtlConfig.getRawDatasourcesMap)

        val combinedBasePath = forianEtlConfig.getIntermediateOutputPath.getCombinedPath
        val prescribers = readPrescribersInput(combinedBasePath)

        val providerOpenOutputPath = s"${combinedBasePath}/${ForianEtlPaths.claimProviderSuffix}/${ForianEtlPaths.openSuffix}"
        processAndWriteProviderData(prescribers, ForianDataset.Open, providerOpenOutputPath, etlParams)

        // Process Closed Providers if required
        if (forianEtlConfig.getIncludeClosed) {
            val providerClosedOutputPath = s"${combinedBasePath}/${ForianEtlPaths.claimProviderSuffix}/${ForianEtlPaths.closedSuffix}"
            processAndWriteProviderData(prescribers, ForianDataset.Closed, providerClosedOutputPath, etlParams)
        }

        Map()
    }

    private def readPrescribersInput(basePath: String)(implicit context: WorkflowContext): Dataset[ForianPrescriber] = {
        import context.spark.implicits._

        val processedPrescribersPath = s"$basePath/${ForianEtlPaths.prescriberSuffix}"
        EtlUtils.readParquet(processedPrescribersPath)
            .as[ForianPrescriber]
    }

    private def processAndWriteProviderData(
                                               prescribers: Dataset[ForianPrescriber],
                                               dataType: ForianDataset,
                                               providerOutputPath: String,
                                               etlParams: ForianETLParams
                                           )(implicit context: WorkflowContext): Unit = {
        val readForianClaimProvider = context.dependencyProvider.readForianClaimProvider
        val providerData = readForianClaimProvider(prescribers, dataType, etlParams)

        providerData.write
            .mode(SaveMode.Overwrite)
            .parquet(providerOutputPath)
    }

}
