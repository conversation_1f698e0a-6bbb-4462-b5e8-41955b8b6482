package com.pharmdash.workflow.dp.etl.jp.desc.diagnosis

import com.pharmdash.workflow.WorkflowContext
import com.pharmdash.workflow.dp.etl.EtlUtils
import com.pharmdash.workflow.dp.etl.EtlUtils.{toSeqColNonNull, toSeqNonNull, toSeqNonNullUnknown}
import com.pharmdash.workflow.dp.etl.jp.desc.DescETL.Common.{DATE_FORMAT, LEFT_JOIN, TAB_SEPARATOR}
import com.pharmdash.workflow.dp.etl.jp.desc.DescETL.CommonColumns.{ClaimId, DiseasesCode, PatientId}
import com.pharmdash.workflow.dp.etl.jp.desc.DescETL.DescEquipmentBasicInfoColumns.DispensingType
import com.pharmdash.workflow.dp.etl.jp.desc.DescETL.DescPaths.{BASIC_INFO_PATH, C_DISEASE, M_ICD10}
import com.pharmdash.workflow.dp.etl.jp.desc.DescETL.DescSuspiciousFlag.{Confirmed, Suspected}
import com.pharmdash.workflow.dp.etl.jp.desc.DescETL.DiseaseColumns.{ClaimYm, DiagnosisStartYmd, SuspiciousFlag}
import com.pharmdash.workflow.dp.etl.jp.desc.DescETL.Icd10Columns.Icd10Code
import com.pharmdash.workflow.dp.etl.jp.desc.DescETL.OutputColumns.SubjectId
import com.pharmdash.workflow.dp.etl.jp.desc.DescETL.{Classifications, Common, CommonColumns, OutputColumns}
import com.pharmdash.workflow.dp.etl.jp.desc.DescEvent
import com.pharmdash.workflow.dp.etl.jp.desc.provider.DescProviderFacility
import com.pharmdash.workflow.dp.importer.SubjectBackwardCompatibilityHelper.EmptyObjectTagsColumn
import com.pharmdash.workflow.model.Event
import com.prospection.arch2.model.Category
import com.prospection.arch2.util.CommonEtlConstants.UNKNOWN
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.DoubleType
import org.apache.spark.sql.{Column, Dataset}

object DescDiagnosisReader {

    type ReadDescDiagnosis = (String, Dataset[DescProviderFacility]) => Dataset[DescEvent]

    def readDescDiagnosis(rawDataPath: String, providerFacility: Dataset[DescProviderFacility])(implicit context: WorkflowContext): Dataset[DescEvent] = {
        import context.spark.implicits._

        val diseaseDf = EtlUtils.readCsv(s"$rawDataPath/$C_DISEASE/", separator = TAB_SEPARATOR)
        val icd10Df = EtlUtils.readCsv(s"$rawDataPath/$M_ICD10/", separator = TAB_SEPARATOR).as("icd10")
        val basicInfoDf = EtlUtils.readCsv(s"$rawDataPath/$BASIC_INFO_PATH/", separator = TAB_SEPARATOR)

        val icd10GroupedDf = icd10Df.groupBy(DiseasesCode)
            .agg(
                collect_set(icd10Df(Icd10Code)).as(Event.Icd10CodeTag),
            )

        diseaseDf
            .join(icd10GroupedDf, diseaseDf(DiseasesCode) === icd10GroupedDf(DiseasesCode), LEFT_JOIN)
            .join(providerFacility, diseaseDf(ClaimId) === providerFacility(OutputColumns.ClaimId), LEFT_JOIN)
            .join(basicInfoDf, Seq(CommonColumns.ClaimId), Common.LEFT_JOIN)
            .select(
                diseaseDf(DiseasesCode).as(Event.CodeColumn),
                diseaseDf(PatientId).as(SubjectId),
                transformClaimDate(diseaseDf(ClaimYm)).as(Event.DateColumn),
                lit(null).cast(DoubleType).as(Event.AmountColumn),
                lit(null).cast(DoubleType).as(Event.CostColumn),
                lit(Category.Diagnosis).as(Event.CategoryColumn),
                lit(Classifications.Diagnosis).as(Event.ClassificationColumn),
                map(
                    lit(Event.ConditionConfirmTag), toSeqNonNullUnknown(transformSuspiciousFlag(diseaseDf(SuspiciousFlag))),
                    lit(Event.Icd10CodeTag), toSeqColNonNull(icd10GroupedDf(Event.Icd10CodeTag)),
                    lit(Event.BedCountTag), toSeqNonNullUnknown(providerFacility(Event.BedCountTag)),
                    lit(Event.InstitutionTypeTag), toSeqNonNullUnknown(providerFacility(Event.InstitutionTypeTag)),
                    lit(Event.HasCancerTreatmentBaseTag), toSeqNonNullUnknown(providerFacility(Event.HasCancerTreatmentBaseTag)),
                    lit(Event.DpcHospitalTag), toSeqNonNullUnknown(providerFacility(Event.DpcHospitalTag)),
                    lit(Event.SubRegionTag), toSeqNonNullUnknown(providerFacility(Event.SubRegionTag)),
                    lit(Event.ProviderSpecialtyTag), toSeqNonNullUnknown(providerFacility(Event.ProviderSpecialtyTag)),
                    lit(Event.FromDateTag), toSeqNonNull(to_date(diseaseDf(DiagnosisStartYmd), DATE_FORMAT)),
                    lit(Event.DispensingTypeTag), toSeqNonNullUnknown(basicInfoDf(DispensingType)),
                    lit(Event.HcoNoTag), toSeqNonNullUnknown(providerFacility(Event.HcoNoTag)),
                ).as(Event.TagsColumn),
                EmptyObjectTagsColumn
            ).as[DescEvent]

    }

    private def transformClaimDate(claimYm: Column): Column = {
        when(claimYm.isNotNull && !(claimYm <=> lit("")), to_date(concat(claimYm, lit("/01")), DATE_FORMAT))
            .otherwise(lit(null))
    }

    private def transformSuspiciousFlag(suspiciousFlag: Column): Column = {
        when(suspiciousFlag === "0", Confirmed)
            .when(suspiciousFlag === "1", Suspected)
            .otherwise(UNKNOWN)
    }
}
