package com.pharmdash.workflow.dp.importer

import com.pharmdash.workflow.common.TechnicalModelEncoders._
import com.pharmdash.workflow.dp.DataProcessor
import com.pharmdash.workflow.dp.config.Dataset
import com.pharmdash.workflow.dp.meta._
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.PropertyType.DATASET_SELECTOR
import com.pharmdash.workflow.dp.meta.propertyannotations.{Nested, Single}
import com.pharmdash.workflow.model.{Event, Subject, SubjectInfo, TagMetadata}
import com.pharmdash.workflow.{Results, WorkflowContext}
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.catalyst.ScalaReflection
import org.apache.spark.sql.types.StructType

@Processor(ProcessorCategory.IMPORT)
@Name("JSON Reader")
@Description("Read subjects from JSON files.")
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
class JsonSubjectImporter(@Single(DATASET_SELECTOR) @Nested(classOf[Dataset]) dataset: Dataset) extends DataProcessor {

    override def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {

        val subjects = context.spark.read
            .schema(ScalaReflection.schemaFor[Subject].dataType.asInstanceOf[StructType])
            .json(dataset.getSourcePath + "/" + dataset.getVersion)
            .as[Subject]

        Map((EVENTS, Results(Some(withMetadata(subjects)))))
    }

    private def withMetadata(subjects: org.apache.spark.sql.Dataset[Subject]): DataFrame = {
        val head = subjects.head()

        subjects
            .withColumn(
                Subject.InfoColumn,
                TagMetadata.toSubjectInfoColumnWithMetadata(
                    head.info.startOfDataset.toString,
                    head.info.endOfDataset.toString,
                    Map(
                        SubjectInfo.GenderTag -> TagMetadata(true)
                    )
                )
            )
            .withColumn(
                Subject.EventsColumn,
                TagMetadata.toEventTagsColumnWithMetadata(
                    Map(
                        Event.AgeTag -> TagMetadata(true),
                        Event.StateTag -> TagMetadata(true)
                    )
                )
            )
    }
}
