package com.pharmdash.workflow.dp

import com.pharmdash.workflow.common.TechnicalModelEncoders._
import com.pharmdash.workflow.dp.meta.Description
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.Single
import com.pharmdash.workflow.model.Subject
import com.pharmdash.workflow.{Results, WorkflowContext}
import org.apache.spark.sql.Dataset

@Processor(ProcessorCategory.TRANSFORMATION)
@Description("With the assumption that the target tag is a number, find the event tag with the min value and add that as a subject tag.")
@Input(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
class MinEventTagsToSubjectTagReducer(@Single tagName: String) extends DataProcessor {

    override def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {
        val subjects: Dataset[Subject] = inputData(EVENTS).subjects.get
        val results = subjects.map(subject => {
            val min = subject.events
                .flatMap(_.tags.fetch(tagName))
                .map(value => value.toLong)
                .minOption

            if (min.isDefined) {
                subject.info.replaceTag(tagName, min.get.toString)
            }

            subject
        })

        Map(EVENTS -> inputData(EVENTS).replaceSubjects(results))
    }

}
