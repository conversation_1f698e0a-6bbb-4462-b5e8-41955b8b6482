package com.pharmdash.workflow.dp.exporter

import com.pharmdash.workflow.dp.DataProcessor
import com.pharmdash.workflow.dp.meta._
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.PropertyType.{DOUBLE, INTEGER}
import com.pharmdash.workflow.dp.meta.propertyannotations.{NotRequired, Single}
import com.pharmdash.workflow.{Results, WorkflowContext}
import org.apache.spark.sql.SaveMode
import org.apache.spark.sql.functions.col

import javax.validation.constraints.NotBlank

@Deprecated
@Processor(ProcessorCategory.EXPORT)
@Name("Parquet Patient Writer")
@Description("Write the subjects in parquet format")
@Input(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
class ParquetExporter(@Single @NotBlank targetBucket: String,

                      @Name("Number of partitions") @Single(INTEGER) @NotRequired
                      @Description("WARNING: Don't touch if you dont know what it does.")
                      numPartitions: Option[BigInt] = None,

                      @Name("Partition by") @Single @NotRequired
                      @Description("WARNING: Don't touch if you dont know what it does.")
                      partitionBy: Option[String] = None
                     )
    extends DataProcessor with Serializable {

    override def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {

        var subject = inputData(EVENTS).subjects.get

        if (numPartitions.isDefined) {
            subject = subject.repartition(numPartitions.get.toInt)
        }

        if (partitionBy.exists(_.trim.nonEmpty)) {
            if (partitionBy.get.equals("id")) {
                subject = subject.repartition(col(partitionBy.get).substr(0, 4))
            } else {
                subject = subject.repartition(col(partitionBy.get))
            }
        }

        subject
            .write
            .mode(SaveMode.Overwrite)
            .parquet(targetBucket)

        Map()
    }
}
