package com.pharmdash.workflow.dp.etl.pbs

import com.pharmdash.workflow.common.DateUtil
import org.apache.spark.sql.Row
import org.apache.spark.sql.expressions.{MutableAggregationBuffer, UserDefinedAggregateFunction}
import org.apache.spark.sql.types._

import java.util.Date
import scala.collection.immutable.Map

class PbsPatientMostCommonAgg(
                            private val aggregationWindowInYears: Int
                        ) extends UserDefinedAggregateFunction {

    override def inputSchema: StructType =
        StructType(
            Seq(
                StructField("value", StringType),
                StructField("publishedDate", DateType)
            )
        )

    override def bufferSchema: StructType = StructType(
        StructField("dataMap", DataTypes.createMapType(DateType, StringType)) :: Nil
    )

    override def dataType: DataType = StringType

    override def deterministic: Boolean = true

    override def initialize(buffer: MutableAggregationBuffer): Unit = {
        buffer(0) = Map[Date, String]()
    }

    override def update(buffer: MutableAggregationBuffer, input: Row): Unit = {
        val inputValue = input.getAs[String](0)
        if (inputValue != null) {
            val dateValue = input.getAs[Date](1)
            val existingMap = buffer.getAs[Map[Date, String]](0)
            buffer(0) = existingMap + (dateValue -> inputValue)
        }
    }

    override def merge(buffer1: MutableAggregationBuffer, buffer2: Row): Unit = {
        val map1 = buffer1.getAs[Map[Date, String]](0)
        val map2 = buffer2.getAs[Map[Date, String]](0)
        buffer1(0) =  map1 ++ map2
    }

    override def evaluate(buffer: Row): String = {
        val dataMap = buffer.getAs[Map[Date, String]](0)
        if (dataMap.isEmpty) {
            return null
        }

        val lastDate = dataMap.keys.max
        val frequencyMap = collection.mutable.Map[String, Long]().withDefaultValue(0L)

        dataMap.toList.toStream
            .filter(record =>
                DateUtil.toLocalDate(record._1).isAfter(
                    DateUtil.toLocalDate(lastDate).minusYears(aggregationWindowInYears)
                )
            )
            .foreach(record => frequencyMap(record._2) += 1L)

        frequencyMap.maxBy(_._2)._1
    }
}

