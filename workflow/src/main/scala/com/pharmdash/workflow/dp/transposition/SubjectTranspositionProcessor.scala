package com.pharmdash.workflow.dp.transposition

import com.pharmdash.workflow.common.TechnicalModelEncoders._
import com.pharmdash.workflow.dp.DataProcessor
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.PropertyType._
import com.pharmdash.workflow.dp.meta.propertyannotations._
import com.pharmdash.workflow.dp.meta.{Description, Name}
import com.pharmdash.workflow.dp.transposition.SubjectTranspositionProcessor.DEFAULT_SUBJECT_ID_RETENTION_TAG
import com.pharmdash.workflow.dp.transposition.TagActionType.{Drop, Keep, TagActionType}
import com.pharmdash.workflow.model.{Event, Subject, SubjectInfo}
import com.pharmdash.workflow.{Results, WorkflowContext}
import com.prospection.arch2.util.WarehouseConfiguration
import org.apache.commons.lang3.StringUtils.isNotBlank

import java.sql.Date
import javax.validation.constraints.NotBlank

object SubjectTranspositionProcessor {
    final val DEFAULT_SUBJECT_ID_RETENTION_TAG = "patientId"
}

@Name("Subject Transposition")
@Processor(ProcessorCategory.TRANSFORMATION)
@Description(
    """
<em><b>**** EXPERIMENTAL ****</b></em>
<br /><br />
Transpose Subject ID with a value from an event tag
<br /><br />
<em><b>Note</b></em>, a single Subject can actually result in multiple new subjects being created, it's not a 1:1 mapping. For example
if we're transposing prescriber ID as the subject ID, rather than using patient ID, a patient <em>may</em> have more
than one prescriber. This results in a new Subject per prescriber in this scenario.
""")
@Input(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
class SubjectTranspositionProcessor(
                                       @Single(STRING)
                                       @NotBlank
                                       @Name("The name of the event tag to use for the new Subject ID")
                                       newSubjectIdTagFromEvent: String,

                                       @Single(STRING)
                                       @Default(DEFAULT_SUBJECT_ID_RETENTION_TAG)
                                       @Name("The name of the event tag to place the old subject ID in (Empty to not retain)")
                                       oldSubjectIdAsEventTag: String = DEFAULT_SUBJECT_ID_RETENTION_TAG,

                                       @Single
                                       @Name("Default Subject Tag Action")
                                       @Default("Drop")
                                       @Values(Array("Copy", "Drop", "Keep", "Move"))
                                       subjectTagDefaultAction: TagActionType = Drop,

                                       @Many
                                       @Nested(classOf[TagAction])
                                       @NotRequired
                                       @Name("Subject Tag Actions")
                                       @Description("Copy, Drop, Keep, or Move Tags to Events")
                                       subjectTagActionConfiguration: Seq[TagAction] = Seq(),

                                       @Single
                                       @Name("Default Event Tag Action")
                                       @Default("Keep")
                                       @Values(Array("Copy", "Drop", "Keep", "Move"))
                                       eventTagDefaultAction: TagActionType = Keep,

                                       @Many
                                       @Nested(classOf[TagAction])
                                       @NotRequired
                                       @Name("Event Tag Actions")
                                       @Description("Copy, Drop, Keep, or Move Tags to Subjects")
                                       eventTagActionConfiguration: Seq[TagAction] = Seq()
                                   ) extends DataProcessor with Serializable with WarehouseConfiguration {

    private val subjectTagActions = new TagActions(defaultAction = subjectTagDefaultAction, actions = subjectTagActionConfiguration)
    private val eventTagActions = new TagActions(defaultAction = eventTagDefaultAction, actions = eventTagActionConfiguration)

    override def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {

        // Don't break the dataprocessor test ...
        if(subjectTagDefaultAction == null) {}
        if(subjectTagActionConfiguration == null) {}
        if(eventTagDefaultAction == null) {}
        if(eventTagActionConfiguration == null) {}

        import context.spark.implicits._
        Map(EVENTS -> inputData(EVENTS).replaceSubjects(
            inputData(EVENTS).subjects.get
                .flatMap(s => transposeSubjectIdentity(s))
                // At this point, we could have multiple subjects with the same ID,
                // so we need to group them together into a single subject
                .groupByKey(subject => subject.id)
                .reduceGroups((s1, s2) => mergeSubjects(s1, s2))
                // Dataset is (key, subject) tuples, we just want subject
                .map(_._2)
        ))
    }

    private def transposeSubjectIdentity(sourceSubject: Subject): Iterable[Subject] = sourceSubject.events
        .filter(e => e.tags.contains(newSubjectIdTagFromEvent))
        .groupBy(e => e.tags(newSubjectIdTagFromEvent).head)
        // entry._1 is the new subject Id, entry._2 is the related events for the new subject ID
        .map(newSubjectIdAndEvents => createNewSubject(sourceSubject, newSubjectIdAndEvents._1, newSubjectIdAndEvents._2))

    private def createNewSubject(sourceSubject: Subject, newSubjectId: String, newSubjectEvents: Seq[Event]) = {
        // We need the updated / sorted events here to calculate the start / end data window
        val newSubjectEventsWithUpdatedTags = createNewSubjectEvents(sourceSubject, newSubjectEvents)

        val newSubjectInfo = createNewSubjectInfo(
            sourceSubject,
            // We also need the new events here, as we know they're date ordered ...
            newSubjectEventsWithUpdatedTags.head.date,
            newSubjectEventsWithUpdatedTags.last.date,
            // But we need the non-updated events here so we have access to the original tags
            newSubjectEvents
        )

        Subject(newSubjectId, newSubjectInfo, newSubjectEventsWithUpdatedTags)
    }

    private def createNewSubjectInfo(sourceSubject: Subject, startOfData: Date, endOfData: Date, newSubjectEvents: Seq[Event]) = {
        val newSubjectTags = determineNewSubjectTags(sourceSubject, newSubjectEvents)

        // TODO What else should we do with the SubjectInfo at this stage? It no longer makes sense as it will be
        //      specific to the original subject, not the new subject. For now, we leave it alone and ignore it
        SubjectInfo(
            // TODO These don't necessarily make sense anymore ... what to do with them?
            dateOfBirth = null,
            dateOfDeath = null,
            calculatedDateOfDeath = null,
            // TODO What do we do for these two fields (start / end of data for subject) ?
            startOfData = startOfData,
            endOfData = endOfData,
            startOfDataset = sourceSubject.info.startOfDataset,
            endOfDataset = sourceSubject.info.endOfDataset,
            tags = newSubjectTags
        )
    }

    private def determineNewSubjectTags(sourceSubject: Subject, newSubjectEvents: Seq[Event]): Map[String, Seq[String]] = {
        // First combine tags across all new subject events so we have a unique set of tags/values that we might want to
        // transpose into the subject tags
        val sourceEventTags = newSubjectEvents.map(e => e.tags)
            .reduce((t1, t2) => combineMapsDistinct(t1, t2))
            // Then filter that list down to only the tags that the workflow has configured to either move or copy
            // from events to subjects
            .filterKeys(tag => eventTagActions.shouldMigrateTag(tag))

        // Filter the list of original subject tags down to those the workflow has explicitly not dropped or moved to
        // events
        val sourceSubjectTags = sourceSubject.info.tags.filterKeys(tag => subjectTagActions.shouldRetainTag(tag))

        combineMapsDistinct(sourceSubjectTags, sourceEventTags)
    }

    private def createNewSubjectEvents(sourceSubject: Subject, eventsToUpdate: Seq[Event]): Seq[Event] = {
        // First, take all the subject tags that the workflow explicitly configured to be copied or moved from the
        // original subject into events
        val subjectTagsForEvents = sourceSubject.info.tags
            .filterKeys(tag => subjectTagActions.shouldMigrateTag(tag))

        // Then, create a new version of each event (otherwise we're side-effecting the original collection of events
        // by modifying their tags in-place)
        eventsToUpdate.map(event => {
            // Only keep event tags that the workflow did not explicitly configure to be dropped or moved
            val filteredEventTags = event.tags.filterKeys(tag => eventTagActions.shouldRetainTag(tag))
            val newEvent = Event(
                event.id, event.code, event.category, event.date, event.amount, event.value, event.unit, event.cost,
                event.groups, combineMapsDistinct(subjectTagsForEvents, filteredEventTags), event.objectTags, event.classification
            )

            // Also add the original subject ID as a tag on the event if required
            if (isNotBlank(oldSubjectIdAsEventTag)) {
                newEvent.addTag(oldSubjectIdAsEventTag, sourceSubject.id)
            }

            newEvent
        }).sortBy(e => (e.date, e.id))
    }

    private def mergeSubjects(s1: Subject, s2: Subject): Subject = {
        val combinedEvents = (s1.events ++ s2.events).sortBy(e => (e.date, e.id))
        Subject(
            s1.id,
            // TODO See earlier caveats about subject info values ...
            SubjectInfo(
                dateOfBirth = null,
                dateOfDeath = null,
                calculatedDateOfDeath = null,
                // TODO Don't know where we'd determine these from? For now, just use a window that expands to cater
                //      for all (original) subjects related to the new subject?
                startOfData = combinedEvents.head.date,
                endOfData = combinedEvents.last.date,
                // These remain unchanged as they're consistent across the dataset
                startOfDataset = s1.info.startOfDataset,
                endOfDataset = s1.info.endOfDataset,
                tags = combineMapsDistinct(s1.info.tags, s2.info.tags)
            ),
            combinedEvents
        )
    }

    /**
     * Combine the entries of two maps of sequences. Ensures that combined map entries contain only unique values.
     *
     * @param a The first [[Map]] of sequences
     * @param b The second [[Map]] of sequences
     * @tparam K The common [[Map]] key type
     * @tparam V The common [[Seq]] type values type for the map values
     * @return A combined [[Map]] of a and b, with only unique values for each mapped sequence
     */
    // See https://spin.atomicobject.com/2020/07/11/combining-maps-iterables-scala/
    private def combineMapsDistinct[K, V](a: Map[K, Seq[V]], b: Map[K, Seq[V]]): Map[K, Seq[V]] = {
        a ++ b.map { case (k, v) => k -> (v ++ a.getOrElse(k, Iterable.empty)).distinct }
    }

    def getNewSubjectIdTagFromEvent: String = newSubjectIdTagFromEvent
    def getOldSubjectIdAsEventTag: String = oldSubjectIdAsEventTag
    def getSubjectTagActions: TagActions = subjectTagActions
    def getEventTagActions: TagActions = eventTagActions
}
