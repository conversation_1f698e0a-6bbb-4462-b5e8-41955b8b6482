package com.pharmdash.workflow.dp.etl.pbs

import java.sql.Date

case class PbsPatientTag(patientId: String, therapyArea: String, abbvieCareTag: String, publishedDate: Date)

object PbsPatientTag {
    val PatientId = "SOURCE_PAT_ID"
    val PatientSex = "PATIENT_SEX"
    val PatientYearOfBirth = "PATIENT_YEAR_OF_BIRTH"
    val PatientYearOfDeath = "PATIENT_YEAR_OF_DEATH"
    val PublishedDate = "PUBLISHED_DATE"
}


