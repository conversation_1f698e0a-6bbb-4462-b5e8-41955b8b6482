package com.pharmdash.workflow.dp

import com.pharmdash.workflow.common.TechnicalModelEncoders._
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.PropertyType.INTEGER
import com.pharmdash.workflow.dp.meta.propertyannotations._
import com.pharmdash.workflow.dp.meta.{Description, Name}
import com.pharmdash.workflow.model.Subject
import com.pharmdash.workflow.{Results, WorkflowContext}
import org.apache.spark.sql.Dataset

@Processor(ProcessorCategory.TRANSFORMATION)
@Description("With the assumption that the target tag is a number, finds the first event tag that matches the given condition and add that as subject tag. " +
    "Note operator 'IN' can accept a list of values.")
@Input(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
class FirstMatchingEventTagAsSubjectTagTagger(
                                                 @Single
                                                 tagName: String,
                                                 @Single @Default("<") @Values(Array("<", ">", "in"))
                                                 operator: String,
                                                 @Many(INTEGER) @Name("Value(s)")
                                                 values: Seq[Integer]
                                             ) extends DataProcessor {

    override def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {
        val subjects: Dataset[Subject] = inputData(EVENTS).subjects.get
        val results = subjects.map(subject => {
            val matching = subject.events
                .map(_.tags.fetch(tagName))
                .filter(_.nonEmpty)
                .find(tagValues => {
                    operator match {
                        case "<" => tagValues(0).toLong < values(0)
                        case ">" => tagValues(0).toLong > values(0)
                        case "in" => values.contains(tagValues(0).toLong)
                        case _ => throw contextedException(s"Invalid operator $operator.")
                    }
                })

            if (matching.isDefined) {
                subject.info.replaceTag(tagName, matching.get(0))
            }

            subject
        })

        Map(EVENTS -> inputData(EVENTS).replaceSubjects(results))
    }

}
