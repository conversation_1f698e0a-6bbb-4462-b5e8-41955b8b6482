package com.pharmdash.workflow.dp.etl.nz.moh

import com.pharmdash.workflow.dp.etl.nz.moh.MohClaimReader.{oneOf, toDate_MMyyyy, toDate_dd_MM_yyyy}
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.{col, lit}

case class LabSource(
                        new_mast_enc_nhi: String,
                        claim_id: String,
                        lab_test: String,
                        visit_date: String,
                        test_type: String,
                        test_description: String
                    )

object LabSource {

    val CLASSIFICATION = "MoH Lab"

    val path = "lab/"

    object Csv {
        val SubjectId: Array[String] = Array("new_mast_enc_nhi", "NEW_MAST_ENC", "new_master_encrypted_hcu_id")
        val Code = "lab_test"
        val Date = "visit_date"
        val TestType = "test_type"
        val TestDescription = "test_description"

        // partition columns
        val Published = "published"
        val FileExtract = "file_extract"

    }

    def standard(dataFrame: DataFrame): DataFrame = {
        dataFrame
            .drop(Csv.Published, Csv.FileExtract)
            .distinct()
            .select(
            oneOf(dataFrame, Csv.SubjectId, StandardMOHColumnHeaders.SubjectId),
            lit(null).as(StandardMOHColumnHeaders.Gender),
            lit(null).as(StandardMOHColumnHeaders.Ethnicity),
            lit(null).as(StandardMOHColumnHeaders.DateOfBirth),
            lit(null).as(StandardMOHColumnHeaders.DateOfDeath),
            lit(null).as(StandardMOHColumnHeaders.EventId),
            lit(null).as(StandardMOHColumnHeaders.Category),
            lit(CLASSIFICATION).as(StandardMOHColumnHeaders.Classification),
            col(Csv.Code).as(StandardMOHColumnHeaders.Code),
            toDate_dd_MM_yyyy(col(Csv.Date)).as(StandardMOHColumnHeaders.Date),
            lit(null).as(StandardMOHColumnHeaders.Amount),
            lit(null).as(StandardMOHColumnHeaders.Value),
            lit(null).as(StandardMOHColumnHeaders.Cost),
            lit(null).as(StandardMOHColumnHeaders.Tag.Age),
            lit(null).as(StandardMOHColumnHeaders.Tag.Location),
            lit(null).as(StandardMOHColumnHeaders.Tag.DaysSupply),
            lit(null).as(StandardMOHColumnHeaders.Tag.Strength),
            lit(null).as(StandardMOHColumnHeaders.Tag.ReferrerId),
            lit(null).as(StandardMOHColumnHeaders.Tag.PrescriberId),
            lit(null).as(StandardMOHColumnHeaders.Tag.RepeatSequence),

            lit(null).as(StandardMOHColumnHeaders.Tag.StartDate),
            lit(null).as(StandardMOHColumnHeaders.Tag.EndDate),
            lit(null).as(StandardMOHColumnHeaders.Tag.Facility),
            lit(null).as(StandardMOHColumnHeaders.Tag.FacilityType),
            lit(null).as(StandardMOHColumnHeaders.Tag.HealthSpecialityCode),

            lit(null).as(StandardMOHColumnHeaders.Tag.DatetimeOfDeparture),
            lit(null).as(StandardMOHColumnHeaders.Tag.DatetimeOfEventEnd),
            lit(null).as(StandardMOHColumnHeaders.Tag.DatetimeOfFirstContact),
            lit(null).as(StandardMOHColumnHeaders.Tag.DatetimeOfPresentation),
            lit(null).as(StandardMOHColumnHeaders.Tag.DatetimeOfService),

            lit(null).as(StandardMOHColumnHeaders.Tag.AccidentFlag),
            lit(null).as(StandardMOHColumnHeaders.Tag.IcuHours),
            lit(null).as(StandardMOHColumnHeaders.Tag.HoursOnVentilation),
            lit(null).as(StandardMOHColumnHeaders.Tag.PatientClinicalComplexityLevel),
            lit(null).as(StandardMOHColumnHeaders.Tag.ComplicationComorbidityLevel),
            lit(null).as(StandardMOHColumnHeaders.Tag.DiagnosticRelatedGroup),
            lit(null).as(StandardMOHColumnHeaders.Tag.DrgGrouperType),

            lit(null).as(StandardMOHColumnHeaders.Tag.Morphology),
            lit(null).as(StandardMOHColumnHeaders.Tag.BasisOfDiagnosis),
            lit(null).as(StandardMOHColumnHeaders.Tag.TumourGrade),
            lit(null).as(StandardMOHColumnHeaders.Tag.ExtentOfDisease),
            lit(null).as(StandardMOHColumnHeaders.Tag.Laterality),

            col(Csv.TestType).as(StandardMOHColumnHeaders.Tag.TestType),
            col(Csv.TestDescription).as(StandardMOHColumnHeaders.Tag.TestDescription)

        )
    }
}
