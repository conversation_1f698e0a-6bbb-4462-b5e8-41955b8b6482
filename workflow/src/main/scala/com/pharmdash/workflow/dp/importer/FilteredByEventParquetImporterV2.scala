package com.pharmdash.workflow.dp.importer

import com.pharmdash.workflow.common.TechnicalModelEncoders._
import com.pharmdash.workflow.dp.config.DatasetPartition
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.{EVENTS, REFERENCE_DATA}
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.PropertyType.{BOOLEAN, DATASET_SELECTOR_V2, INTEGER}
import com.pharmdash.workflow.dp.meta.propertyannotations.{Default, Nested, NotRequired, Single}
import com.pharmdash.workflow.dp.meta.{Description, Name}
import com.pharmdash.workflow.dp.{DataProcessor, GroupEnricher}
import com.pharmdash.workflow.model.{Subject, SubjectDistinctEventCodesMetadata, TagMetadata}
import com.pharmdash.workflow.{Results, WorkflowContext}
import com.prospection.arch2.model.FieldConstants
import org.apache.spark.sql.functions.col

@Deprecated // In V2, subjects and events were filtered by codes only, whrereas in V3, subjects and events are filtered by classification-code pairs
@Processor(ProcessorCategory.IMPORT)
@Name("Dataset Import")
@Description("Import subjects who have the given reference data. \n" +
    "Filter to included groups (faster) or include all events when analysing associated events.")
@Input(name = Channel.REFERENCE_DATA, streamType = DataStreamType.REF_DATA)
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
class FilteredByEventParquetImporterV2(@Single(DATASET_SELECTOR_V2)
                                       @Nested(classOf[DatasetPartition])
                                       datasetPartition: DatasetPartition,

                                       @Single(BOOLEAN)
                                       @NotRequired
                                       @Default("false")
                                       includeAllEvents: Boolean = false,

                                       @Single(BOOLEAN)
                                       @NotRequired
                                       @Default("false")
                                       @Name("Skip Item Group Tagging")
                                       @Description("Skip Item Group Tagging so we can use Item Group Tagger Node later or to split Technical Model into smaller TA")
                                       skipGroupEnrichment: Boolean = false,

                                       @Single(INTEGER)
                                       @NotRequired
                                       @Name("Number of partitions")
                                       @Description("WARNING: Don't touch if you dont know what it does.")
                                       numPartitions: Option[BigInt] = None)
    extends DataProcessor with Serializable {

    def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {

        val path = datasetPartition.getCombinedPath
        val refData = inputData(REFERENCE_DATA).refData.get

        val codes = refData
            .filter(col("target").equalTo("event.code"))
            .collect()
            .flatMap(r => r.codes)
            .toSet // Set improve filtering computing time by 50% Set.contains is O(1)/constant

        /* TODO PDSN-610 delete below */
        var origin = context.spark.read.parquet(path)
        /* end deletion */

        val useSubjectDistinctEventCodesForFiltering = SubjectDistinctEventCodesMetadata.isObsoleteDistinctEventCodesEnabled(origin) || SubjectDistinctEventCodesMetadata.isEnabled(origin)

        if (useSubjectDistinctEventCodesForFiltering) {
            throw contextedException("Replace your Dataset Import V2 node with the V3. Your dataset contains subject-level distinctEventCodes that can be handled by the V3 and onwards.")
        }

        origin = SubjectBackwardCompatibilityHelper.generateMissingFields(origin)

        var subjects = origin.as[Subject]

        if (numPartitions.isDefined) {
            subjects = subjects.repartition(numPartitions.get.toInt)
        }

        subjects = subjects.filter(subject => subject.events.exists(e => codes.contains(e.code)))

        val result = subjects
            .map(subject => {
                Subject(
                    subject.id,
                    subject.info,
                    subject.events
                        .filter(e => includeAllEvents || codes.contains(e.code) || e.code == FieldConstants.DeathCode), // Keep actual death event for later analysis.
                    subject.distinctEventCodesByClassification
                )
            })

        val output = Map(EVENTS -> Results(Some(TagMetadata.keepMetadata(subjects, result))))

        if (skipGroupEnrichment) {
            output
        } else {
            // Combine group enricher with data set selector.
            val inputToGroupEnricher = output + (REFERENCE_DATA -> Results(Some(refData)))

            new GroupEnricher().process(inputToGroupEnricher)
        }
    }
}
