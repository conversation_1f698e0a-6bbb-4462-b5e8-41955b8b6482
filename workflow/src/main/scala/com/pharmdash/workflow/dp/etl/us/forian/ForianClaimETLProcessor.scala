package com.pharmdash.workflow.dp.etl.us.forian

import com.pharmdash.workflow.common.DateUtil
import com.pharmdash.workflow.dp.DataProcessor
import com.pharmdash.workflow.dp.config.{ForianEtlConfig, VersionedPath}
import com.pharmdash.workflow.dp.etl.EtlUtils
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL._
import com.pharmdash.workflow.dp.etl.us.forian.SubjectNetworkTagStruct.SubjectNetworkTagAgg
import com.pharmdash.workflow.dp.etl.us.forian.ValueFrequencyMap.MostValueFromFrequencyMapsAgg
import com.pharmdash.workflow.dp.importer.SubjectBackwardCompatibilityHelper.NullDistinctEventCodesValue
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.PropertyType._
import com.pharmdash.workflow.dp.meta.propertyannotations.{Nested, NotRequired, Single}
import com.pharmdash.workflow.dp.meta.{Description, Name}
import com.pharmdash.workflow.model._
import com.pharmdash.workflow.{Results, WorkflowContext}
import com.prospection.arch2.util.CommonETLtUtils.{getDateWithoutTimestamp, mapToTags}
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._

import java.sql.Date
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

@Processor(ProcessorCategory.ETL)
@Name("Forian Claims ETL Processor")
@Description("Technical model documentation <a href='https://prospection.atlassian.net/l/cp/qX1sMrHG'>link</a>." +
    "<p>" +
    "Reads the outputs of the following Forian ETL processors, unions them together and outputs Technical model:" +
    "</p>" +
    "<ul>" +
    "<li><b>ForianClaimPatientProcessor</b></li>" +
    "<li><b>ForianPharmacyProcessor</b></li>" +
    "<li><b>ForianClaimProcedureProcessor</b></li>" +
    "<li><b>ForianClaimDiagnosisProcessor</b></li>" +
    "<li><b>ForianClaimHeaderProcessor</b></li>" +
    "</ul>"
)
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
class ForianClaimETLProcessor(
                                 @Single
                                 @Nested(classOf[ForianEtlConfig])
                                 forianEtlConfig: ForianEtlConfig,
                                 @Single(INTEGER) @NotRequired partitions: Integer = null
                             ) extends DataProcessor {

    private val mostDateFromFrequencyMapAgg = new MostValueFromFrequencyMapsAgg[Date](DateType)
    private val mostStringFromFrequencyMap = new MostValueFromFrequencyMapsAgg[String](StringType)
    private val subjectNetworkTagAgg = new SubjectNetworkTagAgg

    override def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {
        import context.spark.implicits._

        val intermediateOutputPath = forianEtlConfig.getIntermediateOutputPath

        // read from intermediate output
        val claimPatient = readSubjects(buildProcessedSubjectInputPath(intermediateOutputPath, ForianEtlPaths.claimPatientSuffix))

        val (pharmacySubject, pharmacyEvent) = readSubjectsAndEvents(intermediateOutputPath, ForianEtlPaths.pharmacySuffix)

        val (procedureSubject, procedureEvent) = readSubjectsAndEvents(intermediateOutputPath, ForianEtlPaths.claimProcedureSuffix)

        val (diagnosisSubject, diagnosisEvent) = readSubjectsAndEvents(intermediateOutputPath, ForianEtlPaths.claimDiagnosisSuffix)

        val claimHeaderSubject = readSubjects(buildProcessedSubjectInputPath(intermediateOutputPath, ForianEtlPaths.claimHeaderSuffix))

        val startOfData = forianEtlConfig.getStartOfDataset
        val endOfData = forianEtlConfig.getEndOfDataset

        // prepare subject level data
        val subjects = claimPatient
            .unionByName(pharmacySubject)
            .unionByName(claimHeaderSubject)
            .unionByName(procedureSubject)
            .unionByName(diagnosisSubject)
            .groupBy(CommonColumns.PatientId)
            .agg(
                struct(
                    mostDateFromFrequencyMapAgg(removeDateAfterEndOfData(endOfData)(col(SubjectInfo.DateOfBirthColumn)))
                        .as(SubjectInfo.DateOfBirthColumn),
                    max(col(SubjectInfo.DateOfDeathColumn)).as(SubjectInfo.DateOfDeathColumn),
                    // calculatedDateOfDeath will be filled after events are consolidated into subjects
                    typedLit[Date](null).as(SubjectInfo.CalculatedDateOfDeathColumn),
                    typedLit(getDateWithoutTimestamp(startOfData)).as(SubjectInfo.StartOfDataColumn),
                    typedLit(getDateWithoutTimestamp(endOfData)).as(SubjectInfo.EndOfDataColumn),
                    typedLit(getDateWithoutTimestamp(forianEtlConfig.getStartOfDataset)).as(SubjectInfo.StartOfDatasetColumn),
                    typedLit(getDateWithoutTimestamp(forianEtlConfig.getEndOfDataset)).as(SubjectInfo.EndOfDatasetColumn),
                    mapToTags(
                        List(NetworkTag, RawDataSourceTag),
                        coalesce(mostStringFromFrequencyMap(col(SubjectInfo.GenderTag)), lit(StandardGender.Unknown)) -> SubjectInfo.GenderTag,
                        subjectNetworkTagAgg(col(NetworkTag)) -> NetworkTag,
                        array_distinct(flatten(collect_list(col(RawDataSourceTag)))) -> RawDataSourceTag
                    ).as(SubjectInfo.TagColumn)
                ).as(Subject.InfoColumn)
            )

        // prepare event level data
        var events = pharmacyEvent
            .unionByName(procedureEvent)
            .unionByName(diagnosisEvent)

        // DO NOT use our repartition function here because we always want to repartition by CommonColumns.PatientId here,
        // so that we can sort within partition in the next step.
        events = if (partitions == null) events.repartition(col(CommonColumns.PatientId)) else events.repartition(partitions.toInt, col(CommonColumns.PatientId))

        val sortedEvents = events
            .sortWithinPartitions(col(CommonColumns.PatientId), col(Event.DateColumn))
            .withColumn(Event.IdColumn, monotonically_increasing_id())
            .groupBy(CommonColumns.PatientId)
            .agg(
                collect_list(
                    struct(
                        col(Event.IdColumn),
                        col(Event.CodeColumn),
                        col(Event.DateColumn),
                        col(Event.AmountColumn),
                        lit(null).cast(DoubleType).as(Event.ValueColumn),
                        lit(null).cast(StringType).as(Event.UnitColumn),
                        col(Event.CostColumn),
                        typedLit(Array.empty[String]).as(Event.GroupsColumn),
                        col(Event.CategoryColumn),
                        col(Event.ClassificationColumn),
                        col(Event.TagsColumn),
                        col(Event.ObjectTagsColumn)
                    )
                ).as(Subject.EventsColumn),
                NullDistinctEventCodesValue.as(Subject.DistinctEventCodesColumn)
            )

        // join subjects with events to form technical model
        val technicalModel = subjects
            .join(sortedEvents, Seq(CommonColumns.PatientId), "inner")
            .select(
                col(CommonColumns.PatientId).as(Subject.IdColumn),
                col(Subject.InfoColumn),
                col(Subject.EventsColumn),
                col(Subject.DistinctEventCodesColumn)
            )
            .as[Subject]
            .map(subject => {
                subject.events = subject.events.sortBy(_.id)
                subject.events.foreach(event => {
                    if (subject.dateOfBirth.isDefined) {
                        val dateOfBirth = subject.dateOfBirth.get
                        val age = event.distanceFrom(dateOfBirth, ChronoUnit.YEARS)
                        if (age >= 0)
                            event.addTag(Event.AgeTag, age.toString)
                    }
                })
                subject
            })
            .map(subject => {
                val updated = subject.copy(info = subject.info.copy())
                updated.info.addTag(SubjectInfo.FirstDispensingDateTag, startOfData)
                updated.events
                    .find(_.classification.equals(Classifications.Dispensing))
                    .map(firstEvent => DateTimeFormatter.ISO_LOCAL_DATE.format(DateUtil.toLocalDate(firstEvent.date)))
                    .foreach(updated.replaceTag(SubjectInfo.FirstDispensingDateTag, _))
                // events always in start, end dataset range, so just check events and DOB, DOD
                updated.info.startOfData = DateUtil.later(updated.events.head.date, updated.dateOfBirth.orNull)
                updated.info.endOfData = DateUtil.later(updated.events.last.date, updated.info.dateOfDeath)
                updated
            })
            .map(EtlUtils.addCalculatedDateOfDeath)
            .withColumn(Subject.InfoColumn, TagMetadata.toSubjectInfoColumnWithMetadata(forianEtlConfig.getStartOfDataset, forianEtlConfig.getEndOfDataset))
            .withColumn(Subject.EventsColumn, TagMetadata.toEventTagsColumnWithMetadata(Map.empty))
            .as[Subject]

        Map((EVENTS, Results(Some(technicalModel))))
    }

    private def readSubjectsAndEvents(intermediateOutputPath: VersionedPath, pathSuffix: String)(implicit context: WorkflowContext): (Dataset[ForianSubject], Dataset[ForianEvent]) = {
        val subjectPath = buildProcessedSubjectInputPath(intermediateOutputPath, pathSuffix)
        val eventPath = buildProcessedEventInputPath(intermediateOutputPath, pathSuffix)
        (readSubjects(subjectPath), readEvents(eventPath))
    }

    private def buildProcessedSubjectInputPath(intermediateOutputPath: VersionedPath, pathSuffix: String): String = {
        s"${intermediateOutputPath.getCombinedPath}/$pathSuffix/${ForianEtlPaths.subjectSuffix}"
    }

    private def buildProcessedEventInputPath(intermediateOutputPath: VersionedPath, pathSuffix: String): String = {
        s"${intermediateOutputPath.getCombinedPath}/$pathSuffix/${ForianEtlPaths.eventSuffix}"
    }

    private def readSubjects(path: String)(implicit context: WorkflowContext): Dataset[ForianSubject] = {
        import context.spark.implicits._
        EtlUtils.readParquet(path).as[ForianSubject]
    }

    private def readEvents(path: String)(implicit context: WorkflowContext): Dataset[ForianEvent] = {
        import context.spark.implicits._
        EtlUtils.readParquet(path).as[ForianEvent]
    }

    private def removeDateAfterEndOfData(endOfData: String): UserDefinedFunction = {
        val endOfDataDate = getDateWithoutTimestamp(endOfData)
        udf[Map[Date, Int], Map[Date, Int]]((frequencies: Map[Date, Int]) => {
            if (frequencies == null) Map[Date, Int]()
            else frequencies.toList
                .filter { case (k, _) => endOfDataDate.compareTo(k) >= 0 }
                .toMap
        })
    }

}

