package com.pharmdash.workflow.dp

import com.pharmdash.workflow.{Results, WorkflowContext, WorkflowDataset}
import com.pharmdash.workflow.dp.ProviderAttributeLayerProcessor.{EventWithProvider, EventWithProviders, Mechanisms, PalStructV2, PalTag, Provider, extractProvidersFromEventTags}
import com.pharmdash.workflow.dp.meta._
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.PropertyType._
import com.pharmdash.workflow.dp.meta.propertyannotations._
import com.pharmdash.workflow.model.Event.{PrescriberIdTag, PrescriberSpecialtyTag}
import com.pharmdash.workflow.model.{Event, EventsMetadata, Subject}
import org.json4s.JsonAST.{<PERSON><PERSON><PERSON>, J<PERSON><PERSON>, JString}
import org.json4s.jackson.Serialization.{read, write}
import org.json4s.{CustomSerializer, DefaultFormats, Formats}

import scala.collection.mutable.ListBuffer

object ProviderAttributeLayerProcessor {
    final val PalTag = "pal"

    // TODO: remove once usage in other places get removed as well
    case class PalStruct(specialty: String,
                         mostRecentProviderId: String,
                         mostFrequentProviderId: String) {
        override def toString: String = write(this)
    }

    final val MechanismKey = "mechanism"
    final val SpecialtyKey = "specialty"
    final val ProviderIdKey = "providerId"
    final val PalStructV2FixedFields = Set(MechanismKey, SpecialtyKey, ProviderIdKey)

    implicit val formats: Formats = DefaultFormats + PalStructV2Serializer

    case class PalStructV2(
                              // better use Enum
                              mechanism: String,
                              specialty: String,
                              providerId: String,
                              eventTags: Map[String, String]
                          ) {
        def toJson: String = write(this)
    }

    def readPalStructV2(json: String): PalStructV2 = {
        read[PalStructV2](json)
    }

    case object PalStructV2Serializer extends CustomSerializer[PalStructV2](_ => (
        {
            case x: JObject =>
                PalStructV2(
                    (x \ MechanismKey).extract[String],
                    (x \ SpecialtyKey).extract[String],
                    (x \ ProviderIdKey).extract[String],
                    x.obj.filter(field => !PalStructV2FixedFields.contains(field._1))
                        .map(field => {
                            val value = field._2 match {
                                case JString(v) => v
                                case _ => null
                            }
                            field._1 -> value
                        })
                        .filter(_._2 != null)
                        .toMap
                )
            case _ => null
        },
        {
            case value: PalStructV2 =>
                JObject(JField(MechanismKey, JString(value.mechanism)) ::
                    JField(SpecialtyKey, JString(value.specialty)) ::
                    JField(ProviderIdKey, JString(value.providerId)) ::
                    value.eventTags.map(tag => JField(tag._1, JString(tag._2))).toList)
        }
    ))

    object Mechanisms extends Enumeration {
        type Mechanism = Value
        val MostRecent: Mechanisms.Value = Value(0, "mostRecent")
        val MostFrequent: Mechanisms.Value = Value(1, "mostFrequent")
        val MostRecentAndFrequent: Mechanisms.Value = Value(2, "mostRecentAndFrequent")
        val Direct: Mechanisms.Value = Value(3, "direct")
    }

    @throws(classOf[IllegalArgumentException])
    def extractProvidersFromEventTags(event: Event, useSingleProviderForEvent: Boolean = false): Seq[Provider] = {
        val (prescriberIds, prescriberSpecialties) = if (useSingleProviderForEvent) {
            Seq(event.getTagSafe(PrescriberIdTag).headOption.orNull) -> Seq(event.getTagSafe(PrescriberSpecialtyTag).headOption.orNull)
        } else {
            event.getTagSafe(PrescriberIdTag) -> event.getTagSafe(PrescriberSpecialtyTag)
        }

        if (prescriberIds.size != prescriberSpecialties.size) {
            throw new IllegalArgumentException("Please pick the latest data Or Check 'Use Single Provider For Event' option. Your dataset contains Event having multiple providers but PrescriberId tag doesn't align with PrescriberSpecialty tag: having different size.")
        }

        prescriberIds.zip(prescriberSpecialties)
            .map { case (prescriberId, specialty) => Provider(prescriberId, specialty) }
            .filter(_.isValid)
    }

    case class Provider(id: String, specialty: String) {
        val isValid: Boolean = id != null && id != "Unknown"
    }

    case class EventWithProviders(event: Event, providers: Seq[Provider]) {
        val isValid: Boolean = providers != null && providers.nonEmpty

        def isWithinLookBackPeriod(indexEvent: Event, timePeriodInMonth: Int): Boolean = {
            event.beforeOrSameday(indexEvent) && event.daysUntil(indexEvent.date) <= timePeriodInMonth * 30
        }

        def flattenToSingleProvider: Seq[EventWithProvider] = providers.map(EventWithProvider(event, _))
    }

    case class EventWithProvider(event: Event, provider: Provider)
}

@Processor(ProcessorCategory.ALGORITHM)
@Name("Provider Attribute Layer Algorithm")
@Description("Add tags necessary for provider attribute layer report")
@Input(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT, tags = new Tags(eventTags = Array(PalTag)))
class ProviderAttributeLayerProcessor(
                                         @Single(INTEGER)
                                         @Default("12")
                                         timePeriodInMonth: Int,
                                         @Many(STRING) specialties: List[String],
                                         @NotRequired @Many(STRING) eventTagsExportInPalTag: List[String],
                                         @NotRequired @Single(BOOLEAN) @Default("false")  @Description("Use Single Provider For Event - In case the dataset doesn't support multiple providers correctly")
                                         useSingleProviderForEvent: Boolean = false
                                     )
    extends SubjectProcessor with Serializable {

    override def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {
        import context.spark.implicits._

        val processResult = super.process(inputData)

        // Set metadata to indicate that PAL data is available
        val eventsMetadata = EventsMetadata(hasPalData = true)
        val updatedSubjects = EventsMetadata.saveEventsMetadata(processResult(EVENTS).subjects.get, eventsMetadata)
        val subjectResults = Results(Some(updatedSubjects.as[Subject]))

        Map(EVENTS -> subjectResults)
    }

    def eventTagsExportInPalTag(): List[String] = eventTagsExportInPalTag

    override def processOneSubject(subject: Subject)(implicit workflowDataset: Option[WorkflowDataset], nodeId: String): Subject = {
        val specialtiesSet = specialties.toSet
        val eventsForPal = subject.events
            .map(event => {
                val providers = try {
                    extractProvidersFromEventTags(event, useSingleProviderForEvent)
                        .filter(provider => specialtiesSet.contains(provider.specialty))
                } catch {
                    case e: IllegalArgumentException => throw contextedException(e)
                }
                EventWithProviders(event, providers)
            })
            .filter(_.isValid)

        if (eventsForPal.nonEmpty) {
            subject.events.foreach(event => {
                val palStructs = eventsForPal
                    .filter(_.isWithinLookBackPeriod(event, timePeriodInMonth))
                    .flatMap(_.flattenToSingleProvider)
                    .groupBy(_.provider.specialty)
                    .toSeq
                    .map(_._2)
                    .flatMap(createPalTags) ++
                    createDirectPalTags(event)

                event.tags = event.tags + (PalTag -> palStructs)
            })
        }
        subject
    }

    def getMostRecentProviders(events: Seq[EventWithProvider]): Seq[EventWithProvider] = {
        events
            .groupBy(_.event.date)
            // get all event within latest date
            .maxBy(_._1)._2
            // then pick first event on the date for each provider id
            .groupBy(_.provider.id)
            .toSeq
            .map(_._2.head)
            .sortBy(_.provider.id) // to make sure result is deterministic
    }

    def getMostFrequentProviders(events: Seq[EventWithProvider]): Seq[EventWithProvider] = {
        events
            .groupBy(_.provider.id)
            .toSeq
            .groupBy(_._2.size)
            // get the list of providerIds (and their events) which are most frequent
            .maxBy(_._1)._2
            .map(_._2)
            // pick the latest event for each most frequent provider id
            .map(_.maxBy(_.event.date))
            .sortBy(_.provider.id) // to make sure result is deterministic
    }

    def createPalTags(events: Seq[EventWithProvider]): List[String] = {
        val palTags = new ListBuffer[String]
        val mostRecentProviders = getMostRecentProviders(events)
        val mostFrequentProviders = getMostFrequentProviders(events)
        palTags ++= createPalTag(mostRecentProviders, Mechanisms.MostRecent)
        palTags ++= createPalTag(mostFrequentProviders, Mechanisms.MostFrequent)
        palTags ++= createPalTag(overlappingProviders(mostRecentProviders, mostFrequentProviders), Mechanisms.MostRecentAndFrequent)
        palTags.toList
    }

    def createPalTag(events: Seq[EventWithProvider], mechanism: Mechanisms.Mechanism): Seq[String] = {
        events
            .map {
                case EventWithProvider(event, Provider(id, specialty)) =>
                    val exportTags = if (eventTagsExportInPalTag.nonEmpty) {
                        eventTagsExportInPalTag.map(tag => createExportTag(tag, event))
                            .filter(_._2 != null)
                            .toMap
                    } else Map.empty[String, String]

                    PalStructV2(
                        mechanism.toString,
                        specialty,
                        id,
                        exportTags
                    )
            }
            .map(_.toJson)
    }

    def createDirectPalTags(event: Event): Seq[String] = {
        val providers = extractProvidersFromEventTags(event, useSingleProviderForEvent)

        val exportTags = eventTagsExportInPalTag.map(tag => createExportTag(tag, event))
            .filter(_._2 != null)
            .toMap

        providers.map { case Provider(id, specialty) =>
            PalStructV2(
                Mechanisms.Direct.toString,
                specialty,
                id,
                exportTags
            )
        }.map(_.toJson)
    }

    def overlappingProviders(a: Seq[EventWithProvider], b: Seq[EventWithProvider]): Seq[EventWithProvider] = {
        val bProviderIds = b.map(_.provider.id).toSet
        a.filter(withProvider => bProviderIds.contains(withProvider.provider.id))
    }

    def createExportTag(tag: String, event: Event): (String, String) = {
        // This functionality is to include geolocations alongside the pal providers, for example state, zipcode or sales territory.
        // Since it looks at the geolocation on a state, usually there is only one value rather support multiple value atm
        val value = event.getTagSafe(tag).headOption.orNull
        (tag, value)
    }
}
