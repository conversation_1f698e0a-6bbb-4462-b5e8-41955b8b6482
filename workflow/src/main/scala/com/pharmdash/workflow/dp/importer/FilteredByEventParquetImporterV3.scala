package com.pharmdash.workflow.dp.importer

import com.pharmdash.workflow.common.TechnicalModelEncoders._
import com.pharmdash.workflow.dp.config.DatasetPartition
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.{EVENTS, REFERENCE_DATA}
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.PropertyType.{BOOLEAN, DATASET_SELECTOR_V2, INTEGER}
import com.pharmdash.workflow.dp.meta.propertyannotations.{Default, Nested, NotRequired, Single}
import com.pharmdash.workflow.dp.meta.{Description, Name}
import com.pharmdash.workflow.dp.{DataProcessor, GroupEnricher}
import com.pharmdash.workflow.model.{Subject, SubjectDistinctEventCodesMetadata, TagMetadata}
import com.pharmdash.workflow.{Results, WorkflowContext}
import com.prospection.arch2.model.FieldConstants
import org.apache.spark.sql.functions.col

@Processor(ProcessorCategory.IMPORT)
@Name("Dataset Import")
@Description("Import subjects who have the given reference data. \n" +
    "Filter to included groups (faster) or include all events when analysing associated events.")
@Input(name = Channel.REFERENCE_DATA, streamType = DataStreamType.REF_DATA)
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
class FilteredByEventParquetImporterV3(@Single(DATASET_SELECTOR_V2)
                                       @Nested(classOf[DatasetPartition])
                                       datasetPartition: DatasetPartition,

                                       @Single(BOOLEAN)
                                       @NotRequired
                                       @Default("false")
                                       includeAllEvents: Boolean = false,

                                       @Single(BOOLEAN)
                                       @NotRequired
                                       @Default("false")
                                       @Name("Skip Item Group Tagging")
                                       @Description("Skip Item Group Tagging so we can use Item Group Tagger Node later or to split Technical Model into smaller TA")
                                       skipGroupEnrichment: Boolean = false,

                                       @Single(INTEGER)
                                       @NotRequired
                                       @Name("Number of partitions")
                                       @Description("WARNING: Don't touch if you dont know what it does.")
                                       numPartitions: Option[BigInt] = None)
    extends DataProcessor with Serializable {

    def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {

        val path = datasetPartition.getCombinedPath
        val refData = inputData(REFERENCE_DATA).refData.get

        val codesByClassification = refData
            .filter(col("target").equalTo("event.code"))
            .collect()
            .groupBy(_.classification)
            .mapValues(_.flatMap(_.codes).toSet)

        val codeAndClassificationTuples = codesByClassification
            .map{case (classification, codes) => codes.map((classification, _))}
            .flatten
            .toSet

        /* TODO PDSN-610 delete below */
        var origin = context.spark.read.parquet(path)
        /* end deletion */

        if (SubjectDistinctEventCodesMetadata.isObsoleteDistinctEventCodesEnabled(origin)) {
            throw contextedException("Use the latest dataset. Your dataset is not compatible with the current Dataset Import node.")
        }

        origin = SubjectBackwardCompatibilityHelper.generateMissingFields(origin)

        val useSubjectDistinctEventCodesForFiltering = SubjectDistinctEventCodesMetadata.isEnabled(origin)

        if (useSubjectDistinctEventCodesForFiltering) {
            // TODO: change to arrays_overlap(distinctEventCodesByClassification, ${codes.mkString("('", "','", "')")})
            //   when we upgrade to Spark 3.x
            val filterStatement = codesByClassification.map {
                case (classification, codesOfClassification) => s"exists(distinctEventCodesByClassification['$classification'], code -> code IN ${codesOfClassification.mkString("('", "','", "')")})"
            }
                .mkString(" or ")

            logger.info(s"Filter statement created for distinctEventCodes: $filterStatement")

            origin = origin.filter(filterStatement)
        }

        var subjects = origin.as[Subject]

        if (numPartitions.isDefined) {
            subjects = subjects.repartition(numPartitions.get.toInt)
        }

        if (!useSubjectDistinctEventCodesForFiltering) {
            subjects = subjects.filter(subject => subject.events.exists(e => codeAndClassificationTuples.contains((e.classification, e.code))))
        }

        val result = subjects
            .map(subject => {
                Subject(
                    subject.id,
                    subject.info,
                    subject.events
                        .filter(e => includeAllEvents || codeAndClassificationTuples.contains((e.classification, e.code)) || e.code == FieldConstants.DeathCode), // Keep actual death event for later analysis.
                    subject.distinctEventCodesByClassification
                )
            })

        val output = Map(EVENTS -> Results(Some(TagMetadata.keepMetadata(subjects, result))))

        if (skipGroupEnrichment) {
            output
        } else {
            // Combine group enricher with data set selector.
            val inputToGroupEnricher = output + (REFERENCE_DATA -> Results(Some(refData)))

            new GroupEnricher().process(inputToGroupEnricher)
        }
    }
}
