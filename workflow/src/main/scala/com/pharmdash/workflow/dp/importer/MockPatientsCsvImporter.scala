package com.pharmdash.workflow.dp.importer

import com.pharmdash.workflow.dp.DataProcessor
import com.pharmdash.workflow.dp.config.Dataset
import com.pharmdash.workflow.dp.importer.MockPatientsCsvImporter.{NonTagColumns, RequiredColumns}
import com.pharmdash.workflow.dp.importer.SubjectBackwardCompatibilityHelper.{EmptyObjectTagsColumn, NullDistinctEventCodesValue}
import com.pharmdash.workflow.dp.meta._
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.PropertyType._
import com.pharmdash.workflow.dp.meta.propertyannotations._
import com.pharmdash.workflow.model._
import com.pharmdash.workflow.{Results, WorkflowContext}
import org.apache.spark.sql
import org.apache.spark.sql.Column
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._

import javax.validation.constraints.NotBlank

object MockPatientsCsvImporter {
    private val RequiredColumns = Seq("subjectId", "event", "date")
    private val NonTagColumns = Seq(
        "subjectId",
        SubjectInfo.DateOfBirthColumn,
        SubjectInfo.DateOfDeathColumn,
        SubjectInfo.CalculatedDateOfDeathColumn,
        SubjectInfo.StartOfDataColumn,
        SubjectInfo.EndOfDatasetColumn,
        "event",
        Event.CategoryColumn,
        Event.DateColumn,
        Event.AmountColumn,
        Event.ValueColumn,
        Event.UnitColumn,
        Event.CostColumn,
        Event.GroupsColumn,
        Event.ClassificationColumn
    )
}

@Processor(ProcessorCategory.IMPORT)
@Name("Adhoc Subjects Reader")
@Description("Read subjects from csv file on S3. This reader is used for testing purposes.")
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
class MockPatientsCsvImporter(@Single(DATASET_SELECTOR) @Nested(classOf[Dataset]) dataset: Dataset,
                              @Single(PropertyType.S3_LOCATION) @NotBlank path: String,
                              @Single @NotBlank @Description("yyyy-MM-dd") startOfDataset: String,
                              @Single @NotBlank @Description("yyyy-MM-dd") endOfDataset: String,
                              @Single(BOOLEAN) @Name("Case Insensitive (Transform to lower case)")
                              @Default("false") caseSensitive: Boolean = false,
                              @Many @NotRequired subjectTags: Seq[String] = Seq.empty
                             ) extends DataProcessor {

    override def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {
        import context.spark.implicits._

        // DO NOT DELETE
        // The processor itself does not use dataset, but the reports requires dataset to create data transformers.
        // Therefore, as the transactional data provider this node must have a "dataset" field.
        // However, "clever" scala compiler cleans up the field from the object if the field is not used.
        // This non-used expression below is to prevent the clean up from happening.
        dataset.getSourcePath + dataset.getVersion

        val sourceDf = context.spark.read
            .option("header", "true")
            .option("ignoreLeadingWhiteSpace", "true")
            .option("ignoreTrailingWhiteSpace", "true")
            .csv(path)
        validateSubjectsWithNullDateOrEvent(sourceDf)
        var sourceDfWithInSensitiveCaseColumn = sourceDf
        var requiredColumns = RequiredColumns
        var nonTagColumns = NonTagColumns
        var subjectTagsTransformed = subjectTags
        if (caseSensitive) {
            sourceDfWithInSensitiveCaseColumn = sourceDfWithInSensitiveCaseColumn.toDF(sourceDf.columns map (_.toLowerCase): _*)
            requiredColumns = requiredColumns.map(_.toLowerCase)
            nonTagColumns = nonTagColumns.map(_.toLowerCase)
            subjectTagsTransformed = subjectTags.map(_.toLowerCase)
        }

        val columnNames = sourceDfWithInSensitiveCaseColumn.columns
        if (requiredColumns.exists(!columnNames.contains(_))) {
            throw contextedException("Required column(s) not found.")
                .addContextValue("Required fields", requiredColumns.mkString(", "))
                .addContextValue("Fields provided", columnNames.mkString(", "))
        }

        def colWithDefault(colName: String, defaultValue: String): Column = {
            if (!columnNames.contains(colName)) {
                lit(defaultValue)
            } else {
                col(colName)
            }
        }

        def groupsCol(): Column = {
            if (columnNames.contains(Event.GroupsColumn)) {
                split(col(Event.GroupsColumn), "\\|")
            } else {
                array('event)
            }
        }

        val subjects = sourceDfWithInSensitiveCaseColumn
            .withColumn("rowNumber", monotonically_increasing_id())
            .sort("subjectid", "date", "rowNumber")
            .groupBy('subjectid.as(Subject.IdColumn))
            .agg(
                struct(
                    first(colWithDefault(if (caseSensitive) SubjectInfo.DateOfBirthColumn.toLowerCase else SubjectInfo.DateOfBirthColumn, "1980-01-01")).cast(DateType).as(SubjectInfo.DateOfBirthColumn),
                    first(colWithDefault(if (caseSensitive) SubjectInfo.DateOfDeathColumn.toLowerCase else SubjectInfo.DateOfDeathColumn, null)).cast(DateType).as(SubjectInfo.DateOfDeathColumn),
                    first(colWithDefault(if (caseSensitive) SubjectInfo.CalculatedDateOfDeathColumn.toLowerCase else SubjectInfo.CalculatedDateOfDeathColumn, null)).cast(DateType).as(SubjectInfo.CalculatedDateOfDeathColumn),
                    first(colWithDefault(if (caseSensitive) SubjectInfo.StartOfDataColumn.toLowerCase else SubjectInfo.StartOfDataColumn, startOfDataset)).cast(DateType).as(SubjectInfo.StartOfDataColumn),
                    first(colWithDefault(if (caseSensitive) SubjectInfo.EndOfDataColumn.toLowerCase else SubjectInfo.EndOfDataColumn, endOfDataset)).cast(DateType).as(SubjectInfo.EndOfDataColumn),
                    lit(startOfDataset).cast(DateType).as(SubjectInfo.StartOfDatasetColumn),
                    lit(endOfDataset).cast(DateType).as(SubjectInfo.EndOfDatasetColumn),
                    mapToTags(
                        columnNames
                            .filter(c => !nonTagColumns.contains(c) && subjectTagsTransformed.contains(c))
                            .map(c => c -> first(col(c)))
                    ).as(SubjectInfo.TagColumn)
                ).as(Subject.InfoColumn),
                collect_list(
                    struct(
                        'rowNumber.as(Event.IdColumn),
                        'event.as(Event.CodeColumn),
                        colWithDefault(Event.CategoryColumn, "Mock up").as(Event.CategoryColumn),
                        'date.cast(DateType).as(Event.DateColumn),
                        colWithDefault(Event.AmountColumn, null).cast(IntegerType).as(Event.AmountColumn),
                        colWithDefault(Event.ValueColumn, null).cast(DoubleType).as(Event.ValueColumn),
                        colWithDefault(Event.UnitColumn, null).cast(StringType).as(Event.UnitColumn),
                        colWithDefault(Event.CostColumn, null).cast(DoubleType).as(Event.CostColumn),
                        groupsCol().as(Event.GroupsColumn),
                        colWithDefault(Event.ClassificationColumn, "PBS Item").as(Event.ClassificationColumn),
                        mapToTags(
                            columnNames
                                .filter(c => !nonTagColumns.contains(c) && !subjectTagsTransformed.contains(c))
                                .map(c => c -> col(c))
                        ).as(Event.TagsColumn),
                        EmptyObjectTagsColumn
                    )
                ).as("events")
            )
            .withColumn(Subject.DistinctEventCodesColumn, NullDistinctEventCodesValue)
            .as[Subject]
            .map(subject => {
                subject.events = subject.events.sortBy(event => (event.date, event.id))
                subject
            })
            .withColumn(Subject.InfoColumn, TagMetadata.toSubjectInfoColumnWithMetadata(
                startOfDataset,
                endOfDataset,
                subjectTagsMetadata
            ))
            .withColumn(Subject.EventsColumn, TagMetadata.toEventTagsColumnWithMetadata(eventTagsMetadata))
            .as[Subject]

        Map((EVENTS, Results(Some(subjects))))
    }

    private val eventTagsMetadata = Map(
        Event.AgeTag -> TagMetadata(true),
        Event.StateTag -> TagMetadata(true)
    )

    private val subjectTagsMetadata = Map(
        SubjectInfo.GenderTag -> TagMetadata(true)
    )

    private def mapToTags(tags: Seq[(String, Column)]): Column = {
        if (tags.isEmpty) {
            typedLit(Map.empty[String, Seq[String]])
        } else {
            map(tags.flatMap(tag =>
                Array(lit(tag._1), toSeqNonNull(tag._2.cast(StringType)))
            ): _*)
        }
    }

    private def validateSubjectsWithNullDateOrEvent(subjects: sql.DataFrame)(implicit nodeId: String): Unit = {
        val subjectsWithNullDateOrEvent = subjects.filter(col(Event.DateColumn).isNull || col("event").isNull).select("subjectId").distinct().collectAsList()
        if (!subjectsWithNullDateOrEvent.isEmpty) {
            throw contextedException(s"Found total ${subjectsWithNullDateOrEvent.size()} subjects having null date or event")
                .addContextValue("Subject IDs", s"${if (subjectsWithNullDateOrEvent.size() > 10) subjectsWithNullDateOrEvent.subList(0, 10) else subjectsWithNullDateOrEvent}...")
        }
    }

    val toSeqNonNull: UserDefinedFunction = udf(notNullSeq(_: String))

    def notNullSeq(string: String): Array[String] = {
        if (string != null && string.nonEmpty) Array(string) else Array.empty[String]
    }
}
