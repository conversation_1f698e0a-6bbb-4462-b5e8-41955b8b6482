package com.pharmdash.workflow.dp.etl.us.forian.claimprovider

import com.pharmdash.workflow.dp.etl.us.forian.ClaimProviderStateStruct.{ClaimProviderStateStructAgg, ProviderStateColumn}
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL.ForianDataset.ForianDataset
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL._
import com.pharmdash.workflow.dp.etl.us.forian.function.ClaimProviderCollector
import com.pharmdash.workflow.dp.etl.us.forian.function.ProviderFunctions.ProvidersColumn
import com.pharmdash.workflow.dp.etl.us.prescriber.{NpiCsv, NuccCsv}
import com.pharmdash.workflow.model.Event
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{Column, DataFrame, Dataset, SparkSession}

import java.sql.Date
import scala.util.Try

object ForianClaimProviderReader {

    type ReadForianClaimProvider = (Dataset[ForianPrescriber], ForianDataset, ForianETLParams) => Dataset[ForianClaimProvider]

    private val claimProviderStateStructAgg = new ClaimProviderStateStructAgg
    private val collectProviders = udaf(new ClaimProviderCollector)

    private object ProviderColumns {
        val State = "STATE"
        val Zip = "Zip"
        val ProviderNpi = "PROVIDER_NPI"
        val EntityTypeCode = "ENTITY_TYPE_CODE"
        val ProviderRole = "PROVIDER_ROLE"
        val ReceivedDate = "RECEIVED_DATE"
    }

    def readClaimProvider(
                             prescribers: Dataset[ForianPrescriber],
                             dataType: ForianDataset,
                             etlParams: ForianETLParams
                         )(implicit spark: SparkSession): Dataset[ForianClaimProvider] = {
        import spark.implicits._

        val claimProviders = loadClaimProviders(etlParams, dataType)
        if (claimProviders.isEmpty) {
            return Seq.empty[ForianClaimProvider].toDS()
        }
        joinProviderStateWithPrescriber(prescribers, claimProviders)
    }

    private def joinProviderStateWithPrescriber(prescribers: Dataset[ForianPrescriber], claimProviders: List[DataFrame])
                                               (implicit spark: SparkSession): Dataset[ForianClaimProvider] = {
        import spark.implicits._

        val claimProviderDf = claimProviders.reduce(_ unionByName _)
        val claimProviderStateDf = aggregateClaimProviderState(claimProviderDf)
        val claimProviderPrescriberDf = toClaimProviderPrescriber(claimProviderDf, prescribers)

        claimProviderStateDf
            .join(claimProviderPrescriberDf, Seq(CommonColumns.ClaimNumber), "outer")
            .select(
                CommonColumns.ClaimNumber,
                ProviderStateColumn,
                ProvidersColumn
            ).as[ForianClaimProvider]
    }

    private def loadClaimProviders(etlParams: ForianETLParams, dataType: ForianDataset)
                                  (implicit spark: SparkSession): List[DataFrame] = {
        val bucket = etlParams.bucket

        etlParams.rawDatasources.flatMap { case (datasource, path) =>
            datasource match {
                case ForianDataSource.Legacy =>
                    Some(readClaimProviders(s"$bucket/$path/${getDirClaimProviderLegacy(dataType)}"))
                case ForianDataSource.BiMonthly if dataType == ForianDataset.Open =>
                    Some(readClaimProviders(s"$bucket/$path/${Subdirectories.BiMonthlyClaimProvider}"))
                case _ => None
            }
        }.toList
    }

    private def aggregateClaimProviderState(claimProviders: DataFrame): DataFrame = {
        claimProviders
            .filter(col(ProviderColumns.State).isNotNull.and(col(ProviderColumns.State).notEqual("")))
            .groupBy(col(CommonColumns.ClaimNumber))
            .agg(
                claimProviderStateStructAgg(
                    col(ProviderColumns.ProviderRole),
                    col(Event.StateTag),
                    col(Event.ZipTag),
                    col(ProviderColumns.ReceivedDate),
                    col(CommonColumns.PublishedDate)
                ).as(ProviderStateColumn)
            )
    }

    private def toClaimProviderPrescriber(claimProviders: DataFrame, prescribers: Dataset[ForianPrescriber]): DataFrame = {
        claimProviders
            .filter(col(ProviderColumns.ProviderNpi).isNotNull.and(col(ProviderColumns.ProviderNpi).notEqual("")))
            .drop(Event.StateTag, Event.ZipTag, ProviderColumns.ReceivedDate, CommonColumns.PublishedDate)
            .join(
                broadcast(prescribers.drop(Event.StateTag)),
                col(ProviderColumns.ProviderNpi).equalTo(col(NpiCsv.Output.Columns.Id)),
                "left"
            )
            .groupBy(CommonColumns.ClaimNumber)
            .agg(
                collectProviders(
                    col(ProviderColumns.ProviderNpi),
                    col(ProviderColumns.ProviderRole),
                    col(NuccCsv.Output.Columns.PrescriberSpecialty),
                    col(NuccCsv.Output.Columns.PrescriberDetailSpecialty),
                    coalesce(col(NpiCsv.Output.Columns.EntityTypeCode), col(ProviderColumns.EntityTypeCode))
                        .as(NpiCsv.Output.Columns.EntityTypeCode)
                ).as(ProvidersColumn)
            )
    }

    private def getCommonColumns(df: DataFrame): Seq[Column] = {
        Seq(
            col(CommonColumns.ClaimNumber), // StringType
            col(ProviderColumns.State).as(Event.StateTag), // StringType
            col(ProviderColumns.Zip).as(Event.ZipTag), // StringType
            col(ProviderColumns.ProviderRole), // StringType
            col(ProviderColumns.EntityTypeCode), // StringType
            col(ProviderColumns.ProviderNpi), // StringType, using as intermediate
            col(ProviderColumns.ReceivedDate), // DateType
            if (Try(df.schema(CommonColumns.PublishedDate)).isFailure)
                typedLit[Date](null).as(CommonColumns.PublishedDate)
            else
                col(CommonColumns.PublishedDate) // DateType
        )
    }

    private def readClaimProviders(path: String)(implicit spark: SparkSession): DataFrame = {
        val df = readDataAndFilterOutInvalidPatientId(path)
        df.select(getCommonColumns(df): _*)
            .repartition(col(CommonColumns.ClaimNumber))
    }
}
