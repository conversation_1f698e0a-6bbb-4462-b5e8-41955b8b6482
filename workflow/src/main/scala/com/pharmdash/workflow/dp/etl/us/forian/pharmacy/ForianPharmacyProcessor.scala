package com.pharmdash.workflow.dp.etl.us.forian.pharmacy

import com.pharmdash.workflow.dp.DataProcessor
import com.pharmdash.workflow.dp.config.{ForianEtlConfig, VersionedPath}
import com.pharmdash.workflow.dp.etl.EtlUtils
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL._
import com.pharmdash.workflow.dp.etl.us.forian.ForianEtlPaths
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.{Nested, Single}
import com.pharmdash.workflow.dp.meta.{Description, Name}
import com.pharmdash.workflow.{Results, WorkflowContext}
import org.apache.spark.sql.Dataset

@Processor(ProcessorCategory.ETL)
@Name("Forian Pharmacy ETL")
@Description("Read Forian Pharmacy data from S3")
class ForianPharmacyProcessor(
                                 @Single
                                 @Nested(classOf[ForianEtlConfig])
                                 forianEtlConfig: ForianEtlConfig) extends DataProcessor {

    private val etlParams = ForianETLParams(forianEtlConfig.getStartOfDataset, forianEtlConfig.getEndOfDataset, forianEtlConfig.getRawDataBucket, forianEtlConfig.getRawDatasourcesMap, forianEtlConfig.getKeepPharmacyDispensedOnly)

    override def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {
        val intermediateOutputPath = forianEtlConfig.getIntermediateOutputPath

        // Read ForianPrescriber
        val prescribers = readProcessedPrescribersInput(intermediateOutputPath)

        var (pharmacySubject, pharmacyEvent) = context.dependencyProvider.readForianPharmacy(prescribers, ForianDataset.Open, etlParams)

        if (forianEtlConfig.getIncludeClosed) {
            val (closedPharmacySubject, closedPharmacyEvent) = context.dependencyProvider.readForianPharmacy(prescribers, ForianDataset.Closed, etlParams)

            pharmacySubject = pharmacySubject.unionByName(closedPharmacySubject)
            pharmacyEvent = pharmacyEvent.unionByName(closedPharmacyEvent)
        }

        // Write subjects
        EtlUtils.writeParquet(pharmacySubject, s"${intermediateOutputPath.getCombinedPath}/${ForianEtlPaths.pharmacySuffix}/${ForianEtlPaths.subjectSuffix}")

        // Write events
        EtlUtils.writeParquet(pharmacyEvent, s"${intermediateOutputPath.getCombinedPath}/${ForianEtlPaths.pharmacySuffix}/${ForianEtlPaths.eventSuffix}")

        Map()
    }

    private def readProcessedPrescribersInput(intermediateOutputPath: VersionedPath)(implicit context: WorkflowContext): Dataset[ForianPrescriber] = {
        import context.spark.implicits._

        val processedPrescriberOutputBucket = s"${intermediateOutputPath.getCombinedPath}/${ForianEtlPaths.prescriberSuffix}"
        EtlUtils.readParquet(processedPrescriberOutputBucket)
            .as[ForianPrescriber]
    }
}
