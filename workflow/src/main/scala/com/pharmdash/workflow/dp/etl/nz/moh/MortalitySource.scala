package com.pharmdash.workflow.dp.etl.nz.moh

import com.pharmdash.workflow.dp.etl.nz.moh.MohClaimReader.{oneOf, toDate_MMyyyy, toDate_dd_MM_yyyy}
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions.{col, lit, max, row_number}

case class MortalitySourceCoded(new_mast_enc_nhi: String, year_month_birth: String, dod: String, sex: String, ethnicgp: String = null, bdm_death_cause_1: String, age_at_death_yrs: Int, dthplace: String, published: String = "", file_extract: Int)

case class MortalitySourceUnCoded(new_mast_enc_nhi: String, year_month_birth: String, dod: String, gender: String, ethnicgp: String = null, bdm_death_cause_1: String, age_at_death_yrs: Int, death_place: String, published: String = "", file_extract: Int)

object MortalitySource {

    val CLASSIFICATION = "MoH Mortality"

    object Coded {

        // path
        val path = "mos*_coded/"

        object Csv {
            // csv header names
            val SubjectId: Array[String] = Array("new_mast_enc_nhi", "new_master_encrypted_hcu_id")
            val DateOfBirth: Array[String] = Array("year_month_birth", "MY_BIRTH")
            val Gender = "sex"
            val Ethnicity: Array[String] = Array("ethnicgp", "priority_ethnic_code")

            //event
            val Dod = "dod"
            /*Event Date, also event property*/
            val DeathCause = "bdm_death_cause_1"
            val ICDA = "icda" // used for Servier

            //tag
            val Age = "age_at_death_yrs"
            val Location: Array[String] = Array("dthplace", "death_place")

            // partition columns
            val Published = "published"
            val FileExtract = "file_extract"
        }


        def standard(coded: DataFrame, config: SourceConfig = StandardSourceConfig): DataFrame = {
            deduplicate(coded, Csv.SubjectId, Csv.Published, Csv.Dod)
                .drop(Csv.Published, Csv.FileExtract)
                .select(
                    oneOf(coded, Csv.SubjectId, StandardMOHColumnHeaders.SubjectId),
                    col(Csv.Gender).as(StandardMOHColumnHeaders.Gender),
                    oneOf(coded, Csv.Ethnicity, StandardMOHColumnHeaders.Ethnicity),
                    if (coded.columns.contains(Csv.DateOfBirth(0)))
                        config.getDateFormat(col(Csv.DateOfBirth(0))).as(StandardMOHColumnHeaders.DateOfBirth)
                    else if (coded.columns.contains(Csv.DateOfBirth(1)))
                        toDate_MMyyyy(col(Csv.DateOfBirth(1))).as(StandardMOHColumnHeaders.DateOfBirth)
                    else lit(null).as(StandardMOHColumnHeaders.DateOfBirth),

                    toDate_dd_MM_yyyy(col(Csv.Dod)).as(StandardMOHColumnHeaders.DateOfDeath),

                    lit(null).as(StandardMOHColumnHeaders.EventId),
                    lit(null).as(StandardMOHColumnHeaders.Category), // Category not available
                    lit(CLASSIFICATION).as(StandardMOHColumnHeaders.Classification),
                    col(config.deathCauseColumn).as(StandardMOHColumnHeaders.Code),
                    toDate_dd_MM_yyyy(col(Csv.Dod)).as(StandardMOHColumnHeaders.Date),
                    lit(null).as(StandardMOHColumnHeaders.Amount),
                    lit(null).as(StandardMOHColumnHeaders.Value), // Value not available
                    lit(null).as(StandardMOHColumnHeaders.Cost),

                    col(Csv.Age).as(StandardMOHColumnHeaders.Tag.Age),
                    oneOf(coded, Csv.Location, StandardMOHColumnHeaders.Tag.Location),
                    lit(null).as(StandardMOHColumnHeaders.Tag.DaysSupply),
                    lit(null).as(StandardMOHColumnHeaders.Tag.Strength),
                    lit(null).as(StandardMOHColumnHeaders.Tag.ReferrerId), // ReferrerId not available
                    lit(null).as(StandardMOHColumnHeaders.Tag.PrescriberId),
                    lit(null).as(StandardMOHColumnHeaders.Tag.RepeatSequence),

                    lit(null).as(StandardMOHColumnHeaders.Tag.StartDate),
                    lit(null).as(StandardMOHColumnHeaders.Tag.EndDate),
                    lit(null).as(StandardMOHColumnHeaders.Tag.Facility),
                    lit(null).as(StandardMOHColumnHeaders.Tag.FacilityType),
                    lit(null).as(StandardMOHColumnHeaders.Tag.HealthSpecialityCode),

                    lit(null).as(StandardMOHColumnHeaders.Tag.DatetimeOfDeparture),
                    lit(null).as(StandardMOHColumnHeaders.Tag.DatetimeOfEventEnd),
                    lit(null).as(StandardMOHColumnHeaders.Tag.DatetimeOfFirstContact),
                    lit(null).as(StandardMOHColumnHeaders.Tag.DatetimeOfPresentation),
                    lit(null).as(StandardMOHColumnHeaders.Tag.DatetimeOfService),

                    lit(null).as(StandardMOHColumnHeaders.Tag.AccidentFlag),
                    lit(null).as(StandardMOHColumnHeaders.Tag.IcuHours),
                    lit(null).as(StandardMOHColumnHeaders.Tag.HoursOnVentilation),
                    lit(null).as(StandardMOHColumnHeaders.Tag.PatientClinicalComplexityLevel),
                    lit(null).as(StandardMOHColumnHeaders.Tag.ComplicationComorbidityLevel),
                    lit(null).as(StandardMOHColumnHeaders.Tag.DiagnosticRelatedGroup),
                    lit(null).as(StandardMOHColumnHeaders.Tag.DrgGrouperType),

                    lit(null).as(StandardMOHColumnHeaders.Tag.Morphology),
                    lit(null).as(StandardMOHColumnHeaders.Tag.BasisOfDiagnosis),
                    lit(null).as(StandardMOHColumnHeaders.Tag.TumourGrade),
                    lit(null).as(StandardMOHColumnHeaders.Tag.ExtentOfDisease),
                    lit(null).as(StandardMOHColumnHeaders.Tag.Laterality),

                    lit(null).as(StandardMOHColumnHeaders.Tag.TestType),
                    lit(null).as(StandardMOHColumnHeaders.Tag.TestDescription)
                )
                .filter(
                    col(StandardMOHColumnHeaders.SubjectId).isNotNull.and(col(StandardMOHColumnHeaders.SubjectId).notEqual(""))
                )

        }
    }

    object UnCoded {

        // path
        val path = "mos*_uncoded/"

        object Csv {
            // csv header names
            val SubjectId: Array[String] = Array("new_mast_enc_nhi", "NEW_MASTER_ENCRYPTED_HCU_ID")
            val DateOfBirth: Array[String] = Array("year_month_birth", "DOB")
            val Gender: Array[String] = Array("gender", "SEX")
            val Ethnicity: Array[String] = Array("ethnicgp", "PRIORITY_ETHNIC_CODE")

            //event
            val Dod = "dod"
            /*Event Date, also event property*/
            val DeathCause = "bdm_death_cause_1"

            //tag
            val Age = "age_at_death_yrs"
            val Location = "death_place"

            // partition columns
            val Published = "published"
            val FileExtract = "file_extract"
        }

        def standard(uncoded: DataFrame, config: SourceConfig = StandardSourceConfig): DataFrame = {
            deduplicate(uncoded, Csv.SubjectId, Csv.Published, Csv.Dod)
                .drop(Csv.Published, Csv.FileExtract)
                .select(
                    oneOf(uncoded, Csv.SubjectId, StandardMOHColumnHeaders.SubjectId),
                    oneOf(uncoded, Csv.Gender, StandardMOHColumnHeaders.Gender),
                    oneOf(uncoded, Csv.Ethnicity, StandardMOHColumnHeaders.Ethnicity),

                    if (uncoded.columns.contains(Csv.DateOfBirth(0)))
                        config.getDateFormat(col(Csv.DateOfBirth(0))).as(StandardMOHColumnHeaders.DateOfBirth)
                    else if (uncoded.columns.contains(Csv.DateOfBirth(1)))
                        toDate_MMyyyy(col(Csv.DateOfBirth(1))).as(StandardMOHColumnHeaders.DateOfBirth)
                    else lit(null).as(StandardMOHColumnHeaders.DateOfBirth),

                    toDate_dd_MM_yyyy(col(Csv.Dod)).as(StandardMOHColumnHeaders.DateOfDeath),

                    lit(null).as(StandardMOHColumnHeaders.EventId), //
                    lit(null).as(StandardMOHColumnHeaders.Category), // Category not available
                    lit(CLASSIFICATION).as(StandardMOHColumnHeaders.Classification),
                    col(Csv.DeathCause).as(StandardMOHColumnHeaders.Code),
                    toDate_dd_MM_yyyy(col(Csv.Dod)).as(StandardMOHColumnHeaders.Date),
                    lit(null).as(StandardMOHColumnHeaders.Amount),
                    lit(null).as(StandardMOHColumnHeaders.Value), // Value not available
                    lit(null).as(StandardMOHColumnHeaders.Cost),

                    col(Csv.Age).as(StandardMOHColumnHeaders.Tag.Age),
                    col(Csv.Location).as(StandardMOHColumnHeaders.Tag.Location),
                    lit(null).as(StandardMOHColumnHeaders.Tag.DaysSupply),
                    lit(null).as(StandardMOHColumnHeaders.Tag.Strength),
                    lit(null).as(StandardMOHColumnHeaders.Tag.ReferrerId), // ReferrerId not available
                    lit(null).as(StandardMOHColumnHeaders.Tag.PrescriberId),
                    lit(null).as(StandardMOHColumnHeaders.Tag.RepeatSequence),

                    lit(null).as(StandardMOHColumnHeaders.Tag.StartDate),
                    lit(null).as(StandardMOHColumnHeaders.Tag.EndDate),
                    lit(null).as(StandardMOHColumnHeaders.Tag.Facility),
                    lit(null).as(StandardMOHColumnHeaders.Tag.FacilityType),
                    lit(null).as(StandardMOHColumnHeaders.Tag.HealthSpecialityCode),

                    lit(null).as(StandardMOHColumnHeaders.Tag.DatetimeOfDeparture),
                    lit(null).as(StandardMOHColumnHeaders.Tag.DatetimeOfEventEnd),
                    lit(null).as(StandardMOHColumnHeaders.Tag.DatetimeOfFirstContact),
                    lit(null).as(StandardMOHColumnHeaders.Tag.DatetimeOfPresentation),
                    lit(null).as(StandardMOHColumnHeaders.Tag.DatetimeOfService),

                    lit(null).as(StandardMOHColumnHeaders.Tag.AccidentFlag),
                    lit(null).as(StandardMOHColumnHeaders.Tag.IcuHours),
                    lit(null).as(StandardMOHColumnHeaders.Tag.HoursOnVentilation),

                    lit(null).as(StandardMOHColumnHeaders.Tag.PatientClinicalComplexityLevel),
                    lit(null).as(StandardMOHColumnHeaders.Tag.ComplicationComorbidityLevel),
                    lit(null).as(StandardMOHColumnHeaders.Tag.DiagnosticRelatedGroup),
                    lit(null).as(StandardMOHColumnHeaders.Tag.DrgGrouperType),

                    lit(null).as(StandardMOHColumnHeaders.Tag.Morphology),
                    lit(null).as(StandardMOHColumnHeaders.Tag.BasisOfDiagnosis),
                    lit(null).as(StandardMOHColumnHeaders.Tag.TumourGrade),
                    lit(null).as(StandardMOHColumnHeaders.Tag.ExtentOfDisease),
                    lit(null).as(StandardMOHColumnHeaders.Tag.Laterality),

                    lit(null).as(StandardMOHColumnHeaders.Tag.TestType),
                    lit(null).as(StandardMOHColumnHeaders.Tag.TestDescription)

                )
                .filter(
                    col(StandardMOHColumnHeaders.SubjectId).isNotNull.and(col(StandardMOHColumnHeaders.SubjectId).notEqual(""))
                )

        }
    }

    def deduplicate(dataFrame: DataFrame, subjectIdColumNames: Array[String], publishedColumnName: String, dodColumnName: String): DataFrame = {
        if (dataFrame.columns.exists(_.equalsIgnoreCase(publishedColumnName))) {
            dataFrame
                .withColumn(
                    "row_number",
                    row_number().over(
                        Window.partitionBy(oneOf(dataFrame, subjectIdColumNames))
                            .orderBy(
                                col(publishedColumnName).desc_nulls_last,
                                col(dodColumnName).desc_nulls_last
                            )
                    )
                )
                .filter(col("row_number") === 1)
                .drop("row_number")
        } else {
            dataFrame
        }
    }

    // this converts to the EventIntermediate format which will match other event sources so we can union them together
    // data that is not available in this event source will be returned at lit(null)
    def standard(coded: DataFrame, uncoded: DataFrame, sourceConfig: SourceConfig = StandardSourceConfig): DataFrame = {
        Coded.standard(coded, sourceConfig)
            .union(UnCoded.standard(uncoded, sourceConfig))
    }

}
