package com.pharmdash.workflow.dp.etl.nz.moh
import com.pharmdash.workflow.dp.etl.nz.moh.MohClaimReader.{toDate_MMM_yy, toDate_MMMyyyy}
import com.pharmdash.workflow.dp.etl.nz.moh.MortalitySource.Coded.Csv.DeathCause
import org.apache.spark.sql.expressions.UserDefinedFunction

object StandardSourceConfig extends SourceConfig {
    override def getDateFormat: UserDefinedFunction = toDate_MMM_yy

    override def deathCauseColumn: String = DeathCause
}
