package com.pharmdash.workflow.dp

import com.pharmdash.workflow.common.BetterJson.formats
import com.pharmdash.workflow.common.TechnicalModelEncoders._
import com.pharmdash.workflow.dp.ICSDProcessor._
import com.pharmdash.workflow.dp.ProviderFinderProcessor.ProviderFinderTag
import com.pharmdash.workflow.dp.meta._
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.Many
import com.pharmdash.workflow.dp.meta.propertyannotations.PropertyType.WORKFLOW_GROUP_SELECTOR
import com.pharmdash.workflow.model.Subject
import com.pharmdash.workflow.{Results, WorkflowContext}
import org.apache.spark.sql.Dataset
import org.json4s.jackson.Serialization.read

object ProviderFinderProcessor {
    val PropInterestedEvents = "interestedEvents"

    final val ProviderFinderTag = "providerFinder"
}

@Processor(ProcessorCategory.ALGORITHM)
@Name("Provider Finder Report Algorithm")
@Description("Add tags necessary for Provider Finder report")
@Input(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT, tags = new Tags(eventTags = Array(ProviderFinderTag)))
class ProviderFinderProcessor(@Many(WORKFLOW_GROUP_SELECTOR)
                   interestedEvents: List[String])
    extends DataProcessor with Serializable {

    override def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {
        val includeAllIcsdEvents = interestedEvents.isEmpty
        val subjects: Dataset[Subject] = inputData(EVENTS).subjects.get
        val results = subjects.map(subject => {

            subject.events
                .filter(e => e.tags.contains(IcsdTag))
                .foreach(e =>
                    e.tags = e.tags + (ProviderFinderTag ->
                        e.tags.fetch(IcsdTag)
                            .map(read[ICSDProcessor.ICSDStruct])
                            .filter(i => includeAllIcsdEvents || interestedEvents.contains(i.event))
                            .filter(i => i.isInflow)
                            .map(_.event))
                )

            subject
        })

        Map((EVENTS, inputData(EVENTS).replaceSubjects(results)))
    }
}
