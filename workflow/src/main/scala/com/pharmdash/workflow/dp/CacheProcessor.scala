package com.pharmdash.workflow.dp

import com.pharmdash.workflow.dp.meta._
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.PropertyType._
import com.pharmdash.workflow.dp.meta.propertyannotations._
import com.pharmdash.workflow.model.Subject
import com.pharmdash.workflow.{FeatureToggles, Results, WorkflowContext}
import org.apache.spark.sql.Dataset
import org.apache.spark.storage.StorageLevel


@Deprecated
@Processor(ProcessorCategory.TRANSFORMATION)
@Name("Cache")
@Description("Cache current processed subject to memory or file store. Caution: incorrect use will cause performance drop or failed workflow.")
@Input(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
class CacheProcessor(@Single(BOOLEAN) @Default("false") cacheToFileStore: Boolean = false)

    extends DataProcessor with Serializable {

    override def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {
        val enableAutomateCaching = context.featureToggles.getOrElse(FeatureToggles.EnableAutomateCachingOnWorkflowGeneration, false)

        if (enableAutomateCaching) {
            // If automatic caching is enabled , CacheProcessor nodes are effectively “disabled”, as caching will be handled automatically.
            inputData
        } else {
            var subjects: Dataset[Subject] = inputData(EVENTS).subjects.get
            if (cacheToFileStore) {
                subjects = subjects.persist(StorageLevel.DISK_ONLY)
            } else {
                subjects = subjects.cache()
            }
            Map((EVENTS, inputData(EVENTS).replaceSubjects(subjects)))
        }

    }
}
