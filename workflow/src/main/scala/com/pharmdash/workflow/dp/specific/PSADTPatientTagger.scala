package com.pharmdash.workflow.dp.specific

import com.pharmdash.workflow.common.TechnicalModelEncoders._
import com.pharmdash.workflow.dp.DataProcessor
import com.pharmdash.workflow.dp.meta._
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.PropertyType._
import com.pharmdash.workflow.dp.meta.propertyannotations.{Default, Many, Single}
import com.pharmdash.workflow.dp.specific.PSADTPatientTagger._
import com.pharmdash.workflow.model.{Event, Subject}
import com.pharmdash.workflow.{Results, WorkflowContext}
import org.apache.spark.sql.Dataset

import java.sql.Date
import javax.validation.constraints.NotBlank
import scala.math.pow

@RequireItemGroupConfiguration
@Processor(ProcessorCategory.TRANSFORMATION)
@Name("PSADT Subject Tagger")
@Description(
    "<p>PSA doubling time will be calculated by log(2) divided by the slope of the " +
        "linear regression of log(PSA) over time in months.</p>" +
        "<p>Final PSADT is rounded to two decimal places using half up.</p>" +
        "<p>PSADT < 0 or > 120 will be assigned 120 months for ease of analysis.</p>" +
        "<p>PSADT is calculated from the non negative PSA values that are greater than 0</p>" +
        "<p>If all PSA values are the same, PSADT will be 120.</p>" +
        "<p>PSA values should be above <b>Psa Minimum Threshold</b>. The remaining values must number more than <b>Minimum Number Of PSA Values</b></p>" +
        "<p>Note: <code>month = 12 * ((days from 1900-01-01) + 1) / 365.25</code></p>" +
        "<hr/>" +
        "<p><b>How to use:</b></p>" +
        "<p><b>SPARTAN Method</b>:<br/>" +
        "Only PSA values occurring after or on same day as <b>Treatment Groups</b> will be used to calculate PSADT.</p>" +
        "<p>PSA values up until <b>Metastasis Groups</b>, <b>death</b> or <b>2 yrs</b> (calendar year inclusive) from the first <b>Treatment Groups</b></p>" +
        "<p>There must be a minimum of 3 (Minimum Number Of PSA Values = 3) PSA values.</p>" +
        "<p>The first and last PSA values must be at least 8 weeks (56 days inclusive) (Minimum Gap Days) apart.</p>" +
        "<p><b>Howard Method</b>:<br/>" +
        "Only PSA values occurring after <b>Treatment Groups</b> will be used to calculate PSADT.</p>" +
        "<p>PSA values up until <b>Metastasis Groups</b>, <b>death</b> or <b>2 yrs</b> (calendar year inclusive) from the first <b>Treatment Groups</b></p>" +
        "<p>There must be a minimum of 2 (Minimum Number Of PSA Values = 2) PSA values. The first and last PSA values must be at least 3 months (90 days inclusive) (Minimum Gap Days) apart.</p>"
)
@Input(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
class PSADTPatientTagger(@Single @Default("PSADT") @NotBlank
                         outputTag: String,
                         @Many(WORKFLOW_GROUP_SELECTOR) @NotBlank
                         psaGroups: List[String],
                         @Many(WORKFLOW_GROUP_SELECTOR)
                         treatmentGroups: List[String],
                         @Many(WORKFLOW_GROUP_SELECTOR)
                         metastasisGroups: List[String],
                         @Single(INTEGER) @Default("2") minimumNumberOfPsaValues: Integer,
                         @Single(DOUBLE) @Default("0.0") psaMinimumThreshold: Double,
                         @Single(INTEGER) @Default("2") analysisCutoffYears: Long,
                         @Single(INTEGER) @Default("90") minimumGapDays: Long
                        )

    extends DataProcessor with Serializable {

    override def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {

        assert(outputTag != null && !outputTag.isEmpty, "Output tag is required.")

        val subjects: Dataset[Subject] = inputData(EVENTS).subjects.get

        val results = subjects.map(subject => {
            val maybeFirstEvent = subject firstOf treatmentGroups

            val metastasisDate = subject firstOf metastasisGroups map (_.date)
            val deathDate = subject.dateOfDeath

            // Must be within 2 years
            val analysisCutoff = maybeFirstEvent
                .map(_.date.toLocalDate)
                .map(_.plusYears(analysisCutoffYears))
                .map(Date.valueOf)

            if (maybeFirstEvent.isDefined) {

                val firstTreatmentLine = maybeFirstEvent.get

                val psaDateCutoff =
                    Seq(metastasisDate, deathDate, analysisCutoff)
                        .filter(_.isDefined)
                        .map(_.get)
                        .min

                val psaEvents = subject.all(psaGroups)
                    .filter(_.afterOrSameday(firstTreatmentLine)) // Must be after
                    .filter(_.beforeOrSameday(psaDateCutoff)) // Must be before cutoff
                    .filter(_.value.exists(_ >= psaMinimumThreshold))

                if (psaEvents.size >= minimumNumberOfPsaValues) {

                    // The first and last PSA values used in the calculation must be separated by at least 8 weeks
                    val firstPsaEvent = psaEvents.head
                    if ((firstPsaEvent daysUntil psaEvents.last) >= minimumGapDays) {
                        updatePSADT(subject, psaEvents)
                    }
                }
            }
            subject
        })

        Map((EVENTS, inputData(EVENTS).replaceSubjects(results)))
    }

    private def updatePSADT(subject: Subject, psaEvents: Seq[Event]): Unit = {
        // Perform regression over month and value.
        // Save to subject.
        doublingTime(psaEvents)
            .map(BigDecimal(_))
            .map(value => s"${value.setScale(2, BigDecimal.RoundingMode.HALF_UP)}")
            .foreach(value => subject replaceTag(outputTag, value))
    }

    override def getConsumedItemGroups(): Seq[String] = {
        psaGroups ++ treatmentGroups ++ metastasisGroups
    }
}

object PSADTPatientTagger {

    val SpartanMinDurationPSA: Int = 8 * 7 // 8 Weeks.
    val HowardMinDurationPSA: Int = 3 * 30 // 3 Months.

    val SpartanMethod = "SPARTAN"
    val HowardMethod = "HOWARD"

    def doublingTime(psaEvents: Seq[Event], referenceDate: Date = Date.valueOf("1900-01-01")): Option[Double] = {

        def month(tuple: (Double, Double)): Double = {
            val (month, _) = tuple;
            month
        }

        def lnPSA(tuple: (Double, Double)): Double = {
            val (_, lnPSA) = tuple;
            lnPSA
        }

        val monthPsaList: Seq[(Double, Double)] = psadtMonthValues(psaEvents, referenceDate)

        val n = monthPsaList.size
        if (n <= 1) {
            return None
        }

        val avgDate = monthPsaList.map(month).sum / n
        val avgLnPSA = monthPsaList.map(lnPSA).sum / n

        val numerator = monthPsaList
            .map(event =>
                (month(event) - avgDate) * (lnPSA(event) - avgLnPSA)
            ).sum

        val denominator = monthPsaList.map(event =>
            pow(month(event) - avgDate, 2)
        ).sum


        val slope = numerator / denominator
        val psadt = math.log(2) / slope

        // 55 patients have all their PSA values exactly the same.
        // In this case the slope is zero.
        // log(2) / 0.0 = Infinity in scala.
        // Infinity is > 120
        // Therefore in the case where doubling time cannot be determined due to zero slope
        // We would output 120.0
        Some(if (psadt >= 0 && psadt <= 120) psadt else 120.0)
    }

    def psadtMonthValues(psaEvents: Seq[Event], referenceDate: Date): Seq[(Double, Double)] = {

        def calculateMonth(event: Event) = {
            val daysFromReferenceDate = event daysFrom referenceDate
            val month = 12 * (daysFromReferenceDate + 1) / 365.25
            month
        }

        psaEvents
            .filter(_.value.isDefined)
            .filter(_.value.get > 0) // Can only be positive.
            .map(event => calculateMonth(event) -> math.log(event.value.get))
    }
}


