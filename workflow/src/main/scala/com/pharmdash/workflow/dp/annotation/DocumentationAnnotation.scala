package com.pharmdash.workflow.dp.annotation

import com.pharmdash.workflow.ValidationContext
import com.pharmdash.workflow.dp.javadto.ViolationDTO
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.{NotRequired, PropertyType, Single}
import com.pharmdash.workflow.dp.meta.{Description, Name}
import org.apache.hadoop.shaded.org.apache.commons.lang3.StringUtils

@Processor(ProcessorCategory.ANNOTATION)
@SubProcessor(SubProcessorCategory.DOC_ANNOTATION)
@Name("Documentation")
@Description("Add an annotation/comment in the workflow. This is for information only and does not affect the data.")
@Input(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
class DocumentationAnnotation(
                    @Single(PropertyType.FREE_TEXT)
                    @Description("The content of the annotation or comment.")
                    @NotRequired
                    annotationText: String = ""
                )
    extends AbstractAnnotation(annotationText) with Serializable {

    override def validate()(implicit context: ValidationContext): Seq[ViolationDTO] = {
        if (!StringUtils.isEmpty(annotationText))
            super.validate()
        else Seq.newBuilder[ViolationDTO].result()
    }
}
