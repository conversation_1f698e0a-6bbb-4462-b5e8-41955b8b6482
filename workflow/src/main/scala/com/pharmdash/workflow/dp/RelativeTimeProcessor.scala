package com.pharmdash.workflow.dp

import com.pharmdash.workflow.common.BetterJson.formats
import com.pharmdash.workflow.common.DateUtil
import com.pharmdash.workflow.common.TechnicalModelEncoders._
import com.pharmdash.workflow.dp.RelativeTimeProcessor._
import com.pharmdash.workflow.dp.meta._
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.Many
import com.pharmdash.workflow.dp.meta.propertyannotations.PropertyType._
import com.pharmdash.workflow.{Results, WorkflowContext}
import org.json4s.jackson.Serialization.write

import java.util.Date

object RelativeTimeProcessor {

    final val RelativeTimeTagName = "relativeTime"
    final val RelativeTimeBaselineEventTagName = "relativeTimeBaselineEvent"

    val DaysInAMonth = 30

    case class RelativeTimeStruct(baselineEvent: String, distance: Int)

    /**
     * The relative time tag will be calculated as follows:
     *
     * -59 to -30 days  -> -2 month
     * -29 to -1 days   -> -1 month
     * 0 to 29 days     ->  0 month
     * 30 to 59 days    ->  1 month
     *
     * and so on
     */
    def calcDistance(start: Date, end: Date): Int = {
        val distance = DateUtil.distanceInWholeApproximatedMonths(start, end)
        // shift negative months by 1, otherwise month0 will contain -29 to 29, for a total of 59 days
        if (start.after(end)) distance - 1 else distance
    }
}

@RequireItemGroupConfiguration
@Processor(ProcessorCategory.TRANSFORMATION)
@Name("Relative Time Tagger")
@Description("Tag all events with their distance(in Months) to each of the baseline events." +
    " The relative time tag will be calculated as follows: " +
    " -59 to -30 days -> -2 month; -29 to -1 days -> -1 month; 0 to 29 days -> 0 month; 30 to 59 days -> 1 month;" +
    " and so on.")
@Input(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT, tags = new Tags(eventTags = Array(RelativeTimeTagName, RelativeTimeBaselineEventTagName)))
class RelativeTimeProcessor(@Many(WORKFLOW_GROUP_SELECTOR)
                            baselineEvents: List[String])
    extends DataProcessor {

    override def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {
        val subjects = inputData(EVENTS).subjects.get
        val baselineEventSet = baselineEvents.toSet

        val results = subjects.map(subject => {
            // find initiations for all interested events
            val inits = DataProcessor.findInitEvents(subject.events, baselineEventSet)

            inits.groupBy(_.ref)
                .foreach { case (event, initSimpleEvents) =>
                    event.tags = event.tags + (RelativeTimeBaselineEventTagName -> initSimpleEvents.map(_.group))
                }

            // for each event, calculate distance from each of the initiations to this event.
            subject.events.foreach(event => {
                val distances = inits
                    .map(baseEvent => RelativeTimeStruct(baseEvent.group, calcDistance(baseEvent.date, event.date)))
                    .map(write(_))

                event.tags = event.tags + (RelativeTimeTagName -> distances)
            })

            subject
        })

        Map((EVENTS, inputData(EVENTS).replaceSubjects(results)))
    }

    override def getConsumedItemGroups(): Seq[String] = {
        baselineEvents
    }
}
