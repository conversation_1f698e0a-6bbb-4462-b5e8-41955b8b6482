package com.pharmdash.workflow.dp.etl.us.forian.claimpatient

import com.pharmdash.workflow.dp.etl.us.forian.ForianETL.ForianDataset.ForianDataset
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL._
import com.pharmdash.workflow.dp.etl.us.forian.SubjectNetworkTagStruct.getSubjectNetworkStruct
import com.pharmdash.workflow.model.SubjectInfo
import com.prospection.arch2.util.SerializableLogging
import org.apache.spark.sql.functions.{col, collect_set, lit}
import org.apache.spark.sql.types.{DateType, IntegerType}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

object ForianClaimPatientReader extends SerializableLogging {

    type ReadForianClaimPatient = (ForianDataset, ForianETLParams) => Dataset[ForianSubject]

    def readClaimPatient(dataType: ForianDataset, etlParams: ForianETLParams)(implicit spark: SparkSession): Dataset[ForianSubject] = {
        import spark.implicits._

        val claimPatients = loadClaimPatients(etlParams, dataType)
        if (claimPatients.isEmpty) {
            return Seq.empty[ForianSubject].toDS()
        }
        val patientDf = claimPatients.reduce(_ unionByName _)

        patientDf
            .groupBy(CommonColumns.PatientId)
            .agg(
                yearFrequencyToDateFrequency(collectYearFrequencyAgg(col(PatientColumns.BirthYear).cast(IntegerType)))
                    .as(SubjectInfo.DateOfBirthColumn),
                collectGenderFrequencyAgg(toGender(col(PatientColumns.GenderCode))).as(SubjectInfo.GenderTag),
                lit(getSubjectNetworkStruct(dataType)).as(NetworkTag),
                lit(null).cast(DateType).as(SubjectInfo.DateOfDeathColumn),
                collect_set(col(RawDataSourceTag)).as(RawDataSourceTag)
            ).as[ForianSubject]
    }

    private def loadClaimPatients(etlParams: ForianETLParams, dataType: ForianDataset)
                                  (implicit spark: SparkSession): List[DataFrame] = {
        val bucket = etlParams.bucket

        etlParams.rawDatasources.flatMap { case (datasource, path) =>
            datasource match {
                case ForianDataSource.Legacy =>
                    Some(readClaimPatientColumns(s"$bucket/$path/${getDirClaimPatientLegacy(dataType)}", datasource))
                case ForianDataSource.BiMonthly if dataType == ForianDataset.Open =>
                    Some(readClaimPatientColumns(s"$bucket/$path/${Subdirectories.BiMonthlyClaimPatient}", datasource))
                case _ => None
            }
        }.toList
    }

    private def readClaimPatientColumns(path: String, dataSource: String)(implicit spark: SparkSession): DataFrame = {
        val claimPatientColumns = Seq(
            col(CommonColumns.PatientId),
            col(PatientColumns.BirthYear),
            col(PatientColumns.GenderCode)
        )
        readDataAndFilterOutInvalidPatientId(path).select(claimPatientColumns: _*).withColumn(RawDataSourceTag, lit(dataSource))
    }

}
