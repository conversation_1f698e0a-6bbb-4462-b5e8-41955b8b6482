package com.pharmdash.workflow.dp

import com.pharmdash.workflow.common.TechnicalModelEncoders._
import com.pharmdash.workflow.dp.meta._
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations._
import com.pharmdash.workflow.model._
import com.pharmdash.workflow.{Results, WorkflowContext}
import org.apache.spark.sql.Dataset

import javax.validation.constraints.NotBlank

/**
 * Filters events by tag and its value.
 *
 * Strategies -
 *
 * 1 - INCLUSION: only events having the tag values should be included
 * 2 - EXCLUSION: all events should be excluded if they have the tag values
 *
 */
@Processor(ProcessorCategory.FILTER)
@Name("Event Tag Filter")
@Description("Filters events by tag and its value. " +
    "INCLUSION: only events having the tag values should be included. " +
    "EXCLUSION: all events should be excluded if they have the tag values.")
@Input(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
class EventTagFilter(@Single @NotBlank @Name("Tag Name")
                     tag: String,
                     @Many @Name("Tag Values")
                     values: List[String],
                     @Single @Default("EXCLUSION") @Name("Filter Strategy") @Nested(classOf[EventTagFilterStrategy])
                     strategy: EventTagFilterStrategy) extends DataProcessor {

    override def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {

        val subjects = inputData(EVENTS).subjects.get

        def hasTargetValue(target: String, values: List[String]): Event => Boolean = {
            event => event.tags.fetch(target).intersect(values).nonEmpty
        }

        def events(subject: Subject, target: String, values: List[String]): Seq[Event] = {
            strategy match {
                case EventTagFilterStrategy.INCLUSION => subject.events.filter(hasTargetValue(target, values))
                case EventTagFilterStrategy.EXCLUSION => subject.events.filterNot(hasTargetValue(target, values))
            }
        }

        val result: Dataset[Subject] = subjects.map(subject => {
            subject.events = events(subject, tag, values)
            subject
        })

        Map(EVENTS -> inputData(EVENTS).replaceSubjects(result))
    }
}


