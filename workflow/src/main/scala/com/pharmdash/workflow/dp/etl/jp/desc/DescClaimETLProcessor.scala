package com.pharmdash.workflow.dp.etl.jp.desc

import com.pharmdash.workflow.dp.DataProcessor
import com.pharmdash.workflow.dp.etl.EtlUtils.{sortedEvents, withDeath}
import com.pharmdash.workflow.dp.etl.jp.desc.DescETL.OutputColumns
import com.pharmdash.workflow.dp.etl.jp.desc.enrollment.DescSubject
import com.pharmdash.workflow.dp.etl.mdv.MdvClaimReader.SubjectIdColumn
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL.CommonColumns
import com.pharmdash.workflow.dp.importer.SubjectBackwardCompatibilityHelper.{EmptyObjectTagsColumn, NullDistinctEventCodesValue}
import com.pharmdash.workflow.dp.meta.processorannotations._
import com.pharmdash.workflow.dp.meta.propertyannotations.Single
import com.pharmdash.workflow.dp.meta.{Description, Name}
import com.pharmdash.workflow.model.Event.{AmountColumn, CategoryColumn, ClassificationColumn, CodeColumn, CostColumn, DateColumn, GroupsColumn, IdColumn, TagsColumn, UnitColumn, ValueColumn}
import com.pharmdash.workflow.model.Subject.{EventsColumn, InfoColumn, IdColumn => _, apply => _}
import com.pharmdash.workflow.model.SubjectInfo.{apply => _}
import com.pharmdash.workflow.model.{Event, Subject, TagMetadata}
import com.pharmdash.workflow.{Results, WorkflowContext}
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{DoubleType, StringType}

import javax.validation.constraints.NotBlank


@Processor(ProcessorCategory.ETL)
@Name("Desc Claims ETL Processor")
@Description("Technical model documentation <a href='https://prospection.atlassian.net/wiki/x/BQDJtw'>link</a>." +
    "<p>Looks for the following directories:</p>" +
    "<ul>" +
    "<li><b>/enrollment</b></li>" +
    "<li><b>/c_drug</b></li>" +
    "<li><b>/c_drug_date</b></li>" +
    "<li><b>/m_hco_med</b></li>" +
    "<li><b>/m_hco_xref_specialty</b></li>" +
    "<li><b>/c_basic_info</b></li>" +
    "<li><b>/c_med_procedure</b></li>" +
    "<li><b>/c_med_procedure_date</b></li>" +
    "<li><b>/c_med_procedure_iqvia</b></li>" +
    "<li><b>/c_med_procedure_date_iqvia</b></li>" +
    "<li><b>/c_disease</b></li>" +
    "<li><b>/m_icd10</b></li>" +
    "<li><b>/c_specific_equipment</b></li>" +
    "<li><b>/c_specific_equipment_date</b></li>" +
    "</ul>")
@Output(name = Channel.EVENTS, streamType = DataStreamType.SUBJECT)
class DescClaimETLProcessor(
                         @Single @NotBlank bucket: String,
                         @Single @NotBlank @Description("yyyy-MM-dd") startOfDataset: String,
                         @Single @NotBlank @Description("yyyy-MM-dd") endOfDataset: String) extends DataProcessor {

    override def process(inputData: Map[String, Results])(implicit context: WorkflowContext, nodeId: String): Map[String, Results] = {
        import context.spark.sqlContext.implicits._

        val readDescEnrollment = context.dependencyProvider.readDescEnrollment
        val readDescProvider = context.dependencyProvider.readDescProvider
        val readDescDiagnosis = context.dependencyProvider.readDescDiagnosis
        val readDescDispensing = context.dependencyProvider.readDescDispensing
        val readDescEquipment = context.dependencyProvider.readDescEquipment
        val readDescProcedure = context.dependencyProvider.readDescProcedure
        val readDescIqviaProcedure = context.dependencyProvider.readDescIqviaProcedure

        val subjects: Dataset[DescSubject] = readDescEnrollment(bucket, startOfDataset, endOfDataset)

        val descProvider = readDescProvider(bucket)

        val allEvents = readDescDiagnosis(bucket, descProvider)
            .unionByName(readDescDispensing(bucket, descProvider))
            .unionByName(readDescEquipment(bucket, descProvider))
            .unionByName(readDescProcedure(bucket, descProvider))
            .unionByName(readDescIqviaProcedure(bucket, descProvider))

        val events = allEvents.repartition(col(OutputColumns.SubjectId))
            .sortWithinPartitions(col(OutputColumns.SubjectId), col(Event.DateColumn))
            .withColumn(Event.IdColumn, monotonically_increasing_id())
            .groupBy(col(OutputColumns.SubjectId))
            .agg(
                collect_list(
                    struct(
                        col(Event.IdColumn),
                        col(CodeColumn),
                        col(DateColumn),
                        col(AmountColumn),
                        col(CostColumn),
                        lit(null).cast(DoubleType).as(Event.ValueColumn),
                        lit(null).cast(StringType).as(Event.UnitColumn),
                        typedLit(Array.empty[String]).as(Event.GroupsColumn),
                        col(CategoryColumn),
                        col(ClassificationColumn),
                        col(TagsColumn),
                        EmptyObjectTagsColumn,
                    )
                ).as(Subject.EventsColumn)
            )

        val technicalModel = events
            .join(subjects, Seq(OutputColumns.SubjectId), "inner")
            .select(
                col(OutputColumns.SubjectId).as(Subject.IdColumn),
                col(InfoColumn),
                col(EventsColumn),
                NullDistinctEventCodesValue.as(Subject.DistinctEventCodesColumn)
            )
            .as[Subject]
            .map(sortedEvents)
            .withColumn(Subject.InfoColumn, TagMetadata.toSubjectInfoColumnWithMetadata(startOfDataset, endOfDataset))
            .withColumn(Subject.EventsColumn, TagMetadata.toEventTagsColumnWithMetadata(Map.empty))

        Map(Channel.EVENTS -> Results(Some(technicalModel)))

    }
}
