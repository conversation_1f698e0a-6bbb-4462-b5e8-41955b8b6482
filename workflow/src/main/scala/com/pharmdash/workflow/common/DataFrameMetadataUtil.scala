package com.pharmdash.workflow.common

import com.pharmdash.workflow.model.GenericReportMetadataField
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._

object DataFrameMetadataUtil {

    def extractMetadataFields(df: DataFrame, censoringFields: Seq[String] = Seq.empty, ignoredFields: Seq[String] = Seq.empty): Seq[GenericReportMetadataField] = {
        df.schema.fields.filterNot(f => ignoredFields.contains(f.name)).map {
            case StructField(name, dataType, nullable, _) =>
                val isArray = dataType.isInstanceOf[ArrayType]
                val isNested = dataType.isInstanceOf[StructType]
                val elementType = if (isArray) dataType.asInstanceOf[ArrayType].elementType else dataType
                val elementNullable = if (isArray) dataType.asInstanceOf[ArrayType].containsNull else nullable
                val isNestedArray = isArray && elementType.isInstanceOf[StructType]
                val isNumeric = elementType.isInstanceOf[NumericType]
                val isContinuous = (Set[DataType](FloatType, DoubleType).contains(elementType)
                    || elementType.isInstanceOf[DecimalType])

                if (isNested || isNestedArray) {
                    throw new IllegalArgumentException(s"Nested/Nested Array field $name is not supported")
                }

                var distinctValuesDf = if (isArray) {
                    df.select(explode(col(name)).as(name))
                } else {
                    df.select(name)
                }
                distinctValuesDf = distinctValuesDf.distinct().sort(name).persist()

                val topNonNullValues = distinctValuesDf.where(col(name).isNotNull).select(col(name).cast(StringType))
                    .take(20000)
                    .map(_.getString(0))
                    .toList

                // count(*) will include null value
                var summaryCols = Seq(count("*").as("totalCount"))
                if (elementNullable) {
                    summaryCols = summaryCols :+ count_if(col(name).isNull).as("nullCount")
                }
                if (isNumeric) {
                    summaryCols = summaryCols ++ Seq(max(name).cast(StringType).as("max"), min(name).cast(StringType).as("min"))
                }
                val summary = distinctValuesDf.agg(summaryCols.head, summaryCols.tail: _*).head()
                val totalCount = summary.getAs[Long]("totalCount")
                val nullCount = if (elementNullable) summary.getAs[Long]("nullCount") else 0
                val hasUnlistedValues = (totalCount - nullCount) > 20000

                val containsNullValue = (nullCount > 0
                    // Try to match elasticsearch logic where empty array is considered as null
                    || (isArray && df.where(col(name).isNull || size(col(name)) === 0).limit(1).count() > 0))

                val (minValue, maxValue) = if (isNumeric) {
                    (summary.getAs[String]("min"), summary.getAs[String]("max"))
                } else (null, null)

                distinctValuesDf.unpersist()

                GenericReportMetadataField(
                    name = name,
                    path = name,
                    values = topNonNullValues,
                    countDistinctIncludingNull = totalCount,
                    min = minValue,
                    max = maxValue,
                    containsNullValue = containsNullValue,
                    hasUnlistedValues = hasUnlistedValues,
                    isCensoringNeeded = censoringFields.contains(name),
                    isNumeric = isNumeric,
                    isContinuous = isContinuous,
                    isArray = isArray
                )
        }
    }

}
