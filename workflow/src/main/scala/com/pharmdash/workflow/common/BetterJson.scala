package com.pharmdash.workflow.common

import org.json4s.JsonAST._
import org.json4s.jackson.Serialization
import org.json4s.{CustomSerializer, Formats, NoTypeHints}

import java.sql.Date

object BetterJson {

    implicit val formats: Formats = Serialization.formats(NoTypeHints) + SqlDateFormat

    case object SqlDateFormat extends CustomSerializer[java.sql.Date](fmt => ( {
        case JString(s) => Date.valueOf(s)
        case JNull => null
    }, {
        case d: Date => JString(d.toString)
    }
    ))

}
