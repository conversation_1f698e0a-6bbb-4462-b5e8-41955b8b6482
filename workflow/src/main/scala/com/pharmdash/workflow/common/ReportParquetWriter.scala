package com.pharmdash.workflow.common

import com.pharmdash.workflow.WorkflowContext
import com.pharmdash.workflow.model.{ClickhouseIngestionJob, GenericReportMetadata}
import com.prospection.arch2.util.FileSystemUtils
import org.apache.hadoop.fs.FileSystem
import org.apache.spark.sql.{Dataset, Row, SaveMode}
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization.write

import java.net.URI

case class ReportParquetWriter(context: WorkflowContext) extends Serializable {
    private val config = context.applicationConfig
    private val metadataBasePath = config.metadataBasePath
    private val warehouseBasePath = config.warehouseBasePath
    private val localStateBasePath = config.localStateBasePath
    private val reportExportRepartitioner = ReportExportRepartitioner(context)

    implicit val formats: DefaultFormats.type = DefaultFormats

    /**
     * @param numRecordPerPartition to repartition dataset
     * @param autoCache if true, persist (aka cache) dataset before repartition, writing, then unpersist after done
     */
    def writeParquet(dataset: Dataset[Row], numRecordPerPartition: Option[Int] = None, autoCache: Boolean = false): String = {
        val target = s"$warehouseBasePath/data-mart/report/${context.workflowId}/node/${context.nodeId}"

        if (autoCache) {
            dataset.persist()
        }

        val repartitionedDataset = reportExportRepartitioner.repartition(dataset, numRecordPerPartition)
        repartitionedDataset
            .write
            .mode(SaveMode.Overwrite)
            .parquet(target)

        if (autoCache) {
            dataset.unpersist()
        }

        target
    }

    def writeMetadata(metadata: GenericReportMetadata): Unit = {
        writeFileToMetadataPath("metaV2.json", write(metadata))
    }

    def writeToCensoringFieldsJson(censoringFields: Seq[String]): Unit = {
        val jsonString = write(censoringFields)
        writeFileToMetadataPath("censoring_fields.json", jsonString)
    }

    def writeExportConfigurationJson(configurationJson: Option[String]): Unit = {
        configurationJson.foreach(writeFileToMetadataPath("configuration.json", _))
    }

    def writeDatasetInfo(datasetInfo: DatasetInfo): Unit = {
        writeFileToMetadataPath("dataset_info.json", write(datasetInfo))
    }

    def writeClickhouseIngestionJobs(ingestionJobs: Seq[ClickhouseIngestionJob]): Unit = {
        writeFileToMetadataPath("clickhouse-ingestion-jobs.json", write(ingestionJobs))
    }

    def setFlagToCreateMetadataInLocal(workflowContext: WorkflowContext): Unit = {
        val fs = FileSystem.get(new URI(localStateBasePath), context.spark.sparkContext.hadoopConfiguration)
        val fileName = s"workflow_${workflowContext.workflowId}_node_${workflowContext.nodeId}"
        FileSystemUtils.writeFile(fs, s"$localStateBasePath/workflow/to_create_metadata/$fileName", fileName)
    }

    private def writeFileToMetadataPath(filenameWithExtension: String, content: String): Unit = {
        val fs = FileSystem.get(new URI(metadataBasePath), context.spark.sparkContext.hadoopConfiguration)

        FileSystemUtils.writeFile(fs, s"$metadataBasePath/${context.workflowId}/node/${context.nodeId}/$filenameWithExtension", content)
    }
}
