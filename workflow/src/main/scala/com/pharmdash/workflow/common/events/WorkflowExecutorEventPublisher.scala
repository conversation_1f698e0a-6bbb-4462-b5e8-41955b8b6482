package com.pharmdash.workflow.common.events

import com.amazonaws.services.sns.model.PublishRequest
import com.amazonaws.services.sns.{AmazonSNS, AmazonSNSClientBuilder}
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.scala.DefaultScalaModule
import com.pharmdash.workflow.WorkflowGlobalProps.PropWorkflowExecutorEventSNSTopicArn
import com.prospection.arch2.config.SparkConfiguration.logger
import com.prospection.arch2.util.ConfigurationFactory

case class WorkflowExecutorEventPublisher(topicArn: String = ConfigurationFactory.getInstance.getString(PropWorkflowExecutorEventSNSTopicArn),
                                          snsClient: AmazonSNS = AmazonSNSClientBuilder.defaultClient()) {

    private val mapper = new ObjectMapper()
        .registerModule(new DefaultScalaModule());

    def publish(workflowEvent: WorkflowExecutorEvent): Unit = {
        if (topicArn == null) {
            throw new IllegalStateException("SNS topic not configured")
        }

        val eventAsJsonString = mapper.writeValueAsString(workflowEvent)

        val request = new PublishRequest(topicArn, eventAsJsonString)
        request.setMessageGroupId(workflowEvent.workflowId.toString)

        snsClient.publish(request)

        logger.info(s"Workflow event message '$eventAsJsonString' sent to topic '$topicArn' ")
    }
}


