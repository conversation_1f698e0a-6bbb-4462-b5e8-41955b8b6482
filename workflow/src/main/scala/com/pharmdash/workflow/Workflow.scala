package com.pharmdash.workflow

import org.apache.logging.log4j.scala.Logger

import javax.validation.constraints.{NotBlank, NotNull}
import scala.collection.mutable.ListBuffer
import scala.language.postfixOps

case class Workflow(id: Long, @NotBlank description: String, nodes: Seq[Node], links: Seq[Link], @NotNull config: WorkflowConfig, dryRun: Boolean = false) {

    def getId: Long = id

    def getLinks: Seq[Link] = links

    def getNodes: Seq[Node] = nodes

    def getConfig: WorkflowConfig = config

    def isDryRun: Boolean = dryRun

    @transient lazy val logger: Logger = Logger(getClass)

    def execute(targetId: Option[Int] = None)(implicit context: WorkflowContext): Unit = {
        buildPlan(targetId)
            .filter(!_.skippable)
            .foreach(_.execute())
    }

    def hasMoreThanOneBranchesContainATerminationNode(links: List[Link]): Boolean = {
        links.count(link => hasATerminationNodeExistFrom(link.sinkNodeId)) > 1
    }

    /**
     * @return a collection of nodes ordered in execution order
     *         <PERSON>'s algorithm:
     *         L ← Empty list that will contain the sorted elements
     *         S ← Set of all nodes with no incoming edge
     *         while S is non-empty do
     *         remove a node n from S
     *         add n to tail of L
     *         for each node m with an edge e from n to m do
     *         remove edge e from the graph
     *         if m has no other incoming edges then
     *         insert m into S
     *         return L
     */
    def buildPlan(targetId: Option[Int] = None): Seq[Node] = {
        if (targetId.nonEmpty)
            planFor(nodes.filter(node => (node.isRootNode && node.id == targetId.get) || node.dependencyIds().contains(targetId.get)))
        else
            planFor(nodes)
    }

    private def planFor(nodes: Seq[Node]) = {
        val result: ListBuffer[Node] = ListBuffer() // L
        val rootNodes: ListBuffer[Node] = ListBuffer(nodes.filter(_.isRootNode).toList: _*) // S
        val visitedLink: ListBuffer[Link] = ListBuffer()

        while (rootNodes.nonEmpty) {
            val currentNode = rootNodes.head
            rootNodes.remove(0)
            result += currentNode
            currentNode.outputLinks.foreach(link => {
                visitedLink += link
                if (link.sink.inputLinks.count(!visitedLink.contains(_)) == 0)
                    rootNodes += link.sink
            })
        }
        result
    }

    def print(): Unit = {
        println(links.map(_.toString).mkString("\n"))
    }

    private def findNode(nodeId: Long): Option[Node] = {
        nodes.find((node: Node) => node.getId == nodeId)
    }

    // find source nodes to targetNodeId that satisfies the predicate
    def findNodesTo(targetNodeId: Long, predicate: Function[Node, Boolean]): Seq[Node] = {
        findNode(targetNodeId).map((node: Node) => findNodesTo(node, predicate, scala.collection.mutable.Set[Long]())).getOrElse(Seq[Node]())
    }

    private def findNodesTo(targetNode: Node, predicate: Function[Node, Boolean], visitedNodes: scala.collection.mutable.Set[Long]): Seq[Node] = {
        if (visitedNodes.contains(targetNode.getId)) {
            Stream.empty
        }
        else {
            if (predicate(targetNode)) {
                visitedNodes.add(targetNode.getId)
                Seq(targetNode)
            }
            else { // breadth first search
                links.filter(link => link.sinkNodeId == targetNode.getId)
                    .map(link => findNode(link.sourceNodeId))
                    .filter(_.nonEmpty)
                    .map(_.get)
                    .flatMap((node: Node) => findNodesTo(node, predicate, visitedNodes))
            }
        }
    }

    override def toString = s"Workflow($id, $description, $config)"

    def getWorkflowProperty(key: String): Option[String] = {
        Option(config).map(_.properties).flatMap(_.get(key))
    }

    private def hasATerminationNodeExistFrom(nodeId: Long): Boolean = {
        val linksFromSourceNode = getLinksFromSourceNode(nodeId)
        if (linksFromSourceNode.isEmpty) {
            isTerminationNode(nodeId)
        } else {
            linksFromSourceNode.exists(link => hasATerminationNodeExistFrom(link.sinkNodeId))
        }
    }

    private def getLinksFromSourceNode(nodeId: Long): Seq[Link] = {
        links.filter(_.sourceNodeId == nodeId)
    }

    private def isTerminationNode(nodeId: Long): Boolean = {
        getNodeByNodeIdMap.get(nodeId).exists(_.isTerminationNode)
    }

    private def getNodeByNodeIdMap: Map[Long, Node] = {
        nodes.map(node => node.getId -> node).toMap
    }
}
