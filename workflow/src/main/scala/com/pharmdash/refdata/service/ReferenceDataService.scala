package com.pharmdash.refdata.service

import com.pharmdash.refdata.dto.{DuplicateItemsByItemGroupsDto, ItemToItemGroupDto}

import scala.language.postfixOps

trait ReferenceDataService {
    def getItemGroupIds(refDataVersionId: String, itemGroupIds: Seq[String]): Set[String]
    def findDuplicatedItems(refDataVersionId: String, itemGroupIds: Seq[String]): Seq[DuplicateItemsByItemGroupsDto]
    def findPublishedItemToItemGroups(refDataVersionId: String, itemGroupIds: Seq[String]): Seq[ItemToItemGroupDto]
}
