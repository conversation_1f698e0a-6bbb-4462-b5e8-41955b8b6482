package com.pharmdash.workflow.dp.javadto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ViolationDTO {
    public static final String FATAL = "FATAL";
    public static final String ERROR = "ERROR";
    public static final String WARNING = "WARNING";

    String type;
    Long nodeId;
    String message;
    String debugWorkflowProperty;
    boolean displayAsCodeBlock;

    public ViolationDTO(String type, Long nodeId, String message, boolean displayAsCodeBlock) {
        this(type, nodeId, message, null, displayAsCodeBlock);
    }

    public static ViolationDTO error(Long nodeId, String message) {
        return new ViolationDTO(ERROR, nodeId, message, null, false);
    }

    public static ViolationDTO errorAsCodeBlock(Long nodeId, String message) {
        return new ViolationDTO(ERROR, nodeId, message, null, true);
    }

    public static ViolationDTO warning(Long nodeId, String message) {
        return new ViolationDTO(WARNING, nodeId, message, null, false);
    }

    public static ViolationDTO warningDebugWorkflowProperties(String message, String property) {
        return new ViolationDTO(WARNING, null, message, property, false);
    }

    public boolean isError() {
        return ERROR.equals(type) || FATAL.equals(type);
    }
}
