package com.pharmdash.workflow.dp.meta.processorannotations;

import java.lang.annotation.*;

import static java.lang.annotation.ElementType.ANNOTATION_TYPE;
import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Retention(RetentionPolicy.RUNTIME)
@Target(value = {TYPE, ANNOTATION_TYPE})
@Repeatable(Outputs.class)
public @interface Output {

    String name();

    DataStreamType streamType();

    Tags tags() default @Tags(eventTags = {}, subjectTags = {});

    @Target({FIELD, METHOD, PARAMETER, ANNOTATION_TYPE})
    @Retention(RUNTIME)
    @Documented
    @interface List {
        Output[] value() default {};
    }
}
