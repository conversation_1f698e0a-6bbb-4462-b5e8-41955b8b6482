package com.pharmdash.workflow.dp.javadto;

import com.pharmdash.workflow.dp.meta.processorannotations.Report;
import com.pharmdash.workflow.dp.meta.processorannotations.ReportType;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.Optional;

@Slf4j
public class NodeDTO {
    private long id;
    private String name;
    private String resource;
    private PropertiesDTO properties = new PropertiesDTO();

    // Below fields are for checkpoint nodes only
    private boolean shouldResume;
    private boolean skippable;
    private String upstreamNodesHash;

    private Long upstreamDatasetId;

    public NodeDTO() {
    }

    public long getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    public String getResource() {
        return this.resource;
    }

    public PropertiesDTO getProperties() {
        return this.properties;
    }

    public void setId(long id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public void setProperties(PropertiesDTO properties) {
        this.properties = properties;
    }

    public boolean getShouldResume() {
        return shouldResume;
    }

    public void setShouldResume(boolean shouldResume) {
        this.shouldResume = shouldResume;
    }

    public boolean getSkippable() {
        return skippable;
    }

    public void setSkippable(boolean skippable) {
        this.skippable = skippable;
    }

    public String getUpstreamNodesHash() {
        return upstreamNodesHash;
    }

    public void setUpstreamNodesHash(String upstreamNodesHash) {
        this.upstreamNodesHash = upstreamNodesHash;
    }

    public Long getUpstreamDatasetId() {
        return upstreamDatasetId;
    }

    public void setUpstreamDatasetId(Long datasetId) {
        this.upstreamDatasetId = datasetId;
    }

    public boolean isCheckpointNode() {
        return "com.pharmdash.workflow.dp.exporter.Checkpoint".equals(resource);
    }

    public boolean isNoGenerationRequired() {
        return isFacilityInsightsNode();
    }

    public boolean isGenerationRequiredForReportNode() {
        try {
            return isReportNode() && !Class.forName(resource).getAnnotation(Report.class).reportType()
                .equals(ReportType.NO_GENERATION_REQUIRED);
        } catch (ClassNotFoundException e) {
            log.error("Error while checking if report node needs generation", e);
        }
        return false;
    }

    public boolean isFacilityInsightsNode() {
        return "com.pharmdash.workflow.dp.report.FacilityInsightsReport".equals(resource);
    }

    public boolean isReportNode() {
        try {
            return Class.forName(resource).isAnnotationPresent(Report.class);
        } catch (ClassNotFoundException e) {
            // Ignoring exception, this should not happen
            return false;
        }
    }

    public Optional<ReportType> getReportType() {
        try {
            Report report = Class.forName(resource).getAnnotation(Report.class);
            return Optional.ofNullable(report).map(Report::reportType);
        } catch (ClassNotFoundException e) {
            // Ignoring exception, this should not happen
            return Optional.empty();
        }
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof NodeDTO)) return false;
        final NodeDTO other = (NodeDTO) o;
        if (!other.canEqual((Object) this)) return false;
        if (this.getId() != other.getId()) return false;
        final Object this$name = this.getName();
        final Object other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) return false;
        final Object this$resource = this.getResource();
        final Object other$resource = other.getResource();
        if (this$resource == null ? other$resource != null : !this$resource.equals(other$resource)) return false;
        final Object this$properties = this.getProperties();
        final Object other$properties = other.getProperties();
        if (this$properties == null ? other$properties != null : !this$properties.equals(other$properties))
            return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof NodeDTO;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, resource, properties);
    }

    public String toString() {
        return "NodeDTO(id=" + this.getId() + ", name=" + this.getName() + ", resource=" + this.getResource() + ", properties=" + this.getProperties() + ", resumable=" + this.getShouldResume() + ", skippable=" + this.getSkippable() + ", upstreamNodesHash=" + this.getUpstreamNodesHash() + ")";
    }


}
