package com.pharmdash.workflow.dp.javadto;

import java.util.Objects;

public class LinkDTO {
    private long id;
    private long sourceNodeId;
    private long sinkNodeId;
    private String sourceChannel;
    private String sinkChannel;

    public static LinkDTOBuilder builder() {
        return new LinkDTOBuilder();
    }

    public LinkDTO() {
    }

    public LinkDTO(long id, long sourceNodeId, long sinkNodeId, String sourceChannel, String sinkChannel) {
        this.id = id;
        this.sourceNodeId = sourceNodeId;
        this.sinkNodeId = sinkNodeId;
        this.sourceChannel = sourceChannel;
        this.sinkChannel = sinkChannel;
    }

    public long getId() {
        return this.id;
    }

    public long getSourceNodeId() {
        return this.sourceNodeId;
    }

    public long getSinkNodeId() {
        return this.sinkNodeId;
    }

    public String getSourceChannel() {
        return this.sourceChannel;
    }

    public String getSinkChannel() {
        return this.sinkChannel;
    }

    public void setId(long id) {
        this.id = id;
    }

    public void setSourceNodeId(long sourceNodeId) {
        this.sourceNodeId = sourceNodeId;
    }

    public void setSinkNodeId(long sinkNodeId) {
        this.sinkNodeId = sinkNodeId;
    }

    public void setSourceChannel(String sourceChannel) {
        this.sourceChannel = sourceChannel;
    }

    public void setSinkChannel(String sinkChannel) {
        this.sinkChannel = sinkChannel;
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof LinkDTO)) return false;
        final LinkDTO other = (LinkDTO) o;
        if (!other.canEqual((Object) this)) return false;
        if (this.getId() != other.getId()) return false;
        if (this.getSourceNodeId() != other.getSourceNodeId()) return false;
        if (this.getSinkNodeId() != other.getSinkNodeId()) return false;
        final Object this$sourceChannel = this.getSourceChannel();
        final Object other$sourceChannel = other.getSourceChannel();
        if (this$sourceChannel == null ? other$sourceChannel != null : !this$sourceChannel.equals(other$sourceChannel))
            return false;
        final Object this$sinkChannel = this.getSinkChannel();
        final Object other$sinkChannel = other.getSinkChannel();
        if (this$sinkChannel == null ? other$sinkChannel != null : !this$sinkChannel.equals(other$sinkChannel))
            return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof LinkDTO;
    }

    public String toString() {
        return "LinkDTO(id=" + this.getId() + ", sourceNodeId=" + this.getSourceNodeId() + ", sinkNodeId=" + this.getSinkNodeId() + ", sourceChannel=" + this.getSourceChannel() +
            ", sinkChannel=" + this.getSinkChannel() + ")";
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, sourceNodeId, sinkNodeId, sourceChannel, sinkChannel);
    }

    public static class LinkDTOBuilder {
        private long id;
        private long sourceNodeId;
        private long sinkNodeId;
        private String sourceChannel;
        private String sinkChannel;

        LinkDTOBuilder() {
        }

        public LinkDTOBuilder id(long id) {
            this.id = id;
            return this;
        }

        public LinkDTOBuilder sourceNodeId(long sourceNodeId) {
            this.sourceNodeId = sourceNodeId;
            return this;
        }

        public LinkDTOBuilder sinkNodeId(long sinkNodeId) {
            this.sinkNodeId = sinkNodeId;
            return this;
        }

        public LinkDTOBuilder sourceChannel(String sourceChannel) {
            this.sourceChannel = sourceChannel;
            return this;
        }

        public LinkDTOBuilder sinkChannel(String sinkChannel) {
            this.sinkChannel = sinkChannel;
            return this;
        }

        public LinkDTO build() {
            return new LinkDTO(this.id, this.sourceNodeId, this.sinkNodeId, this.sourceChannel, this.sinkChannel);
        }

        public String toString() {
            return "LinkDTO.LinkDTOBuilder(id=" + this.id + ", sourceNodeId=" + this.sourceNodeId + ", sinkNodeId=" + this.sinkNodeId + ", sourceChannel=" + this.sourceChannel + ", sinkChannel=" + this.sinkChannel + ")";
        }
    }
}
