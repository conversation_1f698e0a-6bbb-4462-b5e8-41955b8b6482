package com.pharmdash.workflow.dp.javadto;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.util.Collections;
import java.util.Map;

/**
 * Placeholder for any additional workflow config which needs to be provided.
 */
public class WorkflowConfigDTO {

    private String refDataVersionId;

    private RefDataManagementVersion refDataManagementVersion;

    private String refDataPath;

    private String territoryFilePath;

    private Map<String, String> properties = Collections.emptyMap();

    public WorkflowConfigDTO() {
    }

    public Map<String, String> getProperties() {
        return this.properties;
    }

    public void setProperties(Map<String, String> properties) {
        this.properties = properties;
    }

    public String getRefDataPath() {
        return this.refDataPath;
    }

    public void setRefDataPath(String refDataPath) {
        this.refDataPath = refDataPath;
    }

    public String getRefDataVersionId() {
        return refDataVersionId;
    }

    public void setRefDataVersionId(String refDataVersionId) {
        this.refDataVersionId = refDataVersionId;
    }

    public RefDataManagementVersion getRefDataManagementVersion() {
        return this.refDataManagementVersion;
    }

    public void setRefDataManagementVersion(RefDataManagementVersion refDataManagementVersion) {
        this.refDataManagementVersion = refDataManagementVersion;
    }
    public String getTerritoryFilePath() { return territoryFilePath; }

    public void setTerritoryFilePath(String territoryFilePath) {
        this.territoryFilePath = territoryFilePath;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof WorkflowConfigDTO;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        WorkflowConfigDTO that = (WorkflowConfigDTO) o;

        return new EqualsBuilder()
            .append(refDataVersionId, that.refDataVersionId)
            .append(refDataManagementVersion, that.refDataManagementVersion)
            .append(refDataPath, that.refDataPath)
            .append(properties, that.properties)
            .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
            .append(refDataVersionId)
            .append(refDataManagementVersion)
            .append(refDataPath)
            .append(properties)
            .toHashCode();
    }

    @Override
    public String toString() {
        return "WorkflowConfigDTO{" +
            "refDataVersionId=" + refDataVersionId +
            ", refDataManagementVersion='" + refDataManagementVersion + '\'' +
            ", refDataPath='" + refDataPath + '\'' +
            ", properties=" + properties +
            '}';
    }

    public enum RefDataManagementVersion {
        V1,
        V2
    }
}
