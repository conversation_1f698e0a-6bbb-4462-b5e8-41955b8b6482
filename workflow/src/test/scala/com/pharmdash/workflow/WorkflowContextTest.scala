package com.pharmdash.workflow

import com.pharmdash.workflow.dp.importer.PatientParquetImporter
import com.pharmdash.workflow.dp.javadto.DatasetDTO
import com.pharmdash.workflow.service.WorkflowMapper
import com.pharmdash.workflow.test.WorkflowTestUtils.{node, nodeId}
import org.junit.runner.RunWith
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.junit.JUnitRunner


@RunWith(classOf[JUnitRunner])
class WorkflowContextTest extends AnyFunSuite with Matchers {

    def testDatasetWithRecencyIncompleteMonthsSetting(recencyIncompleteMonths: Integer, recencyIncompleteMonthsForCensoring: Integer): DatasetDTO = {
        DatasetDTO.builder()
            .id(1030244)
            .name("test")
            .sourcePath("user/technical-model")
            .scalingFactor(0.0)
            .recencyIncompleteMonths(recencyIncompleteMonths)
            .recencyIncompleteMonthsForCensoring(recencyIncompleteMonthsForCensoring)
            .build()
    }

    def testDatasetNodeWithUpstreamDataset(dataset: DatasetDTO): Node = {
        val nodeDTO = node(1, nodeId(classOf[PatientParquetImporter]), """{"dataset":{"sourcePath":"user/technical-model","id":1030244,"version":"current"}}""")
        nodeDTO.setUpstreamDatasetId(dataset.getId())
        WorkflowMapper.nodeFromDTO(nodeDTO, dataset)
    }


}
