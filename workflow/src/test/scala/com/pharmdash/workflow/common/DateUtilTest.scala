package com.pharmdash.workflow.common

import com.pharmdash.workflow.common.DateUtil._
import org.junit.runner.RunWith
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.junit.JUnitRunner

import java.sql.Date
import java.time.LocalDate

@RunWith(classOf[JUnitRunner])
class DateUtilTest extends AnyFunSpec with Matchers {

    describe("DateUtil") {

        it("should be clearly correct") {
            val a = Date.valueOf("1970-01-01")
            val b = Date.valueOf("1970-01-02")
            later(a, b) should be(b)
            earlier(a, b) should be(a)
            later(toLocalDate(a), toLocalDate(b)) should be(toLocalDate(b))
            earlier(toLocalDate(a), toLocalDate(b)) should be(toLocalDate(a))
        }

        it("should work with equal dates") {
            val a = Date.valueOf("1970-01-01")
            val b = Date.valueOf("1970-01-01")
            later(a, b) shouldEqual a
            earlier(a, b) shouldEqual a
            later(toLocalDate(a), toLocalDate(b)) shouldEqual (toLocalDate(a))
            earlier(toLocalDate(a), toLocalDate(b)) shouldEqual (toLocalDate(a))
        }

        it("should work with a null date") {
            val a = Date.valueOf("1970-01-01")
            val b = null
            later(a, b) shouldEqual a
            earlier(a, b) shouldEqual a
            later(toLocalDate(a), toLocalDate(b)) shouldEqual (toLocalDate(a))
            earlier(toLocalDate(a), toLocalDate(b)) shouldEqual (toLocalDate(a))
        }

        it("should work with the other null date") {
            val a = null
            val b = Date.valueOf("1970-01-01")
            later(a, b) shouldEqual b
            earlier(a, b) shouldEqual b
            later(toLocalDate(a), toLocalDate(b)) shouldEqual (toLocalDate(b))
            earlier(toLocalDate(a), toLocalDate(b)) shouldEqual (toLocalDate(b))
        }

        it("should work with both null dates?") {
            val a: Date = null
            val b: Date = null
            later(a, b) shouldEqual null
            earlier(a, b) shouldEqual null
            later(toLocalDate(a), toLocalDate(b)) shouldEqual (null)
            earlier(toLocalDate(a), toLocalDate(b)) shouldEqual (null)
        }

        it("should return true on valid date string") {
            val dateString = "2020-12-16"
            isValidDateString(dateString) shouldBe true
        }

        it("should return false on invalid date string") {
            val dateString = "2020:12:16"
            isValidDateString(dateString) shouldBe false
        }
    }


    describe("DateUtil.distanceInWholeApproximatedMonths") {
        it("should treat 0 day distance as 0 month") {
            val date0 = dateFromNow(0)
            val date = dateFromNow(0)
            distanceInWholeApproximatedMonths(date0, date) shouldEqual 0
        }

        it("should treat 1 day distance as 0 month") {
            val date0 = dateFromNow(0)
            val date = dateFromNow(1)
            distanceInWholeApproximatedMonths(date0, date) shouldEqual 0
        }

        it("should treat 29 day distance as 0 month") {
            val date0 = dateFromNow(0)
            val date = dateFromNow(29)
            distanceInWholeApproximatedMonths(date0, date) shouldEqual 0
        }

        it("should treat 30 day distance as 1 month") {
            val date0 = dateFromNow(0)
            val date = dateFromNow(30)
            distanceInWholeApproximatedMonths(date0, date) shouldEqual 1
        }

        it("should treat 31 day distance as 1 month") {
            val date0 = dateFromNow(0)
            val date = dateFromNow(31)
            distanceInWholeApproximatedMonths(date0, date) shouldEqual 1
        }

        it("should treat -1 day distance as 0 month") {
            val date0 = dateFromNow(0)
            val date = dateFromNow(-1)
            distanceInWholeApproximatedMonths(date0, date) shouldEqual 0
        }

        it("should treat -29 day distance as 0 month") {
            val date0 = dateFromNow(0)
            val date = dateFromNow(-29)
            distanceInWholeApproximatedMonths(date0, date) shouldEqual 0
        }

        it("should treat -30 day distance as -1 month") {
            val date0 = dateFromNow(0)
            val date = dateFromNow(-30)
            distanceInWholeApproximatedMonths(date0, date) shouldEqual -1
        }

        it("should treat -31 day distance as -1 month") {
            val date0 = dateFromNow(0)
            val date = dateFromNow(-31)
            distanceInWholeApproximatedMonths(date0, date) shouldEqual -1
        }
    }

    def dateFromNow(daysToAdd: Long): Date = {
        Date.valueOf(LocalDate.now().plusDays(daysToAdd))
    }
}
