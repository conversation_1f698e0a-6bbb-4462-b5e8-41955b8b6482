package com.pharmdash.workflow.common

import com.pharmdash.workflow.common.CsvRecordLite.{Headers, toCsvRecordLite}
import org.apache.commons.csv.CSVRecord
import org.mockito.MockitoSugar.when
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class CsvRecordLiteTest extends AnyFunSuite with Matchers with MockitoSugar {

    test("get should return the value at the given existing index") {
        // setup
        val headers = Headers(Seq("a", "b"))
        val record = CsvRecordLite(headers, 1, Seq("1", "2"))

        // test & verify
        record.get(1) should be("2")
    }

    test("get should throw if the given index is out of bounds of the record values") {
        // setup
        val headers = Headers(Seq("a", "b"))
        val record = CsvRecordLite(headers, 1, Seq("1"))

        // test
        val thrown = intercept[IndexOutOfBoundsException] {
            record.get(1)
        }

        // verify
        thrown.getMessage should be("CSVRecord only has 1 value!")
    }

    test("get should return the value for the given existing header") {
        // setup
        val headers = Headers(Seq("a", "b"))
        val record = CsvRecordLite(headers, 1, Seq("1", "2"))

        // test & verify
        record.get("b") should be("2")
    }

    test("get should throw if the given header's index is out of bounds of the record values") {
        // setup
        val headers = Headers(Seq("a", "b"))
        val record = CsvRecordLite(headers, 1, Seq("1"))

        // test
        val thrown = intercept[IndexOutOfBoundsException] {
            record.get("b")
        }

        // verify
        thrown.getMessage should be("Index for header 'b' is 1 but record only has 1 value!")
    }

    test("get should throw if the given header does not exist in the headers") {
        // setup
        val headers = Headers(Seq("a", "b"))
        val record = CsvRecordLite(headers, 1, Seq("1"))

        // test
        val thrown = intercept[IllegalArgumentException] {
            record.get("c")
        }

        // verify
        thrown.getMessage should be("Header 'c' not found, expected one of: 'a', 'b'")
    }

    test("getOption should return the value at the given existing index") {
        // setup
        val headers = Headers(Seq("a", "b"))
        val record = CsvRecordLite(headers, 1, Seq("1", "2"))

        // test & verify
        record.getOption(1) should be(Some("2"))
    }

    test("getOption should return None if the given index is out of bounds of the record values") {
        // setup
        val headers = Headers(Seq("a", "b"))
        val record = CsvRecordLite(headers, 1, Seq("1"))

        // test
        val result = record.getOption(1)

        // verify
        result should be(None)
    }

    test("getOption should return the value for the given existing header") {
        // setup
        val headers = Headers(Seq("a", "b"))
        val record = CsvRecordLite(headers, 1, Seq("1", "2"))

        // test & verify
        record.getOption("b") should be(Some("2"))
    }

    test("getOption should return None if the given header does not exist in the headers") {
        // setup
        val headers = Headers(Seq("a", "b"))
        val record = CsvRecordLite(headers, 1, Seq("1", "2"))

        // test
        val result = record.getOption("c")

        // verify
        result should be(None)
    }

    test("getOption should return None if the given header does not exist in the record") {
        // setup
        val headers = Headers(Seq("a", "b"))
        val record = CsvRecordLite(headers, 1, Seq("1"))

        // test
        val result = record.getOption("b")

        // verify
        result should be(None)
    }

    test("toMap should return a map of header names to values even if there are less values than headers") {
        // setup
        val headers = Headers(Seq("a", "b", "c"))
        val record = CsvRecordLite(headers, 1, Seq("1", "2"))

        // test
        val result = record.toMap

        // verify
        result should be(Map("a" -> "1", "b" -> "2"))
    }

    test("toCsvRecordLite should keep blank string when convertBlankStringToNull is false") {
        // setup
        val headers = Headers(Seq("a", "b"))
        val record = mock[CSVRecord]
        when(record.getRecordNumber).thenReturn(1)
        when(record.values()).thenReturn(Array("a", ""))

        // test
        val result = toCsvRecordLite(headers, record, blankStringValueAsNull = false)

        // verify
        result should be(CsvRecordLite(headers, 1, Seq("a", "")))
    }

    test("toCsvRecordLite should convert blank string to null when convertBlankStringToNull is true") {
        // setup
        val headers = Headers(Seq("a", "b", "b"))
        val record = mock[CSVRecord]
        when(record.getRecordNumber).thenReturn(1)
        when(record.values()).thenReturn(Array("a", " ", ""))

        // test
        val result = toCsvRecordLite(headers, record, blankStringValueAsNull = true)

        // verify
        result should be(CsvRecordLite(headers, 1, Seq("a", null, null)))
    }

}
