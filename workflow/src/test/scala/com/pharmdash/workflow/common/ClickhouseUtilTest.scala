package com.pharmdash.workflow.common

import com.pharmdash.workflow.common.ClickhouseUtil.{ClickhouseColumnDefinition, generateMergeTreeTableCTASIngestionJob}
import com.pharmdash.workflow.common.ClickhouseUtilTest.{EXPECTED_TYPE_MAPPING, PARQUET_S3_URL, TABLE_NAME}
import com.pharmdash.workflow.model.{ClickhouseIngestionJob, GenericReportMetadataField}
import com.pharmdash.workflow.test.AbstractDataProcessorTestCase
import org.apache.spark.sql.Row
import org.apache.spark.sql.types._
import org.junit.runner.RunWith
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.junit.JUnitRunner
import org.scalatestplus.mockito.MockitoSugar

import java.net.URL

object ClickhouseUtilTest {
    private val TABLE_NAME = "table_name"
    private val PARQUET_S3_URL = "https://a.b.c"
    private val EXPECTED_TYPE_MAPPING = Map[DataType, String](
        BooleanType -> "Bool",
        ByteType -> "Int8",
        ShortType -> "Int16",
        IntegerType -> "Int32",
        LongType -> "Int64",
        FloatType -> "Float32",
        DoubleType -> "Float64",
        StringType -> "String"
    )
}

@RunWith(classOf[JUnitRunner])
class ClickhouseUtilTest extends AbstractDataProcessorTestCase with Matchers with MockitoSugar {
    import spark.implicits._

    test("generateMergeTreeTableCTASIngestionJob should return correct number of rows, table and parquetS3Path") {
        val df = Seq(1, 2, 3).toDF("Int")
        generateMergeTreeTableCTASIngestionJob(df, TABLE_NAME, new URL(PARQUET_S3_URL)) should be (
            ClickhouseIngestionJob(
                TABLE_NAME,
                s"""
                   |CREATE TABLE $TABLE_NAME
                   |(
                   |    Int Int32
                   |) ENGINE = MergeTree
                   |ORDER BY tuple()
                   |AS
                   |SELECT Int
                   |FROM s3(
                   |        'https://a.b.c/*.parquet',
                   |        'Parquet'
                   |     )
                   |""".stripMargin.trim,
                PARQUET_S3_URL,
                3
            )
        )
    }

    for ((sparkType, expectedType) <- EXPECTED_TYPE_MAPPING) {
        test(s"generateMergeTreeTableCTASIngestionJob ctasStatement should convert $sparkType to $expectedType correctly with nullable and array") {
            val df = spark.createDataFrame(
                spark.sparkContext.parallelize(Seq[Row]()),
                StructType(Seq(
                    StructField("nonnull", sparkType, nullable = false),
                    StructField("nullable", sparkType, nullable = true),
                    StructField("nonnull_array_nonnull_element", ArrayType(sparkType, containsNull = false), nullable = false),
                    StructField("nonnull_array_nullable_element", ArrayType(sparkType, containsNull = true), nullable = false),
                    StructField("nullable_array_nonnull_element", ArrayType(sparkType, containsNull = false), nullable = true),
                    StructField("nullable_array_nullable_element", ArrayType(sparkType, containsNull = true), nullable = true),
                ))
            )
            generateMergeTreeTableCTASIngestionJob(df, TABLE_NAME, new URL(PARQUET_S3_URL)).ctasStatement should be (
                s"""
                   |CREATE TABLE $TABLE_NAME
                   |(
                   |    nonnull $expectedType,
                   |    nullable Nullable($expectedType),
                   |    nonnull_array_nonnull_element Array($expectedType),
                   |    nonnull_array_nullable_element Array(Nullable($expectedType)),
                   |    nullable_array_nonnull_element Array($expectedType),
                   |    nullable_array_nullable_element Array(Nullable($expectedType))
                   |) ENGINE = MergeTree
                   |ORDER BY tuple()
                   |AS
                   |SELECT nonnull,
                   |       nullable,
                   |       nonnull_array_nonnull_element,
                   |       nonnull_array_nullable_element,
                   |       coalesce(nullable_array_nonnull_element, []),
                   |       coalesce(nullable_array_nullable_element, [])
                   |FROM s3(
                   |        'https://a.b.c/*.parquet',
                   |        'Parquet'
                   |     )
                   |""".stripMargin.trim,
            )
        }
    }

    test("generateMergeTreeTableCTASIngestionJob ctasStatement should use containsNullValue = true from metadata provided to further determine nullability for nullable field only") {
        val df = spark.createDataFrame(
            spark.sparkContext.parallelize(Seq[Row]()),
            StructType(Seq(
                StructField("nonnull", IntegerType, nullable = false),
                StructField("nullable", IntegerType, nullable = true),
                StructField("nonnull_array_nonnull_element", ArrayType(IntegerType, containsNull = false), nullable = false),
                StructField("nonnull_array_nullable_element", ArrayType(IntegerType, containsNull = true), nullable = false),
                StructField("nullable_array_nonnull_element", ArrayType(IntegerType, containsNull = false), nullable = true),
                StructField("nullable_array_nullable_element", ArrayType(IntegerType, containsNull = true), nullable = true),
            ))
        )

        generateMergeTreeTableCTASIngestionJob(df, TABLE_NAME, new URL(PARQUET_S3_URL), metadataFields = Seq(
            GenericReportMetadataField("nonnull", null, null, 1, containsNullValue = true),
            GenericReportMetadataField("nullable", null, null, 1, containsNullValue = true),
            GenericReportMetadataField("nonnull_array_nonnull_element", null, null, 1, containsNullValue = true),
            GenericReportMetadataField("nonnull_array_nullable_element", null, null, 1, containsNullValue = true),
            GenericReportMetadataField("nullable_array_nonnull_element", null, null, 1, containsNullValue = true),
            GenericReportMetadataField("nullable_array_nullable_element", null, null, 1, containsNullValue = true),
        )).ctasStatement should be(
            s"""
               |CREATE TABLE $TABLE_NAME
               |(
               |    nonnull Int32,
               |    nullable Nullable(Int32),
               |    nonnull_array_nonnull_element Array(Int32),
               |    nonnull_array_nullable_element Array(Nullable(Int32)),
               |    nullable_array_nonnull_element Array(Int32),
               |    nullable_array_nullable_element Array(Nullable(Int32))
               |) ENGINE = MergeTree
               |ORDER BY tuple()
               |AS
               |SELECT nonnull,
               |       nullable,
               |       nonnull_array_nonnull_element,
               |       nonnull_array_nullable_element,
               |       coalesce(nullable_array_nonnull_element, []),
               |       coalesce(nullable_array_nullable_element, [])
               |FROM s3(
               |        'https://a.b.c/*.parquet',
               |        'Parquet'
               |     )
               |""".stripMargin.trim,
        )
    }

    test("generateMergeTreeTableCTASIngestionJob ctasStatement should use containsNullValue = false from metadata provided to further determine nullability for nullable field only") {
        val df = spark.createDataFrame(
            spark.sparkContext.parallelize(Seq[Row]()),
            StructType(Seq(
                StructField("nonnull", IntegerType, nullable = false),
                StructField("nullable", IntegerType, nullable = true),
                StructField("nonnull_array_nonnull_element", ArrayType(IntegerType, containsNull = false), nullable = false),
                StructField("nonnull_array_nullable_element", ArrayType(IntegerType, containsNull = true), nullable = false),
                StructField("nullable_array_nonnull_element", ArrayType(IntegerType, containsNull = false), nullable = true),
                StructField("nullable_array_nullable_element", ArrayType(IntegerType, containsNull = true), nullable = true),
            ))
        )

        generateMergeTreeTableCTASIngestionJob(df, TABLE_NAME, new URL(PARQUET_S3_URL), metadataFields = Seq(
            GenericReportMetadataField("nonnull", null, null, 1, containsNullValue = false),
            GenericReportMetadataField("nullable", null, null, 1, containsNullValue = false),
            GenericReportMetadataField("nonnull_array_nonnull_element", null, null, 1, containsNullValue = false),
            GenericReportMetadataField("nonnull_array_nullable_element", null, null, 1, containsNullValue = false),
            GenericReportMetadataField("nullable_array_nonnull_element", null, null, 1, containsNullValue = false),
            GenericReportMetadataField("nullable_array_nullable_element", null, null, 1, containsNullValue = false),
        )).ctasStatement should be (
            s"""
               |CREATE TABLE $TABLE_NAME
               |(
               |    nonnull Int32,
               |    nullable Int32,
               |    nonnull_array_nonnull_element Array(Int32),
               |    nonnull_array_nullable_element Array(Int32),
               |    nullable_array_nonnull_element Array(Int32),
               |    nullable_array_nullable_element Array(Int32)
               |) ENGINE = MergeTree
               |ORDER BY tuple()
               |AS
               |SELECT nonnull,
               |       nullable,
               |       nonnull_array_nonnull_element,
               |       nonnull_array_nullable_element,
               |       nullable_array_nonnull_element,
               |       nullable_array_nullable_element
               |FROM s3(
               |        'https://a.b.c/*.parquet',
               |        'Parquet'
               |     )
               |""".stripMargin.trim,
        )
    }

    test("generateMergeTreeTableCTASIngestionJob ctasStatement should use LowCardinality for String type and when metadata is provided and count less than threshold only") {
        val df = spark.createDataFrame(
            spark.sparkContext.parallelize(Seq[Row]()),
            StructType(Seq(
                StructField("nonnull", StringType, nullable = false),
                StructField("nullable", StringType, nullable = true),
                StructField("nonnull_array_nonnull_element", ArrayType(StringType, containsNull = false), nullable = false),
                StructField("nonnull_array_nullable_element", ArrayType(StringType, containsNull = true), nullable = false),
                StructField("nullable_array_nonnull_element", ArrayType(StringType, containsNull = false), nullable = true),
                StructField("nullable_array_nullable_element", ArrayType(StringType, containsNull = true), nullable = true),
            ))
        )
        generateMergeTreeTableCTASIngestionJob(df, TABLE_NAME, new URL(PARQUET_S3_URL), metadataFields = Seq(
            GenericReportMetadataField("nonnull", null, null, 9999, containsNullValue = true),
            GenericReportMetadataField("nullable", null, null, 9999, containsNullValue = true),
            GenericReportMetadataField("nonnull_array_nonnull_element", null, null, 9999, containsNullValue = true),
            GenericReportMetadataField("nonnull_array_nullable_element", null, null, 9999, containsNullValue = true),
            GenericReportMetadataField("nullable_array_nonnull_element", null, null, 9999, containsNullValue = true),
            GenericReportMetadataField("nullable_array_nullable_element", null, null, 9999, containsNullValue = true),
        )).ctasStatement should be (
            s"""
               |CREATE TABLE $TABLE_NAME
               |(
               |    nonnull LowCardinality(String),
               |    nullable LowCardinality(Nullable(String)),
               |    nonnull_array_nonnull_element Array(LowCardinality(String)),
               |    nonnull_array_nullable_element Array(LowCardinality(Nullable(String))),
               |    nullable_array_nonnull_element Array(LowCardinality(String)),
               |    nullable_array_nullable_element Array(LowCardinality(Nullable(String)))
               |) ENGINE = MergeTree
               |ORDER BY tuple()
               |AS
               |SELECT nonnull,
               |       nullable,
               |       nonnull_array_nonnull_element,
               |       nonnull_array_nullable_element,
               |       coalesce(nullable_array_nonnull_element, []),
               |       coalesce(nullable_array_nullable_element, [])
               |FROM s3(
               |        'https://a.b.c/*.parquet',
               |        'Parquet'
               |     )
               |""".stripMargin.trim,
        )
    }

    test("generateMergeTreeTableCTASIngestionJob ctasStatement should not use LowCardinality for String type and when metadata is provided and count is more than or equals threshold") {
        val df = spark.createDataFrame(
            spark.sparkContext.parallelize(Seq[Row]()),
            StructType(Seq(
                StructField("nonnull", StringType, nullable = false),
                StructField("nullable", StringType, nullable = true),
                StructField("nonnull_array_nonnull_element", ArrayType(StringType, containsNull = false), nullable = false),
                StructField("nonnull_array_nullable_element", ArrayType(StringType, containsNull = true), nullable = false),
                StructField("nullable_array_nonnull_element", ArrayType(StringType, containsNull = false), nullable = true),
                StructField("nullable_array_nullable_element", ArrayType(StringType, containsNull = true), nullable = true),
            ))
        )
        generateMergeTreeTableCTASIngestionJob(df, TABLE_NAME, new URL(PARQUET_S3_URL), metadataFields = Seq(
            GenericReportMetadataField("nonnull", null, null, 10001, containsNullValue = true),
            GenericReportMetadataField("nullable", null, null, 10000, containsNullValue = true),
            GenericReportMetadataField("nonnull_array_nonnull_element", null, null, 10000, containsNullValue = true),
            GenericReportMetadataField("nonnull_array_nullable_element", null, null, 10000, containsNullValue = true),
            GenericReportMetadataField("nullable_array_nonnull_element", null, null, 10000, containsNullValue = true),
            GenericReportMetadataField("nullable_array_nullable_element", null, null, 10000, containsNullValue = true),
        )).ctasStatement should be (
            s"""
               |CREATE TABLE $TABLE_NAME
               |(
               |    nonnull String,
               |    nullable Nullable(String),
               |    nonnull_array_nonnull_element Array(String),
               |    nonnull_array_nullable_element Array(Nullable(String)),
               |    nullable_array_nonnull_element Array(String),
               |    nullable_array_nullable_element Array(Nullable(String))
               |) ENGINE = MergeTree
               |ORDER BY tuple()
               |AS
               |SELECT nonnull,
               |       nullable,
               |       nonnull_array_nonnull_element,
               |       nonnull_array_nullable_element,
               |       coalesce(nullable_array_nonnull_element, []),
               |       coalesce(nullable_array_nullable_element, [])
               |FROM s3(
               |        'https://a.b.c/*.parquet',
               |        'Parquet'
               |     )
               |""".stripMargin.trim,
        )
    }

    for ((sparkType, expectedType) <- EXPECTED_TYPE_MAPPING.filter(_._1 != StringType)) {
        test(s"generateMergeTreeTableCTASIngestionJob ctasStatement should not use LowCardinality for $sparkType even metadata provided and count less than threshold") {
            val df = spark.createDataFrame(
                spark.sparkContext.parallelize(Seq[Row]()),
                StructType(Seq(
                    StructField("nonnull", sparkType, nullable = false),
                    StructField("nullable", sparkType, nullable = true),
                    StructField("nonnull_array_nonnull_element", ArrayType(sparkType, containsNull = false), nullable = false),
                    StructField("nonnull_array_nullable_element", ArrayType(sparkType, containsNull = true), nullable = false),
                    StructField("nullable_array_nonnull_element", ArrayType(sparkType, containsNull = false), nullable = true),
                    StructField("nullable_array_nullable_element", ArrayType(sparkType, containsNull = true), nullable = true),
                ))
            )
            generateMergeTreeTableCTASIngestionJob(df, TABLE_NAME, new URL(PARQUET_S3_URL), metadataFields = Seq(
                GenericReportMetadataField("nonnull", null, null, 3, containsNullValue = true),
                GenericReportMetadataField("nullable", null, null, 3, containsNullValue = true),
                GenericReportMetadataField("nonnull_array_nonnull_element", null, null, 3, containsNullValue = true),
                GenericReportMetadataField("nonnull_array_nullable_element", null, null, 3, containsNullValue = true),
                GenericReportMetadataField("nullable_array_nonnull_element", null, null, 3, containsNullValue = true),
                GenericReportMetadataField("nullable_array_nullable_element", null, null, 3, containsNullValue = true),
            )).ctasStatement should be (
                s"""
                   |CREATE TABLE $TABLE_NAME
                   |(
                   |    nonnull $expectedType,
                   |    nullable Nullable($expectedType),
                   |    nonnull_array_nonnull_element Array($expectedType),
                   |    nonnull_array_nullable_element Array(Nullable($expectedType)),
                   |    nullable_array_nonnull_element Array($expectedType),
                   |    nullable_array_nullable_element Array(Nullable($expectedType))
                   |) ENGINE = MergeTree
                   |ORDER BY tuple()
                   |AS
                   |SELECT nonnull,
                   |       nullable,
                   |       nonnull_array_nonnull_element,
                   |       nonnull_array_nullable_element,
                   |       coalesce(nullable_array_nonnull_element, []),
                   |       coalesce(nullable_array_nullable_element, [])
                   |FROM s3(
                   |        'https://a.b.c/*.parquet',
                   |        'Parquet'
                   |     )
                   |""".stripMargin.trim,
            )
        }
    }

    test("generateMergeTreeTableCTASIngestionJob ctasStatement should use overridingColumnDefinitions") {
        val df = Seq((1, 1)).toDF("Int", "Int_overridden")
        generateMergeTreeTableCTASIngestionJob(
            df,
            TABLE_NAME,
            new URL(PARQUET_S3_URL),
            overridingColumnDefinitions = Seq(ClickhouseColumnDefinition("Int_overridden", "UInt32", Some("toUInt32(Int_overridden)"))),
        ).ctasStatement should be (
            s"""
               |CREATE TABLE $TABLE_NAME
               |(
               |    Int Int32,
               |    Int_overridden UInt32
               |) ENGINE = MergeTree
               |ORDER BY tuple()
               |AS
               |SELECT Int,
               |       toUInt32(Int_overridden)
               |FROM s3(
               |        'https://a.b.c/*.parquet',
               |        'Parquet'
               |     )
               |""".stripMargin.trim,
        )
    }

    test("generateMergeTreeTableCTASIngestionJob ctasStatement should use ORDER BY, PRIMARY KEY and PARTITION BY when provided") {
        val df = Seq(1, 2, 3).toDF("Int")
        generateMergeTreeTableCTASIngestionJob(
            df,
            "table_name",
            new URL("https://a.b.c"),
            orderByExpressions = Seq("Int", "Int2"),
            primaryKeyExpressions = Seq("Int3"),
            partitionByExpressions = Seq("Int4")
        ).ctasStatement should be (
            """
              |CREATE TABLE table_name
              |(
              |    Int Int32
              |) ENGINE = MergeTree
              |ORDER BY (Int, Int2)
              |PRIMARY KEY Int3
              |PARTITION BY Int4
              |AS
              |SELECT Int
              |FROM s3(
              |        'https://a.b.c/*.parquet',
              |        'Parquet'
              |     )
              |""".stripMargin.trim,
        )
    }

    test("generateMergeTreeTableCTASIngestionJob ctasStatement should use PRIMARY KEY for ORDER BY when orderByExpressions is not provided but primaryKeyExpressions is provided") {
        val df = Seq(1, 2, 3).toDF("Int")
        generateMergeTreeTableCTASIngestionJob(
            df,
            TABLE_NAME,
            new URL(PARQUET_S3_URL),
            primaryKeyExpressions = Seq("Int"),
        ).ctasStatement should be (
            s"""
               |CREATE TABLE $TABLE_NAME
               |(
               |    Int Int32
               |) ENGINE = MergeTree
               |ORDER BY Int
               |PRIMARY KEY Int
               |AS
               |SELECT Int
               |FROM s3(
               |        'https://a.b.c/*.parquet',
               |        'Parquet'
               |     )
               |""".stripMargin.trim,
        )
    }
}
