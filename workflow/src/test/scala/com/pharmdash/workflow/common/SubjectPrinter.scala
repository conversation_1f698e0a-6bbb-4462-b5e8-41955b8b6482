package com.pharmdash.workflow.common

import com.pharmdash.workflow.model.Subject

class SubjectPrinter {

    def print(subject: Subject): String = {

        val sb = new StringBuilder()

        sb.append (s"Subject ${subject.id}\n")
        sb.append (s"${subject.info}\n")

        subject.events.foreach(event => {
            sb.append(s"$event\n")
        })

        return sb.toString()
    }

}
