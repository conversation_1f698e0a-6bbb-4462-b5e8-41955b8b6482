package com.pharmdash.workflow.common

import com.pharmdash.workflow.common.RetainingOrderGroupByImplicit.RetainingOrderGroupByImpl
import org.junit.runner.RunWith
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.junit.JUnitRunner


@RunWith(classOf[JUnitRunner])
class RetainingOrderGroupByImplicitTest extends AnyFunSpec with Matchers {
    it("retainingOrderGroupBy should retain order of key base on first appearance of insertion, retaining order of values") {
        val seq = Seq((2, 3), (5, 6), (2, 2), (7, 3), (5, 1), (1, 4))

        val result = seq.retainingOrderGroupBy(_._1)

        result should contain theSameElementsInOrderAs Seq(
            2 -> Seq((2, 3), (2, 2)),
            5 -> Seq((5, 6), (5, 1)),
            7 -> Seq((7, 3)),
            1 -> Seq((1, 4))
        )
    }
}