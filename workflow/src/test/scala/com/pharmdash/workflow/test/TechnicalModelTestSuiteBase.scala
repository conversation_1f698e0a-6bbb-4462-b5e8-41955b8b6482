package com.pharmdash.workflow.test

import com.pharmdash.workflow.model.SubjectDistinctEventCodesMetadata.withSubjectDistinctEventCodesMetadata
import com.pharmdash.workflow.model.{Subject, TagMetadata}
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions.{col, explode, udf}
import org.apache.spark.sql.types.Metadata
import org.apache.spark.sql.{DataFrame, Dataset}
import org.scalatest.Suite

import java.sql.Date

trait TechnicalModelTestSuiteBase extends SparkTestSuiteBase {
    self: Suite =>

    import spark.implicits._

    @deprecated("Why do you need this? Use the implicit toDS() function from Spark", "2024-08-20")
    def subjects(subjects: Subject*): Dataset[Subject] = {
        spark.createDataset(subjects)
    }

    /**
     * The output is a little shit when there is an error.
     * Hopefully this helps.
     *
     * limited to 15 subjects (don't be lazy - create your test data please.)
     */
    def assertEqualData(expected: DataFrame, actual: DataFrame): Unit = {
        try {
            assertDataFrameEquals(expected.toDF(), actual.toDF())
        } catch {
            case unknownError: Throwable =>

                println()

                println("Expected Results")
                expected.show(truncate = false, numRows = 20)

                println("Actual Results")
                actual.show(truncate = false, numRows = 20)

                throw unknownError
        }
    }

    def assertSubjectsEqual(expected: Dataset[Subject], actual: Dataset[Subject]): Unit = {
        assertSubjectsEqualIgnoringEvents(expected, actual)
        assertEventsEqual(expected, actual)
    }

    def assertEventsEqual(expected: Dataset[Subject], actual: Dataset[Subject]): Unit = {
        assertEqualData(
            eventsOnly(expected),
            eventsOnly(actual)
        )
    }

    def assertSubjectsEqualIgnoringEvents(expected: Dataset[Subject], actual: Dataset[Subject]): Unit = {
        assertEqualData(
            // FIXME: why compare JSON? Map elements won't be in order
            expected.drop("events").sort(Subject.IdColumn).toJSON.toDF(),
            actual.drop("events").sort(Subject.IdColumn).toJSON.toDF()
        )
    }

    def eventsOnly(subjects: Dataset[Subject]): DataFrame = {
        import spark.implicits._

        subjects.select(
                'id.as("subjectId"),
                explode('events).as("event")
            )
            // subjcet order should not matter
            .sort("subjectId")
            // I don't care if the id is different.
            // But id is important to some algorithms?
            .withColumn("event", $"event".dropFields("id"))
            .toDF()
    }

    def insertFakeMetadata(dataset: Dataset[Subject]): Dataset[Subject] = {
        val subjectMetadata: Metadata = TagMetadata.toColumnMetadata(Map("gender" -> TagMetadata(true)))
        val eventsMetadata: Metadata = TagMetadata.toColumnMetadata(Map("age" -> TagMetadata(true)))

        dataset
            .withColumn(Subject.InfoColumn, col(Subject.InfoColumn).as("", subjectMetadata))
            .withColumn(Subject.EventsColumn, col(Subject.EventsColumn).as("", eventsMetadata))
            .as[Subject]
    }

    def createSubjectsWithFakeMetadata(subjects: Subject*): Dataset[Subject] = {
        insertFakeMetadata(spark.createDataset(subjects))
    }

    def createSubjectsWithFakeMetadataAndEnableSubjectDistinctEventCodes(subjects: Subject*): Dataset[Subject] = {
        withSubjectDistinctEventCodesMetadata(createSubjectsWithFakeMetadata(subjects: _*).toDF()).as[Subject]
    }

    def givenSubjects(subjects: Subject*): Dataset[Subject] = createSubjectsWithFakeMetadata(subjects: _*)

    @deprecated("These seems to no longer work in Spark 3.5", since = "2024-08-23")
    def orderedTags: UserDefinedFunction = udf((tags: Map[String, Seq[String]]) => {
        tags.keys.toSeq
            .sorted
            .map(key => Map(key -> tags.get(key)))
    })

    @deprecated("These seems to no longer work in Spark 3.5", since = "2024-08-23")
    def orderedObjectTags: UserDefinedFunction = udf((tags: Map[String, Seq[Map[String, String]]]) => {
        tags.keys.toSeq
            .sorted
            .map(key => Map(key -> tags.get(key)))
    })

    def date(dateStr: String): Date = {
        Date.valueOf(dateStr)
    }

}
