package com.pharmdash.workflow.test

import com.pharmdash.workflow.common.BetterJson.formats
import com.pharmdash.workflow.dp.ICSDProcessor.{ICSDStruct, IcsdTag}
import org.json4s.jackson.Serialization.write

import java.lang

trait ICSDTestUtils {

    def icsdTag(icsdValues: String*): (String, Seq[String]) = {
        IcsdTag -> icsdValues
    }

    def icsd(dropOffDefination: Int,
             event: String,
             switchOutTo: Seq[String] = Seq.empty,
             switchInFrom: Seq[String] = Seq.empty,
             dropOff: lang.Boolean = null,
             continuation: lang.Boolean = null,
             naiveInitiation: lang.Boolean = null,
             initiation: lang.Boolean = null,
             reInitiation: lang.Boolean = null,
             death: lang.Boolean = null,
             censored: lang.Boolean = null): String = {
        write(
            ICSDStruct(
                dropOffDefination,
                event,
                switchOutTo,
                switchInFrom,
                dropOff,
                continuation,
                naiveInitiation,
                initiation,
                reInitiation,
                death,
                censored
            )
        )
    }

    def naiveInit(dropOffDefination: Int, event: String): String = {
        icsd(dropOffDefination, event, naiveInitiation = true)
    }

    def init(dropOffDefination: Int, event: String): String = {
        icsd(dropOffDefination, event, initiation = true)
    }

    def continue(dropOffDefination: Int, event: String): String = {
        icsd(dropOffDefination, event, continuation = true)
    }

    def switchInFrom(dropOffDefination: Int, event: String, from: Seq[String]): String = {
        icsd(dropOffDefination, event, switchInFrom = from)
    }

    def reinit(dropOffDefination: Int, event: String): String = {
        icsd(dropOffDefination, event, reInitiation = true)
    }

    def dropOff(dropOffDefination: Int, event: String): String = {
        icsd(dropOffDefination, event, dropOff = true)
    }

    def death(dropOffDefination: Int, event: String): String = {
        icsd(dropOffDefination, event, death = true)
    }

    def censored(dropOffDefination: Int, event: String): String = {
        icsd(dropOffDefination, event, censored = true)
    }

    def switchOutTo(dropOffDefination: Int, event: String, to: Seq[String]): String = {
        icsd(dropOffDefination, event, switchOutTo = to)
    }
}
