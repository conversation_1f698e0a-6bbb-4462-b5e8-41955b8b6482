package com.pharmdash.workflow.dp.etl.us.forian.pharmacy

import com.pharmdash.workflow.dp.etl.us.forian.ForianClaimReaderTestBase
import com.pharmdash.workflow.dp.etl.us.forian.ForianClaimReaderTestBase.{emptyTag, tag}
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL.ForianDataset.{Closed, Open}
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL._
import com.pharmdash.workflow.dp.etl.us.forian.function.ProviderFunctions.ProviderStructColumns
import com.pharmdash.workflow.dp.etl.us.forian.pharmacy.ForianPharmacyReader.readPharmacy
import com.pharmdash.workflow.model.Event
import com.pharmdash.workflow.model.StandardGender.{Female, Male, Unknown}
import com.prospection.arch2.model.Category
import org.junit.runner.RunWith
import org.scalatest.matchers.should
import org.scalatestplus.junit.JUnitRunner

import java.sql.Date

@RunWith(classOf[JUnitRunner])
class ForianPharmacyReaderTest extends ForianClaimReaderTestBase with should.Matchers {

    import spark.implicits._

    private val defaultEtlParams = ForianETLParams(
        startOfDataset = "2010-01-01",
        endOfDataset = "2022-06-30",
        bucket = "",
        rawDatasources = Map(ForianDataSource.Legacy -> ""),
    )

    private val unknownPrescriberTags = Map(
        Event.StateTag -> Seq(Unknown),
        Event.PrescriberSpecialtyTag -> Seq(Unknown),
        Event.PrescriberDetailSpecialtyTag -> Seq(Unknown)
    )

    it("should be able to load data from pharmacy") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val pharmacy = Seq(
                    ("10043789", "100000", "00000000001", date("2015-08-04"), "30", "123456", "", "", "1.333", "D001", "Payer1", BigDecimal(20.20).setScale(2), BigDecimal(50.00).setScale(2), BigDecimal(70.20).setScale(2)),
                    ("10043789", "300000", "00000000002", date("2017-01-01"), "10", "123456", "", "", "0000563.000", "D002", "Payer2", BigDecimal(10.00).setScale(2), BigDecimal(20.00).setScale(2), BigDecimal(30.00).setScale(2)),
                    ("10043789", "700000", "00000000003", date("2018-01-01"), "90", "987654", "", "", "5.556789", "", "", BigDecimal(0.00).setScale(2), BigDecimal(0.00).setScale(2), BigDecimal(0.00).setScale(2))
                )
                    .toDF(PharmacyColumn: _*)
                    .withUniqueResponseCodeColumns()

            saveTestData(path, Subdirectories.OpenPharmacyLegacy, pharmacy)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val (subjects, events) = readPharmacy(prescribers, Open, etlParams)(spark)

            subjects.collect() should be(Seq(
                forianSubjectWithDefaults("10043789", network = NetworkStruct(hasOpen = true))
            ))

            events.collect() should be(Seq(
                ForianEvent("10043789", "00000000001", date("2015-08-04"), Some(1.333), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "30"),
                        tag(Event.PrescriberIdTag -> "123456"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "100000"),
                        tag(LinkedDiagnosisTag -> "D001"),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        tag(Event.PayerIdTag -> "Payer1"),
                        tag(Event.CopayTag -> "20.2"),
                        tag(Event.PatientPayTag -> "50.0"),
                        tag(Event.CostTag -> "70.2")
                    ),
                    unknownPrescriberObjectTags("123456"),
                    Some(70.20)
                ),
                ForianEvent("10043789", "00000000002", date("2017-01-01"), Some(563), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "10"),
                        tag(Event.PrescriberIdTag -> "123456"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "300000"),
                        tag(LinkedDiagnosisTag -> "D002"),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        tag(Event.PayerIdTag -> "Payer2"),
                        tag(Event.CopayTag -> "10.0"),
                        tag(Event.PatientPayTag -> "20.0"),
                        tag(Event.CostTag -> "30.0")
                    ),
                    unknownPrescriberObjectTags("123456"),
                    Some(30.00)
                ),
                ForianEvent("10043789", "00000000003", date("2018-01-01"), Some(5.556789), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "90"),
                        tag(Event.PrescriberIdTag -> "987654"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "700000"),
                        emptyTag(LinkedDiagnosisTag),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("987654"),
                    Some(0.0)
                )
            ))
        })
    }

    it("should be able to load pharmacy data from all data sources and populate rawDatasource tag") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(
                bucket = path.toString,
                rawDatasources = Map(
                    ForianDataSource.Legacy -> "legacy",
                    ForianDataSource.Rx1 -> "rx1",
                    ForianDataSource.Rx2 -> "rx2",
                    ForianDataSource.BiMonthly -> "BiMonthly"
                )
            )

            val pharmacyLegacy = Seq(
                ("10043789", "100000", "00000000001", date("2015-08-04"), "30", "123456", "", "", "1.333", "D001", "", 0.0, 0.0, 0.0),
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.Legacy)}/${Subdirectories.OpenPharmacyLegacy}", pharmacyLegacy)
            val pharmacyRx1 = Seq(
                ("10043789", "300000", "00000000002", date("2017-01-01"), "10", "123456", "", "", "0000563.000", "D002", "", 0.0, 0.0, 0.0),
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.Rx1)}/${Subdirectories.Rx1Pharmacy}", pharmacyRx1)

            val pharmacyRx2 = Seq(
                ("invalidPatientId", "700000", "00000000003", date("2018-01-01"), "90", "987654", "", "", "5.556789", "", "", 0.0, 0.0, 0.0)
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.Rx2)}/${Subdirectories.Rx2Pharmacy}", pharmacyRx2)
            val pharmacyRx2Bridge = Seq(
                ("700000", "10043789", "001", "002", date("2018-01-01"), date("2018-01-01"), date("2018-01-01"))
            )
                .toDF(Seq(CommonColumns.ClaimNumber, CommonColumns.PatientId, "prospection_transit_token_1", "prospection_transit_token_2", "extract_date", "create_ts", "update_ts"): _*)
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.Rx2)}/${Subdirectories.Rx2BridgeTable}", pharmacyRx2Bridge)

            val pharmacyBiMonthly = Seq(
                ("10043789", "800000", "00000000004", date("2018-01-01"), "12", "45678", "", "", "4.22", "", "", 0.0, 0.0, 0.0)
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.BiMonthly)}/${Subdirectories.BiMonthlyPharmacy}", pharmacyBiMonthly)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val (subjects, events) = readPharmacy(prescribers, Open, etlParams)(spark)

            subjects.collect() should be(Seq(
                forianSubjectWithDefaults("10043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy, ForianDataSource.Rx2, ForianDataSource.Rx1, ForianDataSource.BiMonthly))
            ))

            events.collect() should be(Seq(
                ForianEvent("10043789", "00000000001", date("2015-08-04"), Some(1.333), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "30"),
                        tag(Event.PrescriberIdTag -> "123456"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "100000"),
                        tag(LinkedDiagnosisTag -> "D001"),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("123456"),
                    Some(0.0)
                ),
                ForianEvent("10043789", "00000000002", date("2017-01-01"), Some(563), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "10"),
                        tag(Event.PrescriberIdTag -> "123456"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "300000"),
                        tag(LinkedDiagnosisTag -> "D002"),
                        tag(RawDataSourceTag -> ForianDataSource.Rx1),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("123456"),
                    Some(0.0)
                ),
                ForianEvent("10043789", "00000000003", date("2018-01-01"), Some(5.556789), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "90"),
                        tag(Event.PrescriberIdTag -> "987654"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "700000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Rx2),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("987654"),
                    Some(0.0)
                ),
                ForianEvent("10043789", "00000000004", date("2018-01-01"), Some(4.22), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "12"),
                        tag(Event.PrescriberIdTag -> "45678"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "800000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.BiMonthly),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("45678"),
                    Some(0.0)
                )
            ))
        })
    }

    it("should filter out null patient id from bridge table") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(
                bucket = path.toString,
                rawDatasources = Map(
                    ForianDataSource.Rx2 -> "rx2"
                )
            )

            val pharmacyRx2 = Seq(
                ("invalidPatientId", "700000", "00000000003", date("2018-01-01"), "90", "987654", "", "", "5.556789", "", "", 0.0, 0.0, 0.0),
                ("invalidPatientId", "800000", "00000000004", date("2018-01-01"), "12", "45678", "", "", "4.22", "", "", 0.0, 0.0, 0.0)
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.Rx2)}/${Subdirectories.Rx2Pharmacy}", pharmacyRx2)
            val pharmacyRx2Bridge = Seq(
                ("700000", "10043789", "001", "002", date("2018-01-01"), date("2018-01-01"), date("2018-01-01")),
                ("800000", null, "001", "002", date("2018-01-01"), date("2018-01-01"), date("2018-01-01"))
            )
                .toDF(Seq(CommonColumns.ClaimNumber, CommonColumns.PatientId, "prospection_transit_token_1", "prospection_transit_token_2", "extract_date", "create_ts", "update_ts"): _*)
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.Rx2)}/${Subdirectories.Rx2BridgeTable}", pharmacyRx2Bridge)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val (_, events) = readPharmacy(prescribers, Open, etlParams)(spark)

            events.collect() should be(Seq(
                ForianEvent("10043789", "00000000003", date("2018-01-01"), Some(5.556789), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "90"),
                        tag(Event.PrescriberIdTag -> "987654"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "700000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Rx2),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("987654"),
                    Some(0.0)
                )
            ))
        })
    }

    it("should return empty dataset if claim patient is empty") {
        withTemp { path =>
            val etlParams = defaultEtlParams.copy(bucket = path.toString, rawDatasources = Map(ForianDataSource.BiMonthly -> ""))

            val prescribers = spark.emptyDataset[ForianPrescriber]
            val (subjects, events) = readPharmacy(prescribers, ForianDataset.Closed, etlParams)(spark)

            subjects.count() should be(0)
            events.count() should be(0)
        }
    }

    it("should merge duplicate events from different sources") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(
                bucket = path.toString,
                rawDatasources = Map(
                    ForianDataSource.Legacy -> "legacy",
                    ForianDataSource.Rx1 -> "rx1",
                    ForianDataSource.Rx2 -> "rx2",
                    ForianDataSource.BiMonthly -> "BiMonthly"
                )
            )

            val pharmacyLegacy = Seq(
                ("10043789", "100000", "00000000001", date("2015-08-04"), "30", "123456", "", "", "333", 1, "", "", "P", "B1", "Payer A", "1.333", "", "D001", "", 0.0, 0.0, 0.0),
            )
                .toDF(ExtendedPharmacyColumns: _*)
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.Legacy)}/${Subdirectories.OpenPharmacyLegacy}", pharmacyLegacy)
            val pharmacyRx1 = Seq(
                ("10043789", "100000", "00000000001", date("2015-08-04"), "30", "123456", "", "", "333", 1, "", "", "P", "B1", "Payer A", "1.333", "", "D001", "", 0.0, 0.0, 0.0),
            )
                .toDF(ExtendedPharmacyColumns: _*)
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.Rx1)}/${Subdirectories.Rx1Pharmacy}", pharmacyRx1)

            val pharmacyRx2 = Seq(
                ("22243789", "100000", "00000000001", date("2015-08-04"), "30", "123456", "", "", "333", 1, "", "", "P", "B1", "Payer A", "1.333", "", "D001", "", 0.0, 0.0, 0.0),
            )
                .toDF(ExtendedPharmacyColumns: _*)
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.Rx2)}/${Subdirectories.Rx2Pharmacy}", pharmacyRx2)
            val pharmacyRx2Bridge = Seq(
                ("100000", "10043789", "001", "002", date("2018-01-01"), date("2018-01-01"), date("2018-01-01"))
            )
                .toDF(Seq(CommonColumns.ClaimNumber, CommonColumns.PatientId, "prospection_transit_token_1", "prospection_transit_token_2", "extract_date", "create_ts", "update_ts"): _*)
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.Rx2)}/${Subdirectories.Rx2BridgeTable}", pharmacyRx2Bridge)

            val pharmacyBiMonthly = Seq(
                ("10043789", "100000", "00000000001", date("2015-08-04"), "30", "123456", "", "", "333", 1, "", "", "P", "B1", "Payer A", "1.333", "", "D001", "", 0.0, 0.0, 0.0),
            )
                .toDF(ExtendedPharmacyColumns: _*)
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.BiMonthly)}/${Subdirectories.BiMonthlyPharmacy}", pharmacyBiMonthly)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val (subjects, events) = readPharmacy(prescribers, Open, etlParams)(spark)

            subjects.collect() should be(Seq(
                forianSubjectWithDefaults("10043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy, ForianDataSource.Rx2, ForianDataSource.Rx1, ForianDataSource.BiMonthly))
            ))

            events.collect() should be(Seq(
                ForianEvent("10043789", "00000000001", date("2015-08-04"), Some(1.333), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "30"),
                        tag(Event.PrescriberIdTag -> "123456"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "100000"),
                        tag(LinkedDiagnosisTag -> "D001"),
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy, ForianDataSource.Rx2, ForianDataSource.Rx1, ForianDataSource.BiMonthly),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("123456"),
                    Some(0.0)
                )
            ))
        })
    }

    it("should load pharmacy data from only provided data sources") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(
                bucket = path.toString,
                rawDatasources = Map(
                    ForianDataSource.Legacy -> "legacy",
                    ForianDataSource.Rx1 -> "rx1"
                )
            )

            val pharmacyLegacy = Seq(
                ("10043789", "100000", "00000000001", date("2015-08-04"), "30", "123456", "", "", "1.333", "D001", "", 0.0, 0.0, 0.0),
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.Legacy)}/${Subdirectories.OpenPharmacyLegacy}", pharmacyLegacy)
            val pharmacyRx1 = Seq(
                ("10043789", "300000", "00000000002", date("2017-01-01"), "10", "123456", "", "", "0000563.000", "D002", "", 0.0, 0.0, 0.0),
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.Rx1)}/${Subdirectories.Rx1Pharmacy}", pharmacyRx1)

            val pharmacyRx2 = Seq(
                ("invalidPatientId", "700000", "00000000003", date("2018-01-01"), "90", "987654", "", "", "5.556789", "", "", 0.0, 0.0, 0.0)
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()
            saveTestData(path, s"rx2/${Subdirectories.Rx2Pharmacy}", pharmacyRx2)
            val pharmacyRx2Bridge = Seq(
                ("700000", "10043789", "001", "002", date("2018-01-01"), date("2018-01-01"), date("2018-01-01"))
            )
                .toDF(Seq(CommonColumns.ClaimNumber, CommonColumns.PatientId, "prospection_transit_token_1", "prospection_transit_token_2", "extract_date", "create_ts", "update_ts"): _*)
            saveTestData(path, s"rx2/${Subdirectories.Rx2BridgeTable}", pharmacyRx2Bridge)

            val pharmacyBiMonthly = Seq(
                ("10043789", "800000", "00000000004", date("2018-01-01"), "12", "45678", "", "", "4.22", "", "", 0.0, 0.0, 0.0)
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()
            saveTestData(path, s"BiMonthly/${Subdirectories.BiMonthlyPharmacy}", pharmacyBiMonthly)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val (subjects, events) = readPharmacy(prescribers, Open, etlParams)(spark)

            subjects.collect() should be(Seq(
                forianSubjectWithDefaults("10043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy, ForianDataSource.Rx1))
            ))

            events.collect() should be(Seq(
                ForianEvent("10043789", "00000000001", date("2015-08-04"), Some(1.333), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "30"),
                        tag(Event.PrescriberIdTag -> "123456"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "100000"),
                        tag(LinkedDiagnosisTag -> "D001"),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("123456"),
                    Some(0.0)
                ),
                ForianEvent("10043789", "00000000002", date("2017-01-01"), Some(563), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "10"),
                        tag(Event.PrescriberIdTag -> "123456"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "300000"),
                        tag(LinkedDiagnosisTag -> "D002"),
                        tag(RawDataSourceTag -> ForianDataSource.Rx1),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("123456"),
                    Some(0.0)
                )
            ))
        })
    }

    it("should read date of birth as date type or string type") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(
                bucket = path.toString,
                rawDatasources = Map(
                    ForianDataSource.Legacy -> "legacy",
                    ForianDataSource.BiMonthly -> "BiMonthly"
                )
            )

            val pharmacyLegacy = Seq(
                ("10043789", "100000", "00000000001", date("2015-08-04"), "30", "123456", date("1970-11-05"), "", "1.333", "D001", "", 0.0, 0.0, 0.0)  // date of birth is date type
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.Legacy)}/${Subdirectories.OpenPharmacyLegacy}", pharmacyLegacy)


            val pharmacyBiMonthly = Seq(
                ("10043791", "800000", "00000000004", date("2018-01-01"), "12", "45678", "1980-02-01", "", "4.22", "", "", 0.0, 0.0, 0.0)  // date of birth is string type
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.BiMonthly)}/${Subdirectories.BiMonthlyPharmacy}", pharmacyBiMonthly)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val (subjects, _) = readPharmacy(prescribers, Open, etlParams)(spark)

            subjects.collect() should be(Seq(
                forianSubjectWithDefaults("10043789", dateOfBirth = Map(date("1970-11-05") -> 1), network = NetworkStruct(hasOpen = true)),
                forianSubjectWithDefaults("10043791", dateOfBirth = Map(date("1980-02-01") -> 1), network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.BiMonthly)),
            ))

        })
    }

    it("should be able to handle leading and trailing whitespaces in daysSupply") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val pharmacy = Seq(
                ("10043789", "100000", "00000000001", date("2015-08-04"), " 30 ", "123456", "", "", "0", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "00000000001", date("2016-02-28"), " 30", "123456", "", "", "0", "", "", 0.0, 0.0, 0.0),
                ("10043789", "300000", "00000000002", date("2017-01-01"), "10 ", "123456", "", "", "0", "", "", 0.0, 0.0, 0.0)
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()

            saveTestData(path, Subdirectories.OpenPharmacyLegacy, pharmacy)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val (subjects, events) = readPharmacy(prescribers, Open, etlParams)(spark)

            subjects.collect() should be(Seq(
                forianSubjectWithDefaults("10043789", network = NetworkStruct(hasOpen = true))
            ))

            events.collect() should be(Seq(
                ForianEvent("10043789", "00000000001", date("2015-08-04"), Some(0), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "30"),
                        tag(Event.PrescriberIdTag -> "123456"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "100000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("123456"),
                    Some(0.0)
                ),
                ForianEvent("10043789", "00000000001", date("2016-02-28"), Some(0), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "30"),
                        tag(Event.PrescriberIdTag -> "123456"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "200000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("123456"),
                    Some(0.0)
                ),
                ForianEvent("10043789", "00000000002", date("2017-01-01"), Some(0), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "10"),
                        tag(Event.PrescriberIdTag -> "123456"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "300000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("123456"),
                    Some(0.0)
                )
            ))
        })
    }

    it("should not include any events before startOfData or after endOfData") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString, startOfDataset = "2016-01-01", endOfDataset = "2021-01-01")

            val pharmacy = Seq(
                ("10043789", "100000", "00000000001", date("2015-08-04"), "30", "1", "", "", "0", "", "", 0.0, 0.0, 0.0),
                ("10043789", "300000", "00000000002", date("2020-01-01"), "30", "1", "", "", "0", "", "", 0.0, 0.0, 0.0),
                ("10043789", "700000", "00000000003", date("2022-01-01"), "30", "1", "", "", "0", "", "", 0.0, 0.0, 0.0)
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()

            saveTestData(path, Subdirectories.OpenPharmacyLegacy, pharmacy)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val (subjects, events) = readPharmacy(prescribers, Open, etlParams)(spark)

            subjects.collect() should be(Seq(
                forianSubjectWithDefaults("10043789", network = NetworkStruct(hasOpen = true))
            ))

            events.collect() should be(Seq(
                ForianEvent("10043789", "00000000002", date("2020-01-01"), Some(0), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "30"),
                        tag(Event.PrescriberIdTag -> "1"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "300000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("1"),
                    Some(0.0)
                )
            ))
        })
    }

    it("should remove records with empty/invalid NDC") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val pharmacy = Seq(
                ("10043789", "100000", "1", date("2017-07-01"), "90", "987654", "", "", "0", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "", date("2018-01-01"), "90", "987654", "", "", "0", "", "", 0.0, 0.0, 0.0),
                ("10043789", "300000", "abc", date("2018-01-01"), "90", "987654", "", "", "0", "", "", 0.0, 0.0, 0.0),
                ("10043789", "300000", "___", date("2018-01-01"), "90", "987654", "", "", "0", "", "", 0.0, 0.0, 0.0)
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()

            saveTestData(path, Subdirectories.OpenPharmacyLegacy, pharmacy)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val (subjects, events) = readPharmacy(prescribers, Open, etlParams)(spark)

            subjects.collect() should be(Seq(
                forianSubjectWithDefaults("10043789", network = NetworkStruct(hasOpen = true))
            ))

            events.collect() should be(Seq(
                ForianEvent("10043789", "00000000001", date("2017-07-01"), Some(0), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "90"),
                        tag(Event.PrescriberIdTag -> "987654"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "100000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("987654"),
                    Some(0.0)
                )
            ))

        })
    }

    it("should trim non digit then retain/add enough leading zeros in NDC") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val pharmacy = Seq(
                ("10043789", "100000", " 00000000001 ", date("2017-07-01"), "90", "987654", "", "", "0", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "  abc 10000  ", date("2018-01-01"), "90", "987654", "", "", "0", "", "", 0.0, 0.0, 0.0)
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()

            saveTestData(path, Subdirectories.OpenPharmacyLegacy, pharmacy)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val (subjects, events) = readPharmacy(prescribers, Open, etlParams)(spark)

            subjects.collect() should be(Seq(
                forianSubjectWithDefaults("10043789", network = NetworkStruct(hasOpen = true))
            ))

            events.collect() should be(Seq(
                ForianEvent("10043789", "00000000001", date("2017-07-01"), Some(0), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "90"),
                        tag(Event.PrescriberIdTag -> "987654"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "100000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("987654"),
                    Some(0.0)
                ),
                ForianEvent("10043789", "00000010000", date("2018-01-01"), Some(0), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "90"),
                        tag(Event.PrescriberIdTag -> "987654"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "200000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("987654"),
                    Some(0.0)
                )
            ))
        })
    }

    it("should remove records with empty DATE_OF_SERVICE") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val pharmacy = Seq(
                ("10043789", "100000", "00000000001", date("2017-07-01"), "90", "987654", "", "", "0", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "00000000001", null, "90", "987654", "", "", "0", "", "", 0.0, 0.0, 0.0)
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()

            saveTestData(path, Subdirectories.OpenPharmacyLegacy, pharmacy)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val (subjects, events) = readPharmacy(prescribers, Open, etlParams)(spark)

            subjects.collect() should be(Seq(
                forianSubjectWithDefaults("10043789", network = NetworkStruct(hasOpen = true))
            ))

            events.collect() should be(Seq(
                ForianEvent("10043789", "00000000001", date("2017-07-01"), Some(0), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "90"),
                        tag(Event.PrescriberIdTag -> "987654"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "100000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("987654"),
                    Some(0.0)
                )
            ))
        })
    }

    it("should load prescriber tags correctly") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val pharmacy = Seq(
                ("10043789", "100000", "00000000001", date("2015-08-04"), "10", "1", "", "", "0", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "00000000001", date("2015-08-04"), "10", "2", "", "", "0", "", "", 0.0, 0.0, 0.0),
                ("10043789", "300000", "00000000001", date("2015-08-04"), "10", "3", "", "", "0", "", "", 0.0, 0.0, 0.0),
                // prescriber not found
                ("10043789", "400000", "00000000001", date("2015-08-04"), "10", "4", "", "", "0", "", "", 0.0, 0.0, 0.0)
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()

            saveTestData(path, Subdirectories.OpenPharmacyLegacy, pharmacy)

            val prescribers = Seq(
                defaultPrescriber().copy("1", "WA", "GP\", testing escape - should be escaped and not split", "GP 1"),
                // null state and specialty
                defaultPrescriber().copy("2", "", "", ""),
                // unknown state
                defaultPrescriber().copy("3", "1", "GP", "GP")
            ).toDS()

            val (subjects, events) = readPharmacy(prescribers, Open, etlParams)(spark)

            subjects.collect() should be(Seq(
                forianSubjectWithDefaults("10043789", network = NetworkStruct(hasOpen = true))
            ))

            events.collect() should be(Seq(
                ForianEvent("10043789", "00000000001", date("2015-08-04"), Some(0), Category.Dispensing, Classifications.Dispensing,
                    Map(
                        tag(Event.PrescriberSpecialtyTag -> "GP\", testing escape - should be escaped and not split"),
                        tag(Event.PrescriberDetailSpecialtyTag -> "GP 1"),
                        tag(Event.StateTag -> "Washington"),
                        tag(Event.DaysSupplyTag -> "10"),
                        tag(Event.PrescriberIdTag -> "1"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "100000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    prescriberObjectTags("1", "GP\", testing escape - should be escaped and not split", "GP 1"),
                    Some(0.0)
                ),
                ForianEvent("10043789", "00000000001", date("2015-08-04"), Some(0), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "10"),
                        tag(Event.PrescriberIdTag -> "2"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "200000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("2"),
                    Some(0.0)
                ),
                ForianEvent("10043789", "00000000001", date("2015-08-04"), Some(0), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.PrescriberSpecialtyTag -> "GP"),
                        tag(Event.PrescriberDetailSpecialtyTag -> "GP"),
                        tag(Event.StateTag -> Unknown),
                        tag(Event.DaysSupplyTag -> "10"),
                        tag(Event.PrescriberIdTag -> "3"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "300000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    prescriberObjectTags("3", "GP", "GP"),
                    Some(0.0)
                ),
                ForianEvent("10043789", "00000000001", date("2015-08-04"), Some(0), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "10"),
                        tag(Event.PrescriberIdTag -> "4"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "400000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("4"),
                    Some(0.0)
                )
            ))
        })
    }

    it("should be able to identify claim status") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val pharmacy = Seq(
                // 1st event: rejected (all claim are rejected)
                ("10043789", "100000", "00000000001", date("2017-07-01"), "90", "", "", "", "ref01", 1, "2017-07-01", "000000", "R", "B1", "Payer A", "0", "R", "", "", 0.0, 0.0, 0.0),
                ("10043789", "100000", "00000000001", date("2017-07-01"), "90", "", "", "", "ref01", 1, "2017-07-01", "000000", "R", "B1", "Payer A", "0", "R", "", "", 0.0, 0.0, 0.0),
                // 2nd event: abandoned (P < A & B2)
                ("10043789", "200000", "00000000001", date("2018-01-01"), "90", "", "", "", "ref02", 1, "2018-01-01", "000000", "A", "B2", "Payer A", "0", "A", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "00000000001", date("2018-01-01"), "90", "", "", "", "ref02", 1, "2018-01-01", "000000", "A", "B2", "Payer B", "0", "A", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "00000000001", date("2018-01-01"), "90", "", "", "", "ref02", 1, "2018-01-01", "000000", "A", "A1", "Payer B", "0", "A", "", "", 0.0, 0.0, 0.0),
                // 3rd event: abandoned with approved claims (P = A & B2)
                ("10043789", "200000", "00000000001", date("2018-01-01"), "90", "", "", "", "ref03", 1, "2018-01-01", "000000", "A", "B2", "Payer A", "0", "A", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "00000000001", date("2018-01-01"), "90", "", "", "", "ref03", 1, "2018-01-01", "000000", "P", "B2", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                // 4th event: dispensed
                ("10043789", "300000", "00000000001", date("2019-01-01"), "90", "", "", "", "ref04", 1, "2019-01-01", "000000", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                // 5th event: dispensed with rejected records (P > A & B2)
                ("10043789", "400000", "00000000001", date("2019-02-01"), "90", "", "", "", "ref04", 2, "2019-02-01", "000000", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "400000", "00000000001", date("2019-02-01"), "90", "", "", "", "ref04", 2, "2019-02-01", "000000", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "400000", "00000000001", date("2019-02-01"), "90", "", "", "", "ref04", 2, "2019-02-01", "000000", "A", "A2", "Payer A", "0", "A", "", "", 0.0, 0.0, 0.0),
                // 6th event: dispensed with rejected claims (P > A & B2)
                ("10043789", "400000", "00000000001", date("2019-03-01"), "90", "", "", "", "ref04", 3, "2019-03-01", "000000", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "400000", "00000000001", date("2019-03-01"), "90", "", "", "", "ref04", 3, "2019-03-01", "000000", "A", "A2", "Payer B", "0", "A", "", "", 0.0, 0.0, 0.0)
            ).toDF(ExtendedPharmacyColumns: _*)

            saveTestData(path, Subdirectories.OpenPharmacyLegacy, pharmacy)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val actualEvents = readPharmacy(prescribers, Open, etlParams)(spark)._2.collect()

            actualEvents should have size 6
            actualEvents.map(_.tags.getOrElse(Event.ClaimStatusTag, Seq.empty).head) should equal(Seq(
                ClaimStatus.Rejected,
                ClaimStatus.Abandoned,
                ClaimStatus.Abandoned,
                ClaimStatus.Dispensed,
                ClaimStatus.Dispensed,
                ClaimStatus.Dispensed
            ))
        })
    }

    it("should use record with P final_status (if exist) and latest  (DATE_AUTHORIZED, TIME_AUTHORIZED then DATE_OF_SERVICE) to populate even") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val pharmacy = Seq(
                // Should use latest DATE_AUTHORIZED
                ("10043789", "100000", "00000000001", date("2019-01-01"), "90", "provider_1", "", "", "ref04", 2, "2019-01-01", "000000", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "100000", "00000000001", date("2019-01-01"), "90", "provider_2", "", "", "ref04", 2, "2019-01-02", "000000", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "100000", "00000000001", date("2019-01-01"), "90", "provider_3", "", "", "ref04", 2, "2019-01-03", "000000", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "100000", "00000000001", date("2019-01-01"), "90", "provider_4", "", "", "ref04", 2, "2019-01-04", "000000", "A", "A2", "Payer A", "0", "A", "", "", 0.0, 0.0, 0.0),

                // Should use latest TIME_AUTHORIZED because DATE_AUTHORIZED are same
                ("10043789", "200000", "00000000002", date("2019-02-01"), "90", "provider_1", "", "", "ref04", 2, "2019-02-01", "000001", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "00000000002", date("2019-02-01"), "90", "provider_2", "", "", "ref04", 2, "2019-02-01", "000002", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "00000000002", date("2019-02-01"), "90", "provider_3", "", "", "ref04", 2, "2019-02-01", "000003", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "00000000002", date("2019-02-01"), "90", "provider_4", "", "", "ref04", 2, "2019-02-01", "000004", "A", "A2", "Payer A", "0", "A", "", "", 0.0, 0.0, 0.0),

                // Should use latest DATE_OF_SERVICE because the other 2 are same
                ("10043789", "300000", "00000000003", date("2019-03-01"), "90", "provider_1", "", "", "ref04", 2, "2019-03-01", "000000", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "300000", "00000000003", date("2019-03-02"), "90", "provider_2", "", "", "ref04", 2, "2019-03-01", "000000", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "300000", "00000000003", date("2019-03-03"), "90", "provider_3", "", "", "ref04", 2, "2019-03-01", "000000", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "300000", "00000000003", date("2019-03-04"), "90", "provider_4", "", "", "ref04", 2, "2019-03-01", "000000", "A", "A2", "Payer A", "0", "A", "", "", 0.0, 0.0, 0.0)
            ).toDF(ExtendedPharmacyColumns: _*)

            saveTestData(path, Subdirectories.OpenPharmacyLegacy, pharmacy)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val actualEvents = readPharmacy(prescribers, Open, etlParams)(spark)._2.collect()

            actualEvents should have size 3
            actualEvents.map(_.tags.getOrElse(Event.PrescriberIdTag, Seq.empty).head) should equal(Seq(
                "provider_3",
                "provider_3",
                "provider_3"
            ))
            actualEvents.map(_.date) should equal(Seq(
                date("2019-01-01"),
                date("2019-02-01"),
                date("2019-03-03")
            ))
        })
    }

    it("should use coalesce(l_final_status, l_transaction_response_status) to find information for fields of event") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val pharmacy = Seq(
                // 1st event: rejected (all claim are rejected)
                ("10043789", "100000", "00000000001", date("2017-07-01"), "90", "", "", "", "ref01", 1, "2017-07-01", "000000", "R", "B1", "Payer A", "1", "R", "", "", 0.0, 0.0, 0.0),
                ("10043789", "100000", "00000000001", date("2017-07-01"), "90", "", "", "", "ref01", 1, "2017-07-01", "000000", "R", "B1", "Payer A", "2", "P", "", "", 0.0, 0.0, 0.0),
                // 2nd event: abandoned (P < A & B2)
                ("10043789", "200000", "00000000001", date("2018-01-01"), "90", "", "", "", "ref02", 1, "2018-01-01", "000000", "A", "B2", "Payer A", "1", "A", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "00000000001", date("2018-01-01"), "90", "", "", "", "ref02", 1, "2018-01-01", "000000", "A", "B2", "Payer B", "2", "A", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "00000000001", date("2018-01-01"), "90", "", "", "", "ref02", 1, "2018-01-01", "000000", "A", "A1", "Payer B", "3", "P", "", "", 0.0, 0.0, 0.0),
                // 3rd event: abandoned with approved claims (P = A & B2)
                ("10043789", "200000", "00000000001", date("2018-02-01"), "90", "", "", "", "ref03", 1, "2018-02-01", "000000", "A", "B2", "Payer A", "1", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "00000000001", date("2018-02-01"), "90", "", "", "", "ref03", 1, "2018-02-01", "000000", "P", "B2", "Payer A", "2", "A", "", "", 0.0, 0.0, 0.0),
                // 4th event: dispensed
                ("10043789", "300000", "00000000001", date("2019-01-01"), "90", "", "", "", "ref04", 1, "2019-01-01", "000000", "P", "B1", "Payer A", "1", "P", "", "", 0.0, 0.0, 0.0),
                // 5th event: dispensed with rejected records (P > A & B2)
                ("10043789", "400000", "00000000001", date("2019-02-01"), "90", "", "", "", "ref04", 2, "2019-02-01", "000000", "P", "B1", "Payer A", "1", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "400000", "00000000001", date("2019-02-01"), "90", "", "", "", "ref04", 2, "2019-02-02", "000000", "P", "B1", "Payer A", "2", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "400000", "00000000001", date("2019-02-01"), "90", "", "", "", "ref04", 2, "2019-02-03", "000000", "A", "A2", "Payer A", "3", "P", "", "", 0.0, 0.0, 0.0),
                // 6th event: dispensed with rejected claims (P > A & B2)
                ("10043789", "400000", "00000000001", date("2019-03-01"), "90", "", "", "", "ref04", 3, "2019-03-01", "000000", "P", "B1", "Payer A", "1", "A", "", "", 0.0, 0.0, 0.0),
                ("10043789", "400000", "00000000001", date("2019-03-01"), "90", "", "", "", "ref04", 3, "2019-03-02", "000000", "A", "A2", "Payer B", "2", "A", "", "", 0.0, 0.0, 0.0)
            ).toDF(ExtendedPharmacyColumns: _*)

            saveTestData(path, Subdirectories.OpenPharmacyLegacy, pharmacy)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val actualEvents = readPharmacy(prescribers, Open, etlParams)(spark)._2.collect()

            actualEvents should have size 6
            actualEvents.map(_.tags.getOrElse(Event.ClaimStatusTag, Seq.empty).head) should equal(Seq(
                ClaimStatus.Rejected,
                ClaimStatus.Abandoned,
                ClaimStatus.Abandoned,
                ClaimStatus.Dispensed,
                ClaimStatus.Dispensed,
                ClaimStatus.Dispensed
            ))
            actualEvents.map(_.amount.get) should equal(Seq(
                2.0, // from P L_FINAL_STATUS record
                3.0, // from P L_FINAL_STATUS record
                1.0, // from P L_FINAL_STATUS record
                1.0, // from P L_FINAL_STATUS record
                3.0, // from latest P L_FINAL_STATUS record order by date_authorized and time_authorized
                // from latest record order by date_authorized and time_authorized since there is no P L_FINAL_STATUS record
                // and having P L_TRANSACTION_RESPONSE_STATUS at different record doesn't affect it.
                2.0
            ))
        })
    }

    it("should keep only Dispensed events when keepPharmacyDispensedOnly = true") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString, keepPharmacyDispensedOnly = true)

            val pharmacy = Seq(
                // 1st event: rejected (all claim are rejected)
                ("10043789", "100000", "00000000001", date("2017-07-01"), "90", "", "", "", "ref01", 1, "2017-07-01", "000000", "R", "B1", "Payer A", "0", "R", "", "", 0.0, 0.0, 0.0),
                ("10043789", "100000", "00000000001", date("2017-07-01"), "90", "", "", "", "ref01", 1, "2017-07-01", "000000", "R", "B1", "Payer A", "0", "R", "", "", 0.0, 0.0, 0.0),
                // 2nd event: abandoned (P < A & B2)
                ("10043789", "200000", "00000000001", date("2018-01-01"), "90", "", "", "", "ref02", 1, "2018-01-01", "000000", "A", "B2", "Payer A", "0", "A", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "00000000001", date("2018-01-01"), "90", "", "", "", "ref02", 1, "2018-01-01", "000000", "A", "B2", "Payer B", "0", "A", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "00000000001", date("2018-01-01"), "90", "", "", "", "ref02", 1, "2018-01-01", "000000", "A", "A1", "Payer B", "0", "A", "", "", 0.0, 0.0, 0.0),
                // 3rd event: abandoned with approved claims (P = A & B2)
                ("10043789", "200000", "00000000001", date("2018-01-01"), "90", "", "", "", "ref03", 1, "2018-01-01", "000000", "A", "B2", "Payer A", "0", "A", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "00000000001", date("2018-01-01"), "90", "", "", "", "ref03", 1, "2018-01-01", "000000", "P", "B2", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                // 4th event: dispensed
                ("10043789", "300000", "00000000001", date("2019-01-01"), "90", "", "", "", "ref04", 1, "2019-01-01", "000000", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                // 5th event: dispensed with rejected records (P > A & B2)
                ("10043789", "400000", "00000000001", date("2019-02-01"), "90", "", "", "", "ref04", 2, "2019-02-01", "000000", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "400000", "00000000001", date("2019-02-01"), "90", "", "", "", "ref04", 2, "2019-02-01", "000000", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "400000", "00000000001", date("2019-02-01"), "90", "", "", "", "ref04", 2, "2019-02-01", "000000", "A", "A2", "Payer A", "0", "A", "", "", 0.0, 0.0, 0.0),
                // 6th event: dispensed with rejected claims (P > A & B2)
                ("10043789", "400000", "00000000001", date("2019-03-01"), "90", "", "", "", "ref04", 3, "2019-03-01", "000000", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "400000", "00000000001", date("2019-03-01"), "90", "", "", "", "ref04", 3, "2019-03-01", "000000", "A", "A2", "Payer B", "0", "A", "", "", 0.0, 0.0, 0.0)
            ).toDF(ExtendedPharmacyColumns: _*)

            saveTestData(path, Subdirectories.OpenPharmacyLegacy, pharmacy)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val actualEvents = readPharmacy(prescribers, Open, etlParams)(spark)._2.collect()

            actualEvents should have size 3
            actualEvents.map(_.tags.getOrElse(Event.ClaimStatusTag, Seq.empty).head) should equal(Seq(
                ClaimStatus.Dispensed,
                ClaimStatus.Dispensed,
                ClaimStatus.Dispensed
            ))
        })
    }

    it("should find event field values from the latest valid record") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val pharmacy = Seq(
                // 1st event: has multiple dispensed records, take values from the latest one
                ("10043789", "100000", "00000000001", date("2010-01-01"), "0", "", "", "", "ref01", 1, "2017-07-01", "000000", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "100000", "00000000001", date("2017-07-01"), "90", "", "", "", "ref01", 1, "2017-07-01", "000001", "P", "B1", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                // 1st event: has both abandoned and dispensed records, take values from the latest dispensed one
                ("10043789", "200000", "00000000001", date("2010-01-01"), "", "", "", "", "ref02", 1, "2018-01-01", "000000", "A", "B2", "Payer A", "0", "A", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "00000000001", date("2017-07-02"), "90", "", "", "", "ref02", 1, "2018-01-02", "000000", "P", "B2", "Payer A", "0", "P", "", "", 0.0, 0.0, 0.0),
                ("10043789", "200000", "00000000001", date("2010-01-01"), "", "", "", "", "ref02", 1, "2018-01-03", "000000", "A", "A1", "Payer A", "0", "A", "", "", 0.0, 0.0, 0.0),
                // 3rd event: has no dispensed record, take values from the latest one
                ("10043789", "300000", "00000000001", date("2010-01-01"), "", "", "", "", "ref03", 1, "2018-01-01", "001000", "R", "A1", "Payer A", "0", "R", "", "", 0.0, 0.0, 0.0),
                ("10043789", "300000", "00000000001", date("2017-07-03"), "", "", "", "", "ref03", 1, "2018-01-02", "230000", "A", "B2", "Payer A", "0", "A", "", "", 0.0, 0.0, 0.0)
            ).toDF(ExtendedPharmacyColumns: _*)

            saveTestData(path, Subdirectories.OpenPharmacyLegacy, pharmacy)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val actualEvents = readPharmacy(prescribers, Open, etlParams)(spark)._2.collect()

            actualEvents should have size 3
            actualEvents.map(_.date) should equal(Seq(date("2017-07-01"), date("2017-07-02"), date("2017-07-03")))
            actualEvents.map(_.tags(Event.DaysSupplyTag).headOption) should equal(Seq(Some("90"), Some("90"), None))
        })
    }

    it("should compute gender and dateOfBirth frequency maps correctly without considering AuthorizedTime") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val pharmacy = Seq(
                ("1", "100000", "50419025001", date("2019-07-08"), "030", "", date("1937-01-01"), "1", "000000027170", 1, "2019-07-08", "134333", "R", "B1", "PCS ADVANCE PARADIGM", "0", "R", "", "", 0.0, 0.0, 0.0),
                ("1", "200000", "50419025001", date("2019-07-08"), "000", "", null, null, "000000027170", 1, "2019-07-08", "134338", "S", "B2", "PCS ADVANCE PARADIGM", "0", "S", "", "", 0.0, 0.0, 0.0),
                ("1", "300000", "50419025001", date("2019-07-08"), "000", "", date("1937-01-01"), "1", "000000027170", 1, "2019-07-08", "134339", "S", "B2", "PCS ADVANCE PARADIGM", "0", "S", "", "", 0.0, 0.0, 0.0),
                ("1", "400000", "50419025001", date("2019-07-08"), "000", "", date("1947-01-08"), "2", "000000027170", 2, "2020-12-30", "888888", "S", "B2", "PCS ADVANCE PARADIGM", "0", "S", "", "", 0.0, 0.0, 0.0)
            ).toDF(ExtendedPharmacyColumns: _*)

            saveTestData(path, Subdirectories.OpenPharmacyLegacy, pharmacy)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val actualSubjects = readPharmacy(prescribers, Open, etlParams)(spark)._1.collect()

            actualSubjects should be(Seq(
                forianSubjectWithDefaults("1",
                    dateOfBirth = Map(date("1937-01-01") -> 2, date("1947-01-08") -> 1),
                    gender = Map(Male -> 2, Female -> 1),
                    network = NetworkStruct(hasOpen = true)
                )
            ))
        })
    }

    it("should be able to read Closed pharmacy files with dateOfBirth in years and gender code in letters") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val pharmacy = Seq(
                ("1", "100000", "0", date("2011-01-01"), "0", "1", "1980", "M", "0", "", "", 0.0, 0.0, 0.0),
                ("1", "200000", "0", date("2011-01-01"), "0", "1", "1990", "F", "0", "", "", 0.0, 0.0, 0.0),
                ("1", "300000", "0", date("2011-01-01"), "0", "1", null, "", "0", "", "", 0.0, 0.0, 0.0)
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()

            saveTestData(path, Subdirectories.ClosedPharmacyLegacy, pharmacy)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val (subjects, events) = readPharmacy(prescribers, Closed, etlParams)(spark)

            subjects.collect() should be(Seq(
                forianSubjectWithDefaults(
                    "1",
                    dateOfBirth = Map(date("1980-01-01") -> 1, date("1990-01-01") -> 1),
                    gender = Map(Male -> 1, Female -> 1),
                    network = NetworkStruct(hasClosed = true)
                )
            ))

            events.collect() should be(Seq(
                ForianEvent("1", "00000000000", date("2011-01-01"), Some(0), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "0"),
                        tag(Event.PrescriberIdTag -> "1"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Closed),
                        tag(ClaimNumbersTag -> "100000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("1")        ,
                Some(0.0)),
                ForianEvent("1", "00000000000", date("2011-01-01"), Some(0), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "0"),
                        tag(Event.PrescriberIdTag -> "1"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Closed),
                        tag(ClaimNumbersTag -> "200000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("1"),
                    Some(0.0)
                ),
                ForianEvent("1", "00000000000", date("2011-01-01"), Some(0), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "0"),
                        tag(Event.PrescriberIdTag -> "1"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Closed),
                        tag(ClaimNumbersTag -> "300000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("1"),
                    Some(0.0)
                )
            ))
        })
    }

    it("should be able to read hive partitions") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val pharmacy = Seq(
                ("1", "100000", "0", date("2011-01-01"), "0", "1", "", "", "0", "", "", 0.0, 0.0, 0.0),
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()

            saveTestData(path, s"${Subdirectories.OpenPharmacyLegacy}/published=2011-01-01", pharmacy)

            val pharmacyV2 = Seq(
                ("1", "200000", "1", date("2011-02-01"), "0", "1", "", "", "0", "", "", 0.0, 0.0, 0.0)
            )
                .toDF(PharmacyColumn: _*)
                .withUniqueResponseCodeColumns()

            saveTestData(path, s"${Subdirectories.OpenPharmacyLegacy}/published=2011-02-01", pharmacyV2)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val (subjects, events) = readPharmacy(prescribers, Open, etlParams)(spark)

            subjects.collect() should be(Seq(
                forianSubjectWithDefaults(
                    "1",
                    network = NetworkStruct(hasOpen = true)
                )
            ))

            events.collect() should be(Seq(
                ForianEvent("1", "00000000000", date("2011-01-01"), Some(0), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "0"),
                        tag(Event.PrescriberIdTag -> "1"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "100000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("1"),
                    Some(0.0)
                ),
                ForianEvent("1", "00000000001", date("2011-02-01"), Some(0), Category.Dispensing, Classifications.Dispensing,
                    unknownPrescriberTags ++ Map(
                        tag(Event.DaysSupplyTag -> "0"),
                        tag(Event.PrescriberIdTag -> "1"),
                        tag(Event.DataSourceTag -> DispensingDataSource.Pharmacy),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        tag(NetworkTag -> Open),
                        tag(ClaimNumbersTag -> "200000"),
                        emptyTag(LinkedDiagnosisTag),
                        tag(RawDataSourceTag -> ForianDataSource.Legacy),
                        emptyTag(Event.PayerIdTag),
                        tag(Event.CopayTag-> "0.0"),
                        tag(Event.PatientPayTag -> "0.0"),
                        tag(Event.CostTag -> "0.0")
                    ),
                    unknownPrescriberObjectTags("1"),
                    Some(0.0)
                )
            ))
        })
    }

    it("should collect all relevant linked diagnosis codes for an event") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val pharmacy = Seq(
                ("1", "100000", "50419025001", date("2019-07-08"), "030", "", date("1937-01-01"), "1", "000000027170", 1, "2019-07-08", "134333", "R", "B1", "PCS ADVANCE PARADIGM", "0", "R", "D001", "", 0.0, 0.0, 0.0),
                ("1", "100000", "50419025001", date("2019-07-08"), "030", "", date("1937-01-01"), "1", "000000027170", 1, "2019-07-08", "134333", "R", "B1", "PCS ADVANCE PARADIGM", "0", "R", "D002", "", 0.0, 0.0, 0.0),
                ("1", "100000", "50419025001", date("2019-07-08"), "030", "", date("1937-01-01"), "1", "000000027170", 1, "2019-07-08", "134333", "R", "B1", "PCS ADVANCE PARADIGM", "0", "R", "", "", 0.0, 0.0, 0.0)
            ).toDF(ExtendedPharmacyColumns: _*)

            saveTestData(path, Subdirectories.OpenPharmacyLegacy, pharmacy)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val events = readPharmacy(prescribers, Open, etlParams)(spark)._2.collect()

            events should have size 1
            events.head.tags(LinkedDiagnosisTag) should contain allElementsOf Seq("D001", "D002")
        })
    }

    private def unknownPrescriberObjectTags(id: String) = {
        Map(
            Event.ProvidersTag -> Seq(
                Map(ProviderStructColumns.Id -> id,
                    ProviderStructColumns.Role -> ProviderRole.Prescribing,
                    ProviderStructColumns.Specialty -> Unknown,
                    ProviderStructColumns.DetailSpecialty -> Unknown
                )
            )
        )
    }

    private def prescriberObjectTags(id: String, speciality: String, detailSpecialty: String) = {
        Map(
            Event.ProvidersTag -> Seq(
                Map(ProviderStructColumns.Id -> id,
                    ProviderStructColumns.Role -> ProviderRole.Prescribing,
                    ProviderStructColumns.Specialty -> speciality,
                    ProviderStructColumns.DetailSpecialty -> detailSpecialty
                )
            )
        )
    }

    // Pharmacy defaults are different from the defaults in the ForianSubject case class
    private def forianSubjectWithDefaults(id: String, dateOfBirth: Map[Date, Int] = Map.empty, dateOfDeath: Date = null, gender: Map[String, Int] = Map.empty, network: NetworkStruct, rawDatasource: Seq[String] = Seq(ForianDataSource.Legacy)) = {
        ForianSubject(id, dateOfBirth, dateOfDeath, gender, network, rawDatasource)
    }

}
