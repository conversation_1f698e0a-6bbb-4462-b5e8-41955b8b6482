package com.pharmdash.workflow.dp.report.transformer

import com.pharmdash.workflow.dp.ExportType
import com.pharmdash.workflow.dp.algorithm.AdherenceAlgorithm._
import com.pharmdash.workflow.dp.report.ExportTag
import com.pharmdash.workflow.model.Subject
import com.pharmdash.workflow.test.AbstractDataProcessorTestCase
import com.pharmdash.workflow.test.EventTestUtils._
import org.apache.commons.lang3.StringUtils
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.functions._
import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner

import scala.collection.mutable.ArrayBuffer

@RunWith(classOf[JUnitRunner])
class AdherenceEsSubjectTransformerTest extends AbstractDataProcessorTestCase {

    test("can export subject tags and event tags as configured") {
        val testDataRDD: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject("1", "M", 1935,
                ArrayBuffer(
                    // event that has 2 measurement period
                    event(101, "A", "category", date("2006-01-05"), Map(
                        AdherenceTag -> Seq(
                            AdherenceStruct("A", 0, 3, "2006-01", adherence = 0.9D).toString,
                            AdherenceStruct("A", 1, 3, "2006-01", adherence = 0.7D).toString,
                            AdherenceStruct("A", 2, 3, "2006-01", insufficientData = true).toString,
                            AdherenceStruct("A", 0, 12, "2006-01", adherence = 0.5D).toString
                        ),
                        "age" -> Seq("71")
                    ), Seq("A")),
                    event(102, "B", "category", date("2006-02-05"), Map(
                        AdherenceTag -> Seq(
                            AdherenceStruct("B", 0, 3, "2006-02", adherence = 0.9D).toString,
                            AdherenceStruct("B", 0, 12, "2006-02", adherence = 0.1D).toString
                        ),
                        "state" -> Seq("NSW")
                    ), Seq("B")),
                    event(103, "C", "category", date("2006-03-05"), Map(
                        AdherenceTag -> Seq(
                            AdherenceStruct("C", 0, 3, "2006-03", insufficientData = true).toString,
                            AdherenceStruct("C", 0, 12, "2006-03", insufficientData = true).toString
                        ),
                        "age" -> Seq("72"), "state" -> Seq("QLD")
                    ), Seq("C"))
                ),
                tags = tag("ethnicGroup" -> Seq("Mandalorian"), "unusedTag" -> Seq("unusedValue"))
            )
        )

        val exporter = createTransformer()
        val result = exporter.transform(testDataRDD)

        val expectedResult = spark.read.schema(result.schema).json(
            spark.sparkContext.parallelize(Seq(
                s"""
                   |{
                   |  "_subjectId": "1",
                   |  "gender": ["M"],
                   |  "ethnicGroup": [
                   |    "Mandalorian"
                   |  ],
                   |  "adherence": [
                   |     ${adherenceStruct("A", 3, 0, "2006-01", 0.9D, age = 71)},
                   |     ${adherenceStruct("A", 3, 1, "2006-01", 0.7D, age = 71)},
                   |     ${adherenceStruct("A", 3, 2, "2006-01", insufficientData = true, age = 71)},
                   |     ${adherenceStruct("A", 12, 0, "2006-01", 0.5D, age = 71)},
                   |     ${adherenceStruct("B", 3, 0, "2006-02", 0.9D, state = "NSW")},
                   |     ${adherenceStruct("B", 12, 0, "2006-02", 0.1D, state = "NSW")},
                   |     ${adherenceStruct("C", 3, 0, "2006-03", insufficientData = true, age = 72, state = "QLD")},
                   |     ${adherenceStruct("C", 12, 0, "2006-03", insufficientData = true, age = 72, state = "QLD")}
                   |  ]
                   |}
                """.stripMargin))
        )

        val events = result.select(explode(col("adherence"))).collect()
        val expected = expectedResult.select(explode(col("adherence"))).collect()

        events should contain theSameElementsAs expected
    }

    test("can export multiple subjects") {
        val testDataRDD: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject("1", "M", 1935,
                ArrayBuffer(
                    // event that has 2 measurement period
                    event(101, "A", "category", date("2006-01-05"), Map(
                        AdherenceTag -> Seq(
                            AdherenceStruct("A", 0, 3, "2006-01", adherence = 0.9D).toString,
                            AdherenceStruct("A", 1, 3, "2006-01", adherence = 0.7D).toString,
                            AdherenceStruct("A", 2, 3, "2006-01", insufficientData = true).toString,
                            AdherenceStruct("A", 0, 12, "2006-01", adherence = 0.5D).toString
                        ),
                        "age" -> Seq("71")
                    ), Seq("A"))
                ),
                tags = tag("ethnicGroup" -> Seq("Mandalorian"), "unusedTag" -> Seq("unusedValue"))
            ),
            subject("2", "F", 1970,
                ArrayBuffer(
                    // event that has 2 measurement period
                    event(201, "B", "category", date("2006-01-05"), Map(
                        AdherenceTag -> Seq(
                            AdherenceStruct("B", 0, 3, "2006-07", adherence = 0.2D).toString,
                            AdherenceStruct("B", 1, 3, "2006-07", insufficientData = true).toString,
                            AdherenceStruct("B", 0, 12, "2006-07", adherence = 0.03D).toString
                        )
                    ), Seq("B"))
                )
            )
        )

        val exporter = createTransformer()
        val result = exporter.transform(testDataRDD)

        val expectedResult = spark.read.schema(result.schema).json(
            spark.sparkContext.parallelize(Seq(
                s"""
                   |{
                   |  "_subjectId": "1",
                   |  "gender": ["M"],
                   |  "ethnicGroup": [
                   |    "Mandalorian"
                   |  ],
                   |  "adherence": [
                   |     ${adherenceStruct("A", 3, 0, "2006-01", 0.9D, age = 71)},
                   |     ${adherenceStruct("A", 3, 1, "2006-01", 0.7D, age = 71)},
                   |     ${adherenceStruct("A", 3, 2, "2006-01", insufficientData = true, age = 71)},
                   |     ${adherenceStruct("A", 12, 0, "2006-01", 0.5D, age = 71)}
                   |  ]
                   |}
                """.stripMargin,
                s"""
                   |{
                   |  "_subjectId": "2",
                   |  "gender": ["F"],
                   |  "adherence": [
                   |     ${adherenceStruct("B", 3, 0, "2006-07", 0.2D)},
                   |     ${adherenceStruct("B", 3, 1, "2006-07", insufficientData = true)},
                   |     ${adherenceStruct("B", 12, 0, "2006-07", 0.03D)}
                   |  ]
                   |}
                """.stripMargin
            ))
        )

        val events = result.select(explode(col("adherence"))).collect()
        val expected = expectedResult.select(explode(col("adherence"))).collect()

        events should contain theSameElementsAs expected
    }

    test("should only create event level documents from events that has adherence tag") {
        val testDataRDD: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject("1", "M", 1935,
                ArrayBuffer(
                    // event that has 2 measurement period
                    event(101, "A", "category", date("2006-01-05"), Map(
                        AdherenceTag -> Seq(
                            AdherenceStruct("A", 0, 3, "2006-01", adherence = 0.9D).toString,
                            AdherenceStruct("A", 1, 3, "2006-01", adherence = 0.7D).toString,
                            AdherenceStruct("A", 2, 3, "2006-01", insufficientData = true).toString,
                            AdherenceStruct("A", 0, 12, "2006-01", adherence = 0.5D).toString
                        ),
                        "age" -> Seq("71")
                    ), Seq("A")),
                    event(102, "B", "category", date("2006-02-05"), Map("state" -> Seq("NSW")), Seq("B")),
                    event(103, "C", "category", date("2006-03-05"), Map("age" -> Seq("72"), "state" -> Seq("QLD")), Seq("C"))
                ),
                tags = tag("ethnicGroup" -> Seq("Mandalorian"), "unusedTag" -> Seq("unusedValue"))
            )
        )

        val exporter = createTransformer()
        val result = exporter.transform(testDataRDD)

        val expectedResult = spark.read.schema(result.schema).json(
            spark.sparkContext.parallelize(Seq(
                s"""
                   |{
                   |  "_subjectId": "1",
                   |  "gender": ["M"],
                   |  "ethnicGroup": [
                   |    "Mandalorian"
                   |  ],
                   |  "adherence": [
                   |     ${adherenceStruct("A", 3, 0, "2006-01", 0.9D, age = 71)},
                   |     ${adherenceStruct("A", 3, 1, "2006-01", 0.7D, age = 71)},
                   |     ${adherenceStruct("A", 3, 2, "2006-01", insufficientData = true, age = 71)},
                   |     ${adherenceStruct("A", 12, 0, "2006-01", 0.5D, age = 71)}
                   |  ]
                   |}
                """.stripMargin))
        )

        val events = result.select(explode(col("adherence"))).collect()
        val expected = expectedResult.select(explode(col("adherence"))).collect()

        events should contain theSameElementsAs expected
    }

    private def adherenceStruct(event: String, measurementPeriodLength: Int, measurementPeriodIndex: Int, initMonth: String,
                                adherence: java.lang.Double = null, insufficientData: java.lang.Boolean = null,
                                age: java.lang.Integer = null, state: String = null) = {
        s"""
           |    {
           |      "event": "$event",
           |      "measurementPeriodLength": $measurementPeriodLength,
           |      "measurementPeriodIndex": $measurementPeriodIndex,
           |      "initMonth": "$initMonth",
           |      "adherence": $adherence,
           |      "insufficientData": $insufficientData,
           |      "ageAtInitiation": ${"[" + Option(age).map(_.toString).getOrElse("") + "]"},
           |      "stateAtInitiation": ${"[" + Option(state).map(StringUtils.wrap(_, "\"")).getOrElse("") + "]"}
           |    }
           |""".stripMargin
    }

    def createTransformer(): DefaultEsSubjectTransformer = AdherenceEsSubjectTransformer(
        List(new ExportTag("ethnicGroup"), new ExportTag("gender")),
        List(new ExportTag("age", ExportType.LONG), new ExportTag("state"))
    )
}
