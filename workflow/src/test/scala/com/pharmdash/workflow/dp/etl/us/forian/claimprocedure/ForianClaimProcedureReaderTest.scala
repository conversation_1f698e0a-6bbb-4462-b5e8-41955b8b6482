package com.pharmdash.workflow.dp.etl.us.forian.claimprocedure

import com.pharmdash.workflow.dp.etl.us.forian.ForianClaimReaderTestBase._
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL.ForianDataset.{Closed, Open}
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL._
import com.pharmdash.workflow.dp.etl.us.forian.claimdiagnosis.ForianClaimDiagnosisReader.ReadRawForianClaimDiagnosis
import com.pharmdash.workflow.dp.etl.us.forian.claimprocedure.ForianClaimProcedureReader._
import com.pharmdash.workflow.dp.etl.us.forian.{ClaimProviderStateStruct, ForianClaimReaderTestBase}
import com.pharmdash.workflow.model.Event
import com.pharmdash.workflow.model.Event.PayerIdTag
import com.pharmdash.workflow.model.StandardGender.Unknown
import com.prospection.arch2.model.Category
import org.apache.spark.sql.Row
import org.apache.spark.sql.types.{StringType, StructField, StructType}
import org.junit.runner.RunWith
import org.scalatest.matchers.should
import org.scalatestplus.junit.JUnitRunner

import java.sql.Date

@RunWith(classOf[JUnitRunner])
class ForianClaimProcedureReaderTest extends ForianClaimReaderTestBase with should.Matchers {

    import spark.implicits._

    private val defaultEtlParams = ForianETLParams(
        startOfDataset = "2010-01-01",
        endOfDataset = "2022-06-30",
        bucket = "",
        rawDatasources = Map(ForianDataSource.Legacy -> ""),
    )

    private val claimPayerColumns: Seq[String] = Seq(
        "FORIAN_PATIENT_ID",
        "CLAIM_NUMBER",
        "PAYER_ID",
        "PAYER_NAME"
    )

    val claimPayerSchema: StructType = StructType(
        Seq(
            StructField("FORIAN_PATIENT_ID", StringType),
            StructField("CLAIM_NUMBER", StringType),
            StructField("PAYER_ID", StringType),
            StructField("PAYER_NAME", StringType)
        )
    )

    private val readRawForianClaimDiagnosis: ReadRawForianClaimDiagnosis = (_, _) => {
        claimDiagnosesDf(
            ("10043789", "100001", date("2015-01-01"), "D001", "1", "P", date("2022-01-01")),
            ("10043789", "100001", date("2015-01-01"), "D002", "2", "P", date("2022-01-01")),
            ("10043789", "100001", date("2015-01-01"), "D002", "3", "P", date("2022-01-01")),
            ("10043789", "100001", date("2015-01-01"), "D022", "4", "P", date("2022-01-01")),
            ("10043789", "100002", date("2016-01-01"), "D003", "1", "P", date("2022-01-01")),
            ("10043789", "100002", date("2016-01-01"), "D004", "1", "I", date("2022-01-01")),
            ("10043789", "100003", date("2017-01-01"), "D005", "1", "I", date("2022-01-01")),
            ("10043789", "100004", date("2020-01-01"), "D006", "1", "P", date("2022-01-01")),
        )
    }

    it("should be able to load data from claim_procedure") {
        withTemp(path => {
            // arrange
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimProcedure = claimProcedureDataFrame(Seq(
                ClaimProcedure("10043789", "100000", date("2015-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01"), lineCharge = Some(1.0)),
                ClaimProcedure("10043789", "300000", date("2017-01-01"), "P002", 2, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01"), lineCharge = Some(2.5)),
                ClaimProcedure("10043789", "600000", date("2020-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "00003029328", "", date("2022-01-01"), lineCharge = Some(3.0)),
            ))

            saveTestData(path, Subdirectories.OpenClaimProcedureLegacy, claimProcedure)
            saveTestData(path, Subdirectories.OpenClaimPayerLegacy, spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val claimProvider = spark.emptyDataset[ForianClaimProvider]

            // act
            val (procedureSubject, procedureEvents, allProcedureDispensingEvents) = readClaimProcedure(claimProvider, prescribers, Open, etlParams, readRawForianClaimDiagnosis)(spark)
            // assert
            procedureSubject.collect() should be(Seq(
                ForianSubject("10043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
            ))

            procedureEvents.collect() should contain theSameElementsAs Seq(
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2015-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("100000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq("1.0"),
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P002", date("2017-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("300000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq("2.5")
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2020-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("600000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq("3.0")
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                )
            )

            allProcedureDispensingEvents.collect() should contain theSameElementsAs Seq(
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "00003029328", date("2020-01-01"), amount = None, category = Category.Dispensing, classification = Classifications.Dispensing,
                    tags = unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        tag(Event.DataSourceTag -> DispensingDataSource.Procedure),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        ClaimNumbersTag -> Seq("600000"),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq("3.0")
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                )
            )

        })
    }

    it("should load data from legacy and bimonthly data sources") {
        withTemp(path => {
            // arrange
            val etlParams = defaultEtlParams.copy(
                bucket = path.toString,
                rawDatasources = Map(
                    ForianDataSource.Legacy -> "legacy",
                    ForianDataSource.Rx1 -> "rx1",
                    ForianDataSource.Rx2 -> "rx2",
                    ForianDataSource.BiMonthly -> "BiMonthly"
                )
            )

            val claimProcedureLegacy = claimProcedureDataFrame(Seq(
                ClaimProcedure("10043789", "100000", date("2015-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
                ClaimProcedure("10043789", "300000", date("2017-01-01"), "P002", 2, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
            ))

            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.Legacy)}/${Subdirectories.OpenClaimProcedureLegacy}", claimProcedureLegacy)
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.Legacy)}/${Subdirectories.OpenClaimPayerLegacy}", spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

            val claimProcedureBimonthly = claimProcedureDataFrame(Seq(
                ClaimProcedure("10043789", "300000", date("2017-01-01"), "P002", 2, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
            ))

            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.BiMonthly)}/${Subdirectories.BiMonthlyClaimProcedure}", claimProcedureBimonthly)

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val claimProvider = spark.emptyDataset[ForianClaimProvider]

            // act
            val (procedureSubject, procedureEvents, _) = readClaimProcedure(claimProvider, prescribers, Open, etlParams, readRawForianClaimDiagnosis)(spark)
            // assert
            procedureSubject.collect() should be(Seq(
                ForianSubject("10043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy, ForianDataSource.BiMonthly))
            ))

            procedureEvents.collect() should contain theSameElementsAs Seq(
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2015-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("100000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P002", date("2017-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("300000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy, ForianDataSource.BiMonthly),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                )
            )
        })
    }

    it("should return empty dataset if claim procedures is empty") {
        withTemp { path =>
            val etlParams = defaultEtlParams.copy(bucket = path.toString, rawDatasources = Map(ForianDataSource.BiMonthly -> ""))

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val claimProvider = spark.emptyDataset[ForianClaimProvider]

            val (procedureSubject, procedureEvents, _) = readClaimProcedure(claimProvider, prescribers, Closed, etlParams, readRawForianClaimDiagnosis)(spark)

            procedureSubject.count() should be(0)
            procedureEvents.count() should be(0)
        }
    }

    it("should be able to load provider data from claim_provider") {
        withTemp(path => {
            // arrange
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimProcedure = claimProcedureDataFrame(Seq(
                ClaimProcedure("10043789", "100000", date("2015-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
                ClaimProcedure("10043789", "300000", date("2017-01-01"), "P002", 2, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
                ClaimProcedure("10043789", "600000", date("2020-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "00003029328", "", date("2022-01-01")),
                ClaimProcedure("10043789", "700000", date("2021-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "00003029328", "", date("2022-01-01")),
                ClaimProcedure("10043789", "800000", date("2022-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "00003029328", "", date("2022-01-01")),
            ))

            saveTestData(path, Subdirectories.OpenClaimProcedureLegacy, claimProcedure)
            saveTestData(path, Subdirectories.OpenClaimPayerLegacy, spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val claimProvider = Seq(
                ForianClaimProvider("100000", ClaimProviderStateStruct(ProviderRole.Billing, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")), Seq(defaultProvider().copy("NPI1", ProviderRole.Billing, "Specialty1", "DetailSpecialty1", entityTypeCode = "1"))),
                ForianClaimProvider("300000", ClaimProviderStateStruct(ProviderRole.Billing, "NY", "10001", Date.valueOf("2023-02-01"), Date.valueOf("2023-02-02")), Seq(defaultProvider().copy("NPI2", ProviderRole.Billing, "Specialty2", "DetailSpecialty2", entityTypeCode = "2"))),
                ForianClaimProvider("600000", ClaimProviderStateStruct(ProviderRole.Billing, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")), Seq(defaultProvider().copy("NPI1", ProviderRole.Billing, "Specialty1", "DetailSpecialty1", entityTypeCode = "1"))),
                ForianClaimProvider("700000", ClaimProviderStateStruct(ProviderRole.Billing, null, "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")), Seq(defaultProvider().copy("NPI1", ProviderRole.Billing, "Specialty1", "DetailSpecialty1", entityTypeCode = "1"))),
                ForianClaimProvider("800000", ClaimProviderStateStruct(ProviderRole.Billing, "", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")), Seq(defaultProvider().copy("NPI1", ProviderRole.Billing, "Specialty1", "DetailSpecialty1", entityTypeCode = "1")))
            ).toDS()

            // act
            val (procedureSubject, procedureEvents, _) = readClaimProcedure(claimProvider, prescribers, Open, etlParams, readRawForianClaimDiagnosis)(spark)

            // assert
            procedureSubject.collect() should be(Seq(
                ForianSubject("10043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
            ))

            procedureEvents.collect() should contain theSameElementsAs Seq(
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2015-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("100000"),
                        tag(NetworkTag -> Open),
                        tag(Event.StateTag -> "California"),
                        tag(Event.Zip5Tag -> "90001"),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq(providersTagObjectTags("NPI1", ProviderRole.Billing, "Specialty1", "DetailSpecialty1")))
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P002", date("2017-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("300000"),
                        tag(NetworkTag -> Open),
                        tag(Event.StateTag -> "New York"),
                        tag(Event.Zip5Tag -> "10001"),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq(providersTagObjectTags("NPI2", ProviderRole.Billing, "Specialty2", "DetailSpecialty2", "2")))
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2020-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("600000"),
                        tag(NetworkTag -> Open),
                        tag(Event.StateTag -> "California"),
                        tag(Event.Zip5Tag -> "90001"),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq(providersTagObjectTags("NPI1", ProviderRole.Billing, "Specialty1", "DetailSpecialty1")))
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2021-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("700000"),
                        tag(NetworkTag -> Open),
                        tag(Event.StateTag -> Unknown),
                        tag(Event.Zip5Tag -> "90001"),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq(providersTagObjectTags("NPI1", ProviderRole.Billing, "Specialty1", "DetailSpecialty1")))
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2022-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("800000"),
                        tag(NetworkTag -> Open),
                        tag(Event.StateTag -> Unknown),
                        tag(Event.Zip5Tag -> "90001"),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq(providersTagObjectTags("NPI1", ProviderRole.Billing, "Specialty1", "DetailSpecialty1")))
                )
            )
        })
    }

    it("should not load provider data from prescribers when rendering_provider_npi not available from claim_provider") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimProcedure = claimProcedureDataFrame(
                Seq(
                    defaultClaimProcedure().copy(
                        forianPatientId = "10043789",
                        claimNumber = "200000",
                        dateOfService = date("2016-01-01"),
                        dProcedureCode = "P001",
                        procedureSequence = 1,
                        dRenderingProviderNpi = "4",
                        receivedDate = date("2022-01-01"),

                    )
                )
            )

            saveTestData(path, Subdirectories.OpenClaimProcedureLegacy, claimProcedure)
            saveTestData(path, Subdirectories.OpenClaimPayerLegacy, spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

            val claimProvider =
                Seq(
                    ForianClaimProvider(
                        "400000",
                        null,
                        Seq(
                            defaultProvider().copy(id = "6", role = ProviderRole.Rendering)
                        )
                    )
                ).toDS()

            val prescribers = Seq(
                ForianPrescriber("4", "", "Specialist", "Specialist 1", entityTypeCode = "1")
            ).toDS()

            val (procedureSubject, procedureEvents, _) = readClaimProcedure(claimProvider, prescribers, Open, etlParams, readRawForianClaimDiagnosis)(spark)

            procedureSubject.collect() should be(Seq(
                ForianSubject("10043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
            ))

            val expectedProcedureEvent = Seq(
                ForianEvent("10043789", "P001", date("2016-01-01"), None, Category.Procedure, Classifications.Procedure,
                    unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("200000"),
                        Event.PrescriberIdTag -> Seq("4"),
                        tag(Event.ReferrerIdTag -> Unknown),
                        Event.PrescriberSpecialtyTag -> Seq("Specialist"),
                        Event.PrescriberDetailSpecialtyTag -> Seq("Specialist 1"),
                        tag(NetworkTag -> Open),
                        LinkedDiagnosisTag -> Seq.empty,
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    Map(Event.ProvidersTag -> Seq(
                        providersTagObjectTags("4", "Rendering", "Specialist", "Specialist 1")
                    ))
                )
            )

            procedureEvents.collect() should contain theSameElementsAs expectedProcedureEvent

        })
    }

    it("should be able to load referrer details from providers") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimProcedure = claimProcedureDataFrame(Seq(
                ClaimProcedure("10043789", "200000", date("2016-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01"))
            ))

            saveTestData(path, Subdirectories.OpenClaimProcedureLegacy, claimProcedure)
            saveTestData(path, Subdirectories.OpenClaimPayerLegacy, spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

            val claimProvider =
                Seq(
                    ForianClaimProvider(
                        "200000",
                        null,
                        Seq(
                            defaultProvider().copy("6", ProviderRole.Referring, "Referring1", "ReferringDescription1", "1"),
                        )
                    )
                ).toDS()

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val (procedureSubject, procedureEvents, _) = readClaimProcedure(claimProvider, prescribers, Open, etlParams, readRawForianClaimDiagnosis)(spark)

            procedureSubject.collect() should be(Seq(
                ForianSubject("10043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
            ))

            val expectedProcedureEvent = Seq(
                ForianEvent("10043789", "P001", date("2016-01-01"), None, Category.Procedure, Classifications.Procedure,
                    unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("200000"),
                        Event.ReferrerIdTag -> Seq("6"),
                        Event.ReferrerSpecialtyTag -> Seq("Referring1"),
                        Event.ReferrerDetailSpecialtyTag -> Seq("ReferringDescription1"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    Map(Event.ProvidersTag -> Seq(
                        providersTagObjectTags("6", "Referring", "Referring1", "ReferringDescription1")
                    ))
                )
            )

            procedureEvents.collect() should contain theSameElementsAs expectedProcedureEvent

        })
    }

    it("should filter out event where d_procedure_code/d_product_id is not empty or null or invalid") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimProcedure = claimProcedureDataFrame(Seq(
                ClaimProcedure("10043789", "100000", date("2015-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "00003029328", "", date("2022-01-01")),
                ClaimProcedure("10043789", "200000", date("2016-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
                ClaimProcedure("10043789", "300000", date("2017-01-01"), "P002", 2, date("1970-01-01"), date("1970-01-01"), "", "", "00003029305", "", date("2022-01-01")),
                ClaimProcedure("10043789", "400000", date("2018-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", null, "", date("2022-01-01")),
                ClaimProcedure("10043789", "500000", date("2019-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "00003029328", "", date("2022-01-01")),
                ClaimProcedure("10043789", "600000", date("2018-01-01"), "", 2, date("1970-01-01"), date("1970-01-01"), "", "", "00003029305", "", date("2022-01-01")),
                ClaimProcedure("10043789", "700000", date("2019-01-01"), null, 2, date("1970-01-01"), date("1970-01-01"), "", "", "00003029305", "", date("2022-01-01")),
                ClaimProcedure("10043789", "800000", date("2019-01-01"), "___", 2, date("1970-01-01"), date("1970-01-01"), "", "", "abc", "", date("2022-01-01"))
            ))

            saveTestData(path, Subdirectories.OpenClaimProcedureLegacy, claimProcedure)
            saveTestData(path, Subdirectories.OpenClaimPayerLegacy, spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val claimProvider = spark.emptyDataset[ForianClaimProvider]

            val (procedureSubject, procedureEvents, allProcedureDispensingEvents) = readClaimProcedure(claimProvider, prescribers, Open, etlParams, readRawForianClaimDiagnosis)(spark)

            procedureSubject.collect() should be(Seq(
                ForianSubject("10043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
            ))

            procedureEvents.collect() should contain theSameElementsAs Seq(
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2015-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("100000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2016-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("200000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P002", date("2017-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("300000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2018-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("400000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2019-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("500000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                )
            )

            allProcedureDispensingEvents.collect() should contain theSameElementsAs Seq(
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "00003029328", date("2015-01-01"), amount = None, category = Category.Dispensing, classification = Classifications.Dispensing,
                    tags = unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        tag(Event.DataSourceTag -> DispensingDataSource.Procedure),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        ClaimNumbersTag -> Seq("100000"),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "00003029305", date("2017-01-01"), amount = None, category = Category.Dispensing, classification = Classifications.Dispensing,
                    tags = unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        tag(Event.DataSourceTag -> DispensingDataSource.Procedure),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        ClaimNumbersTag -> Seq("300000"),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "00003029305", date("2018-01-01"), amount = None, category = Category.Dispensing, classification = Classifications.Dispensing,
                    tags = unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        tag(Event.DataSourceTag -> DispensingDataSource.Procedure),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        ClaimNumbersTag -> Seq("600000"),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "00003029305", date("2019-01-01"), amount = None, category = Category.Dispensing, classification = Classifications.Dispensing,
                    tags = unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        tag(Event.DataSourceTag -> DispensingDataSource.Procedure),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        ClaimNumbersTag -> Seq("700000"),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "00003029328", date("2019-01-01"), amount = None, category = Category.Dispensing, classification = Classifications.Dispensing,
                    tags = unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        tag(Event.DataSourceTag -> DispensingDataSource.Procedure),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        ClaimNumbersTag -> Seq("500000"),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty

                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                )
            )

        })
    }

    it("should be able to deduplicate events") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)
            val closedClaimProcedure = claimProcedureDataFrame(Seq(
                ClaimProcedure("10043789", "100000", date("2015-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
                ClaimProcedure("10043789", "200000", date("2015-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
                ClaimProcedure("10043789", "300000", date("2016-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
                ClaimProcedure("10043789", "400000", date("2016-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
                ClaimProcedure("10043789", "500000", date("2017-01-01"), "P002", 3, date("1970-01-01"), date("1970-01-01"), "", "", "00003029328", "", date("2022-01-01")),
                ClaimProcedure("10043789", "600000", date("2018-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
                ClaimProcedure("10043789", "700000", date("2017-01-01"), "P002", 3, date("1970-01-01"), date("1970-01-01"), "", "", "00003029328", "", date("2022-01-01"))
            ))

            saveTestData(path, Subdirectories.ClosedClaimProcedureLegacy, closedClaimProcedure)
            saveTestData(path, Subdirectories.ClosedClaimPayerLegacy, spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

            val (procedureSubject, procedureEvents, allProcedureDispensingEvents) = readClaimProcedure(spark.emptyDataset, spark.emptyDataset, Closed, etlParams, readRawForianClaimDiagnosis)(spark)

            procedureSubject.collect() should contain theSameElementsAs Seq(
                ForianSubject("10043789", network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy))
            )

            unordered(procedureEvents.collect()) should contain theSameElementsAs unordered(Seq(
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2015-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("100000", "200000"),
                        tag(NetworkTag -> Closed),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2016-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("300000", "400000"),
                        tag(NetworkTag -> Closed),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P002", date("2017-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("500000", "700000"),
                        tag(NetworkTag -> Closed),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2018-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("600000"),
                        tag(NetworkTag -> Closed),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                )
            ))

            unordered(allProcedureDispensingEvents.collect()) should contain theSameElementsAs unordered(Seq(
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "00003029328", date("2017-01-01"), amount = None, category = Category.Dispensing, classification = Classifications.Dispensing,
                    tags = unknownProviderTags ++ Map(
                        tag(NetworkTag -> Closed),
                        tag(Event.DataSourceTag -> DispensingDataSource.Procedure),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        ClaimNumbersTag -> Seq("500000", "700000"),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                )
            ))
        })
    }

    it("should load and tag network for non null patient id") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimProcedure = claimProcedureDataFrame(Seq(
                ClaimProcedure("10043789", "300000", date("2012-06-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
                ClaimProcedure("10043789", "400000", date("2013-01-01"), "P001", 3, date("1970-01-01"), date("1970-01-01"), "", "", "00003029328", "", date("2022-01-01")),
                ClaimProcedure(null, "400001", date("2013-02-01"), "P001", 3, date("1970-01-01"), date("1970-01-01"), "", "", "00003029328", "", date("2022-01-01"))
            ))

            saveTestData(path, Subdirectories.OpenClaimProcedureLegacy, claimProcedure)
            saveTestData(path, Subdirectories.OpenClaimPayerLegacy, spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

            val (procedureSubject, procedureEvents, allProcedureDispensingEvents) = readClaimProcedure(spark.emptyDataset, spark.emptyDataset, Open, etlParams, readRawForianClaimDiagnosis)(spark)

            procedureSubject.collect() should contain theSameElementsAs Seq(
                ForianSubject("10043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
            )

            procedureEvents.collect() should contain theSameElementsAs Seq(
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2012-06-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("300000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2013-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("400000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                )
            )

            allProcedureDispensingEvents.collect() should contain theSameElementsAs Seq(
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "00003029328", date("2013-01-01"), amount = None, category = Category.Dispensing, classification = Classifications.Dispensing,
                    tags = unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        tag(Event.DataSourceTag -> DispensingDataSource.Procedure),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        ClaimNumbersTag -> Seq("400000"),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                )
            )
        })
    }

    it("should be able to remove any leading or trailing spaces") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimProcedure = claimProcedureDataFrame(Seq(
                ClaimProcedure("10043789", "200000", date("2016-01-01"), " P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
                ClaimProcedure("10043789", "300000", date("2017-01-01"), "P002 ", 1, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
                ClaimProcedure("10043789", "400000", date("2018-01-01"), " P003 ", 1, date("1970-01-01"), date("1970-01-01"), "", "", "00003029328", "", date("2022-01-01"))
            ))

            saveTestData(path, Subdirectories.OpenClaimProcedureLegacy, claimProcedure)
            saveTestData(path, Subdirectories.OpenClaimPayerLegacy, spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

            val (_, procedureEvents, dispensingEvents) = readClaimProcedure(spark.emptyDataset, spark.emptyDataset, Open, etlParams, readRawForianClaimDiagnosis)(spark)

            procedureEvents.collect() should contain theSameElementsAs Seq(
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2016-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("200000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P002", date("2017-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("300000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P003", date("2018-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("400000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                )
            )

            dispensingEvents.collect() should contain theSameElementsAs Seq(
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "00003029328", date("2018-01-01"), amount = None, category = Category.Dispensing, classification = Classifications.Dispensing,
                    tags = unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        tag(Event.DataSourceTag -> DispensingDataSource.Procedure),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        ClaimNumbersTag -> Seq("400000"),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                )
            )
        })
    }

    it("should only load events after startOfDataset and before endOfDataset") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimProcedure = claimProcedureDataFrame(Seq(
                ClaimProcedure("10043789", "100000", date("2009-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "00003029328", "", date("2022-01-01")),
                ClaimProcedure("10043789", "200000", date("2016-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
                ClaimProcedure("10043790", "300000", date("2016-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
                ClaimProcedure("10043791", "400000", date("2011-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
                ClaimProcedure("10043791", "500000", date("2019-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "00003029328", "", date("2022-01-01")),
                ClaimProcedure("10043792", "500000", date("2023-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "00003029328", "", date("2022-01-01"))
            ))

            saveTestData(path, Subdirectories.OpenClaimProcedureLegacy, claimProcedure)
            saveTestData(path, Subdirectories.OpenClaimPayerLegacy, spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

            val (_, procedureEvents, dispensingEvents) = readClaimProcedure(spark.emptyDataset, spark.emptyDataset, Open, etlParams, readRawForianClaimDiagnosis)(spark)

            procedureEvents.collect() should contain theSameElementsAs Seq(
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2016-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("200000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043790", code = "P001", date("2016-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("300000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043791", code = "P001", date("2011-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("400000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043791", code = "P001", date("2019-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("500000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                )
            )

            dispensingEvents.collect() should contain theSameElementsAs Seq(
                ForianEvent(FORIAN_PATIENT_ID = "10043791", code = "00003029328", date("2019-01-01"), amount = None, category = Category.Dispensing, classification = Classifications.Dispensing,
                    tags = unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        tag(Event.DataSourceTag -> DispensingDataSource.Procedure),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        ClaimNumbersTag -> Seq("500000"),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                )
            )
        })
    }

    it("should be able to read closed procedure data") {
        withTemp(path => {
            // arrange
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimProcedure = claimProcedureDataFrame(Seq(
                ClaimProcedure("10043789", "100000", date("2015-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
                ClaimProcedure("10043789", "600000", date("2020-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "00003029328", "", date("2022-01-01")),
            ))

            saveTestData(path, Subdirectories.ClosedClaimProcedureLegacy, claimProcedure)
            saveTestData(path, Subdirectories.ClosedClaimPayerLegacy, spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val claimProvider = spark.emptyDataset[ForianClaimProvider]

            // act
            val (procedureSubject, procedureEvents, allProcedureDispensingEvents) = readClaimProcedure(claimProvider, prescribers, Closed, etlParams, readRawForianClaimDiagnosis)(spark)
            // assert
            procedureSubject.collect() should be(Seq(
                ForianSubject("10043789", network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy))
            ))

            procedureEvents.collect() should contain theSameElementsAs Seq(
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2015-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("100000"),
                        tag(NetworkTag -> Closed),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                ),
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "P001", date("2020-01-01"), amount = None, category = Category.Procedure, classification = Classifications.Procedure,
                    tags = unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("600000"),
                        tag(NetworkTag -> Closed),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                )
            )

            allProcedureDispensingEvents.collect() should contain theSameElementsAs Seq(
                ForianEvent(FORIAN_PATIENT_ID = "10043789", code = "00003029328", date("2020-01-01"), amount = None, category = Category.Dispensing, classification = Classifications.Dispensing,
                    tags = unknownProviderTags ++ Map(
                        tag(NetworkTag -> Closed),
                        tag(Event.DataSourceTag -> DispensingDataSource.Procedure),
                        tag(Event.ClaimStatusTag -> ClaimStatus.Dispensed),
                        ClaimNumbersTag -> Seq("600000"),
                        unknownHcoId,
                        LinkedDiagnosisTag -> Seq.empty,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy),
                        PayerIdTag -> Seq.empty,
                        Event.CostTag -> Seq.empty
                    ),
                    objectTags = Map(Event.ProvidersTag -> Seq.empty)
                )
            )

        })
    }

    it("should correctly identify rendering provider for professional claim type") {
        withTemp(path => {
            // arrange
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimProcedure = claimProcedureDataFrame(Seq(
                ClaimProcedure("10043789", "100000", date("2015-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "P", "", "", "", date("2015-02-01")),
                ClaimProcedure("10043789", "150000", date("2015-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "I", "", "", "", date("2015-01-30")),
                ClaimProcedure("10043789", "200000", date("2017-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "P", "", "", "", date("2017-02-01")),
                ClaimProcedure("10043789", "300000", date("2020-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "P", "", "00003029328", "", date("2020-02-01")),
                ClaimProcedure("10043789", "400000", date("2021-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "P", "", "", "", date("2021-02-01")),
                ClaimProcedure("10043789", "500000", date("2022-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "P", "", "", "", date("2022-02-01")),
                ClaimProcedure("10043789", "600000", date("2022-01-02"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "P", "", "", "", date("2015-02-01"))
            ))

            saveTestData(path, Subdirectories.OpenClaimProcedureLegacy, claimProcedure)
            saveTestData(path, Subdirectories.OpenClaimPayerLegacy, spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val claimProvider = Seq(
                ForianClaimProvider("100000", defaultClaimProviderState(),
                    Seq(
                        defaultProvider().copy("NPI1", ProviderRole.Billing, "Billing1", "DetailBilling1", "1"),
                        defaultProvider().copy("NPI2", ProviderRole.Operating, "Operating1", "DetailOperating1", "1"),
                        defaultProvider().copy("NPI3", ProviderRole.Supervising, "Supervising1", "DetailSupervising1", "1"),
                        defaultProvider().copy("NPI4", ProviderRole.Attending, "Attending1", "DetailAttending1", "1"),
                        defaultProvider().copy("NPI5", ProviderRole.Rendering, "Rendering1", "DetailRendering1", "1"),
                    )
                ),
                ForianClaimProvider("150000", defaultClaimProviderState(),
                    Seq(
                        defaultProvider().copy("NPI1_1", ProviderRole.Billing, "Billing1", "DetailBilling1", "1"),
                        defaultProvider().copy("NPI2_1", ProviderRole.Operating, "Operating1", "DetailOperating1", "1"),
                        defaultProvider().copy("NPI3_1", ProviderRole.Supervising, "Supervising1", "DetailSupervising1", "1"),
                        defaultProvider().copy("NPI4_1", ProviderRole.Attending, "Attending1", "DetailAttending1", "1"),
                        defaultProvider().copy("NPI5_1", ProviderRole.Rendering, "Rendering1", "DetailRendering1", "1")
                    )
                ),
                ForianClaimProvider("200000", ClaimProviderStateStruct(ProviderRole.Attending, "CA", "90001", Date.valueOf("2023-02-01"), Date.valueOf("2023-02-02")),
                    Seq(
                        defaultProvider().copy("NPI6", ProviderRole.Billing, "Billing1", "DetailBilling1", "1"),
                        defaultProvider().copy("NPI7", ProviderRole.Operating, "Operating1", "DetailOperating1", "1"),
                        defaultProvider().copy("NPI8", ProviderRole.Supervising, "Supervising1", "DetailSupervising1", "1"),
                        defaultProvider().copy("NPI9", ProviderRole.Attending, "Attending1", "DetailAttending1", "1")
                    )
                ),
                ForianClaimProvider("300000", ClaimProviderStateStruct(ProviderRole.Supervising, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI10", ProviderRole.Billing, "Billing1", "DetailBilling1", "1"),
                        defaultProvider().copy("NPI11", ProviderRole.Operating, "Operating1", "DetailOperating1", "1"),
                        defaultProvider().copy("NPI12", ProviderRole.Supervising, "Supervising1", "DetailSupervising1", "1"),
                    )
                ),
                ForianClaimProvider("400000", ClaimProviderStateStruct(ProviderRole.Operating, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI13", ProviderRole.Billing, "Billing1", "DetailBilling1", "1"),
                        defaultProvider().copy("NPI14", ProviderRole.Operating, "Operating1", "DetailOperating1", "1")
                    )
                ),
                ForianClaimProvider("500000", ClaimProviderStateStruct(ProviderRole.Billing, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI15", ProviderRole.Billing, "Billing1", "DetailBilling1", "1")
                    )
                ),
                ForianClaimProvider("600000", ClaimProviderStateStruct(ProviderRole.OtherOperating, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI16", ProviderRole.OtherOperating, "OtherOperating1", "DetailOtherOperating1", "1")
                    )
                ),
            ).toDS()

            // act
            val (_, procedureEvents, allProcedureDispensingEvents) = readClaimProcedure(claimProvider, prescribers, Open, etlParams, readRawForianClaimDiagnosis)(spark)
            // assert
            val resultsEventsProviderTags = procedureEvents.collect().map(event => event.date -> event.tags.filterKeys(
                Set(
                    Event.Zip5Tag,
                    Event.StateTag,
                    Event.ReferrerIdTag,
                    Event.PrescriberSpecialtyTag,
                    Event.PrescriberIdTag,
                    Event.PrescriberDetailSpecialtyTag
                )
            )).toMap

            resultsEventsProviderTags(date("2015-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Rendering1"),
                Event.PrescriberIdTag -> Seq("NPI5"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailRendering1")
            )

            resultsEventsProviderTags(date("2017-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Attending1"),
                Event.PrescriberIdTag -> Seq("NPI9"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailAttending1")
            )

            resultsEventsProviderTags(date("2020-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Supervising1"),
                Event.PrescriberIdTag -> Seq("NPI12"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailSupervising1")
            )

            resultsEventsProviderTags(date("2021-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Operating1"),
                Event.PrescriberIdTag -> Seq("NPI14"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailOperating1")
            )

            resultsEventsProviderTags(date("2022-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Billing1"),
                Event.PrescriberIdTag -> Seq("NPI15"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailBilling1")
            )

            resultsEventsProviderTags(date("2022-01-02")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq(Unknown),
                Event.PrescriberIdTag -> Seq(Unknown),
                Event.PrescriberDetailSpecialtyTag -> Seq(Unknown)
            )


            val resultsDispensingEventsProviderTags = allProcedureDispensingEvents.collect().map(event => event.date -> event.tags.filterKeys(
                Set(
                    Event.Zip5Tag,
                    Event.StateTag,
                    Event.ReferrerIdTag,
                    Event.PrescriberSpecialtyTag,
                    Event.PrescriberIdTag,
                    Event.PrescriberDetailSpecialtyTag
                )
            )).toMap

            resultsDispensingEventsProviderTags(date("2020-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Supervising1"),
                Event.PrescriberIdTag -> Seq("NPI12"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailSupervising1")
            )
        })
    }

    it("should correctly identify rendering provider for institutional claim type") {
        withTemp(path => {
            // arrange
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimProcedure = claimProcedureDataFrame(Seq(
                ClaimProcedure("10043789", "100000", date("2015-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "I", "", "", "", date("2015-02-01")),
                ClaimProcedure("10043789", "150000", date("2015-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "P", "", "", "", date("2015-01-30")),
                ClaimProcedure("10043789", "200000", date("2017-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "I", "", "", "", date("2017-02-01")),
                ClaimProcedure("10043789", "300000", date("2020-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "I", "", "00003029328", "", date("2020-02-01")),
                ClaimProcedure("10043789", "400000", date("2021-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "I", "", "", "", date("2021-02-01")),
                ClaimProcedure("10043789", "500000", date("2022-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "I", "", "", "", date("2022-02-01")),
                ClaimProcedure("10043789", "600000", date("2022-01-02"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "I", "", "", "", date("2015-02-01"))
            ))

            saveTestData(path, Subdirectories.OpenClaimProcedureLegacy, claimProcedure)
            saveTestData(path, Subdirectories.OpenClaimPayerLegacy, spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

            val prescribers = spark.emptyDataset[ForianPrescriber]

            val claimProvider = Seq(
                ForianClaimProvider("100000", defaultClaimProviderState(),
                    Seq(
                        defaultProvider().copy("NPI1", ProviderRole.Billing, "Billing1", "DetailBilling1", "1"),
                        defaultProvider().copy("NPI2", ProviderRole.Operating, "Operating1", "DetailOperating1", "1"),
                        defaultProvider().copy("NPI3", ProviderRole.Supervising, "Supervising1", "DetailSupervising1", "1"),
                        defaultProvider().copy("NPI4", ProviderRole.Attending, "Attending1", "DetailAttending1", "1"),
                        defaultProvider().copy("NPI5", ProviderRole.Rendering, "Rendering1", "DetailRendering1", "1"),
                        defaultProvider().copy("NPI6", ProviderRole.OtherOperating, "OtherOperating1", "DetailOtherOperating1", "1"),
                    )
                ),
                ForianClaimProvider("150000", defaultClaimProviderState(),
                    Seq(
                        defaultProvider().copy("NPI1_1", ProviderRole.Billing, "Billing1", "DetailBilling1", "1"),
                        defaultProvider().copy("NPI2_1", ProviderRole.Operating, "Operating1", "DetailOperating1", "1"),
                        defaultProvider().copy("NPI3_1", ProviderRole.Supervising, "Supervising1", "DetailSupervising1", "1"),
                        defaultProvider().copy("NPI4_1", ProviderRole.Attending, "Attending1", "DetailAttending1", "1"),
                        defaultProvider().copy("NPI5_1", ProviderRole.Rendering, "Rendering1", "DetailRendering1", "1"),
                        defaultProvider().copy("NPI6_1", ProviderRole.OtherOperating, "OtherOperating1", "DetailOtherOperating1", "1"),
                    )
                ),
                ForianClaimProvider("200000", ClaimProviderStateStruct(ProviderRole.Attending, "CA", "90001", Date.valueOf("2023-02-01"), Date.valueOf("2023-02-02")),
                    Seq(
                        defaultProvider().copy("NPI7", ProviderRole.Billing, "Billing1", "DetailBilling1", "1"),
                        defaultProvider().copy("NPI8", ProviderRole.Operating, "Operating1", "DetailOperating1", "1"),
                        defaultProvider().copy("NPI9", ProviderRole.Supervising, "Supervising1", "DetailSupervising1", "1"),
                        defaultProvider().copy("NPI10", ProviderRole.Attending, "Attending1", "DetailAttending1", "1"),
                        defaultProvider().copy("NPI11", ProviderRole.OtherOperating, "OtherOperating1", "DetailOtherOperating1", "1"),
                    )
                ),
                ForianClaimProvider("300000", ClaimProviderStateStruct(ProviderRole.Supervising, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI12", ProviderRole.Billing, "Billing1", "DetailBilling1", "1"),
                        defaultProvider().copy("NPI13", ProviderRole.Operating, "Operating1", "DetailOperating1", "1"),
                        defaultProvider().copy("NPI14", ProviderRole.Supervising, "Supervising1", "DetailSupervising1", "1"),
                        defaultProvider().copy("NPI15", ProviderRole.OtherOperating, "OtherOperating1", "DetailOtherOperating1", "1"),
                    )
                ),
                ForianClaimProvider("400000", ClaimProviderStateStruct(ProviderRole.Operating, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI16", ProviderRole.Billing, "Billing1", "DetailBilling1", "1"),
                        defaultProvider().copy("NPI17", ProviderRole.Supervising, "Supervising1", "DetailSupervising1", "1"),
                        defaultProvider().copy("NPI18", ProviderRole.OtherOperating, "OtherOperating1", "DetailOtherOperating1", "1"),
                    )
                ),
                ForianClaimProvider("500000", ClaimProviderStateStruct(ProviderRole.Billing, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI19", ProviderRole.Billing, "Billing1", "DetailBilling1", "1"),
                        defaultProvider().copy("NPI20", ProviderRole.OtherOperating, "OtherOperating1", "DetailOtherOperating1", "1")
                    )
                ),
                ForianClaimProvider("600000", ClaimProviderStateStruct(ProviderRole.OtherOperating, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI21", ProviderRole.Billing, "Billing1", "DetailBilling1", "1")
                    )
                ),
            ).toDS()

            // act
            val (_, procedureEvents, allProcedureDispensingEvents) = readClaimProcedure(claimProvider, prescribers, Open, etlParams, readRawForianClaimDiagnosis)(spark)
            // assert
            val resultsEventsProviderTags = procedureEvents.collect().map(event => event.date -> event.tags.filterKeys(
                Set(
                    Event.Zip5Tag,
                    Event.StateTag,
                    Event.ReferrerIdTag,
                    Event.PrescriberSpecialtyTag,
                    Event.PrescriberIdTag,
                    Event.PrescriberDetailSpecialtyTag
                )
            )).toMap

            resultsEventsProviderTags(date("2015-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Rendering1"),
                Event.PrescriberIdTag -> Seq("NPI5"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailRendering1")
            )

            resultsEventsProviderTags(date("2017-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Attending1"),
                Event.PrescriberIdTag -> Seq("NPI10"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailAttending1")
            )

            resultsEventsProviderTags(date("2020-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Operating1"),
                Event.PrescriberIdTag -> Seq("NPI13"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailOperating1")
            )

            resultsEventsProviderTags(date("2021-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Supervising1"),
                Event.PrescriberIdTag -> Seq("NPI17"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailSupervising1")
            )

            resultsEventsProviderTags(date("2022-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("OtherOperating1"),
                Event.PrescriberIdTag -> Seq("NPI20"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailOtherOperating1")
            )

            resultsEventsProviderTags(date("2022-01-02")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Billing1"),
                Event.PrescriberIdTag -> Seq("NPI21"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailBilling1")
            )


            val resultsDispensingEventsProviderTags = allProcedureDispensingEvents.collect().map(event => event.date -> event.tags.filterKeys(
                Set(
                    Event.Zip5Tag,
                    Event.StateTag,
                    Event.ReferrerIdTag,
                    Event.PrescriberSpecialtyTag,
                    Event.PrescriberIdTag,
                    Event.PrescriberDetailSpecialtyTag
                )
            )).toMap

            resultsDispensingEventsProviderTags(date("2020-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Operating1"),
                Event.PrescriberIdTag -> Seq("NPI13"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailOperating1")
            )
        })
    }

    it("should collect payer id") {
        withTemp(path => {
            // arrange
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimProcedure = claimProcedureDataFrame(Seq(
                ClaimProcedure("10043789", "100000", date("2015-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "I", "", "", "", date("2015-02-01")),
                ClaimProcedure("10043789", "150000", date("2015-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "P", "", "", "", date("2015-01-30"))
            ))

            saveTestData(path, Subdirectories.OpenClaimProcedureLegacy, claimProcedure)

            val claimPayer = Seq(
                ("10043789", "100000", "P123", "Payer 123"),
                ("10043789", "150000", "P456", "Payer 456")
            ).toDF(claimPayerColumns: _*)
            saveTestData(path, Subdirectories.OpenClaimPayerLegacy, claimPayer)

            val prescribers = spark.emptyDataset[ForianPrescriber]
            val claimProvider = spark.emptyDataset[ForianClaimProvider]


            // act
            val (_, procedureEvents, allProcedureDispensingEvents) = readClaimProcedure(claimProvider, prescribers, Open, etlParams, readRawForianClaimDiagnosis)(spark)
            // assert
            val resultsEventsProviderTags = procedureEvents.collect().map(event => event.date -> event.tags.filterKeys(
                Set(
                    PayerIdTag
                )
            )).toMap

            resultsEventsProviderTags(date("2015-01-01")) should contain theSameElementsAs Map(
                PayerIdTag -> Seq("P123","P456")
            )
        })
    }

    describe("readClaimProcedure while finding facility") {
        it("should correctly identify facility for professional claim type") {
            withTemp(path => {
                // arrange
                val etlParams = defaultEtlParams.copy(bucket = path.toString)

                val procedures = claimProcedureDataFrame(
                    Seq(
                        defaultClaimProcedure().copy(forianPatientId = "10043789", claimNumber = "100000", dateOfService = date("2015-01-01"), receivedDate = date("2015-02-01"), lClaimTypeCode = "P", dProductId = ""),
                        defaultClaimProcedure().copy(forianPatientId = "10043789", claimNumber = "150000", dateOfService = date("2015-01-01"), receivedDate = date("2015-01-30"), lClaimTypeCode = "I", dProductId = ""),
                        defaultClaimProcedure().copy(forianPatientId = "10043789", claimNumber = "200000", dateOfService = date("2017-01-01"), receivedDate = date("2017-02-01"), lClaimTypeCode = "P", dProductId = ""),
                        defaultClaimProcedure().copy(forianPatientId = "10043789", claimNumber = "300000", dateOfService = date("2020-01-01"), receivedDate = date("2020-02-01"), lClaimTypeCode = "P", dProductId = "00003029328"),
                        defaultClaimProcedure().copy(forianPatientId = "10043789", claimNumber = "400000", dateOfService = date("2021-01-01"), receivedDate = date("2021-02-01"), lClaimTypeCode = "P", dProductId = ""),
                        defaultClaimProcedure().copy(forianPatientId = "10043789", claimNumber = "500000", dateOfService = date("2022-01-01"), receivedDate = date("2022-02-01"), lClaimTypeCode = "P", dProductId = ""),
                        defaultClaimProcedure().copy(forianPatientId = "10043789", claimNumber = "600000", dateOfService = date("2022-01-02"), receivedDate = date("2015-02-01"), lClaimTypeCode = "P", dProductId = ""),
                        defaultClaimProcedure().copy(forianPatientId = "10043789", claimNumber = "700000", dateOfService = date("2022-01-03"), receivedDate = date("2022-02-01"), lClaimTypeCode = "P", dProductId = "")
                    )
                )

                saveTestData(path, Subdirectories.OpenClaimProcedureLegacy, procedures)
                saveTestData(path, Subdirectories.OpenClaimPayerLegacy, spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

                val prescribers = spark.emptyDataset[ForianPrescriber]

                val claimProvider = Seq(
                    ForianClaimProvider("100000", defaultClaimProviderState(),
                        Seq(
                            defaultProvider().copy(id = "NPI1", role = ProviderRole.Billing, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI2", role = ProviderRole.Facility, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI3", role = ProviderRole.ServiceLocation, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI4", role = ProviderRole.Rendering, entityTypeCode = "2")
                        )
                    ),
                    ForianClaimProvider("150000", defaultClaimProviderState(),
                        Seq(
                            defaultProvider().copy(id = "NPI5", role = ProviderRole.Billing, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI6", role = ProviderRole.OtherOperating, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI7", role = ProviderRole.ServiceLocation, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI8", role = ProviderRole.Rendering, entityTypeCode = "2")
                        )
                    ),
                    ForianClaimProvider("200000", defaultClaimProviderState(),
                        Seq(
                            defaultProvider().copy(id = "NPI9", role = ProviderRole.Billing, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI10", role = ProviderRole.OtherOperating, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI11", role = ProviderRole.ServiceLocation, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI12", role = ProviderRole.Rendering, entityTypeCode = "2")
                        )
                    ),
                    ForianClaimProvider("300000", defaultClaimProviderState(),
                        Seq(
                            defaultProvider().copy(id = "NPI13", role = ProviderRole.Billing, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI14", role = ProviderRole.OtherOperating, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI15", role = ProviderRole.Rendering, entityTypeCode = "2")
                        )
                    ),
                    ForianClaimProvider("400000", defaultClaimProviderState(),
                        Seq(
                            defaultProvider().copy(id = "NPI16", role = ProviderRole.Billing, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI17", role = ProviderRole.Facility, entityTypeCode = "2")
                        )
                    ),
                    ForianClaimProvider("500000", defaultClaimProviderState(),
                        Seq(
                            defaultProvider().copy(id = "NPI18", role = ProviderRole.Billing, entityTypeCode = "2"),
                        )
                    ),
                    ForianClaimProvider("600000", defaultClaimProviderState(),
                        Seq(
                            defaultProvider().copy(id = "NPI19", role = ProviderRole.OtherOperating, entityTypeCode = "2")
                        )
                    ),
                    ForianClaimProvider("700000", defaultClaimProviderState(),
                        Seq(
                            defaultProvider().copy(id = "NPI20", role = ProviderRole.Attending, entityTypeCode = "1")
                        )
                    )
                ).toDS()

            // act
            val (_, procedureEvents, allProcedureDispensingEvents) = readClaimProcedure(claimProvider, prescribers, Open, etlParams, readRawForianClaimDiagnosis)(spark)
            // assert
            val resultsEventsProviderTags = procedureEvents.collect().map(event => event.date -> event.tags.filterKeys(
                Set(
                    Event.HcoId
                )
            )).toMap

                resultsEventsProviderTags should contain theSameElementsAs
                    Map(
                        date("2015-01-01") -> Map(
                            Event.HcoId -> Seq("NPI2")
                        ),
                        date("2017-01-01") -> Map(
                            Event.HcoId -> Seq("NPI11")
                        ),
                        date("2020-01-01") -> Map(
                            Event.HcoId -> Seq("NPI15")
                        ),
                        date("2021-01-01") -> Map(
                            Event.HcoId -> Seq("NPI17")
                        ),
                        date("2022-01-01") -> Map(
                            Event.HcoId -> Seq("NPI18")
                        ),
                        date("2022-01-02") -> Map(
                            unknownHcoId
                        ),
                        date("2022-01-03") -> Map(
                            unknownHcoId
                        )
                    )

                val resultsDispensingEventsProviderTags = allProcedureDispensingEvents.collect().map(event => event.date -> event.tags.filterKeys(
                    Set(
                        Event.HcoId
                    )
                )).toMap

                resultsDispensingEventsProviderTags should contain theSameElementsAs
                    Map(
                        date("2020-01-01") -> Map(
                            Event.HcoId -> Seq("NPI15")
                        )
                    )
            })
        }

        it("should correctly identify facility for institutional claim type") {
            withTemp(path => {
                // arrange
                val etlParams = defaultEtlParams.copy(bucket = path.toString)

                val procedures = claimProcedureDataFrame(
                    Seq(
                        defaultClaimProcedure().copy(forianPatientId = "10043789", claimNumber = "100000", dateOfService = date("2015-01-01"), receivedDate = date("2015-02-01"), lClaimTypeCode = "I", dProductId = ""),
                        defaultClaimProcedure().copy(forianPatientId = "10043789", claimNumber = "150000", dateOfService = date("2015-01-01"), receivedDate = date("2015-01-30"), lClaimTypeCode = "P", dProductId = ""),
                        defaultClaimProcedure().copy(forianPatientId = "10043789", claimNumber = "200000", dateOfService = date("2017-01-01"), receivedDate = date("2017-02-01"), lClaimTypeCode = "I", dProductId = ""),
                        defaultClaimProcedure().copy(forianPatientId = "10043789", claimNumber = "300000", dateOfService = date("2020-01-01"), receivedDate = date("2020-02-01"), lClaimTypeCode = "I", dProductId = "00003029328"),
                        defaultClaimProcedure().copy(forianPatientId = "10043789", claimNumber = "400000", dateOfService = date("2021-01-01"), receivedDate = date("2021-02-01"), lClaimTypeCode = "I", dProductId = ""),
                        defaultClaimProcedure().copy(forianPatientId = "10043789", claimNumber = "500000", dateOfService = date("2022-01-01"), receivedDate = date("2022-02-01"), lClaimTypeCode = "I", dProductId = ""),
                        defaultClaimProcedure().copy(forianPatientId = "10043789", claimNumber = "600000", dateOfService = date("2022-01-02"), receivedDate = date("2015-02-01"), lClaimTypeCode = "I", dProductId = ""),
                        defaultClaimProcedure().copy(forianPatientId = "10043789", claimNumber = "700000", dateOfService = date("2022-01-03"), receivedDate = date("2022-02-01"), lClaimTypeCode = "I", dProductId = "")
                    )
                )

                saveTestData(path, Subdirectories.OpenClaimProcedureLegacy, procedures)
                saveTestData(path, Subdirectories.OpenClaimPayerLegacy, spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

                val prescribers = spark.emptyDataset[ForianPrescriber]

                val claimProvider = Seq(
                    ForianClaimProvider("100000", defaultClaimProviderState(),
                        Seq(
                            defaultProvider().copy(id = "NPI1", role = ProviderRole.Billing, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI2", role = ProviderRole.Facility, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI3", role = ProviderRole.ServiceLocation, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI4", role = ProviderRole.Rendering, entityTypeCode = "2")
                        )
                    ),
                    ForianClaimProvider("150000", defaultClaimProviderState(),
                        Seq(
                            defaultProvider().copy(id = "NPI5", role = ProviderRole.Billing, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI6", role = ProviderRole.OtherOperating, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI7", role = ProviderRole.ServiceLocation, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI8", role = ProviderRole.Rendering, entityTypeCode = "2")
                        )
                    ),
                    ForianClaimProvider("200000", defaultClaimProviderState(),
                        Seq(
                            defaultProvider().copy(id = "NPI9", role = ProviderRole.Billing, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI10", role = ProviderRole.OtherOperating, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI11", role = ProviderRole.ServiceLocation, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI12", role = ProviderRole.Rendering, entityTypeCode = "2")
                        )
                    ),
                    ForianClaimProvider("300000", defaultClaimProviderState(),
                        Seq(
                            defaultProvider().copy(id = "NPI13", role = ProviderRole.Billing, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI14", role = ProviderRole.OtherOperating, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI15", role = ProviderRole.Rendering, entityTypeCode = "2")
                        )
                    ),
                    ForianClaimProvider("400000", defaultClaimProviderState(),
                        Seq(
                            defaultProvider().copy(id = "NPI16", role = ProviderRole.Billing, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI17", role = ProviderRole.Facility, entityTypeCode = "2")
                        )
                    ),
                    ForianClaimProvider("500000", defaultClaimProviderState(),
                        Seq(
                            defaultProvider().copy(id = "NPI18", role = ProviderRole.Billing, entityTypeCode = "2"),
                        )
                    ),
                    ForianClaimProvider("600000", defaultClaimProviderState(),
                        Seq(
                            defaultProvider().copy(id = "NPI19", role = ProviderRole.OtherOperating, entityTypeCode = "2")
                        )
                    ),
                    ForianClaimProvider("700000", defaultClaimProviderState(),
                        Seq(
                            defaultProvider().copy(id = "NPI20", role = ProviderRole.OtherOperating, entityTypeCode = "1"),
                            defaultProvider().copy(id = "NPI21", role = ProviderRole.Attending, entityTypeCode = "2")
                        )
                    )
                ).toDS()

            // act
            val (_, procedureEvents, allProcedureDispensingEvents) = readClaimProcedure(claimProvider, prescribers, Open, etlParams, readRawForianClaimDiagnosis)(spark)
            // assert
            val resultsEventsProviderTags = procedureEvents.collect().map(event => event.date -> event.tags.filterKeys(
                Set(
                    Event.HcoId
                )
            )).toMap

                resultsEventsProviderTags should contain theSameElementsAs
                    Map(
                        date("2015-01-01") -> Map(
                            Event.HcoId -> Seq("NPI2")
                        ),
                        date("2017-01-01") -> Map(
                            Event.HcoId -> Seq("NPI11")
                        ),
                        date("2020-01-01") -> Map(
                            Event.HcoId -> Seq("NPI13")
                        ),
                        date("2021-01-01") -> Map(
                            Event.HcoId -> Seq("NPI17")
                        ),
                        date("2022-01-01") -> Map(
                            Event.HcoId -> Seq("NPI18")
                        ),
                        date("2022-01-02") -> Map(
                            Event.HcoId -> Seq("Unknown")
                        ),
                        date("2022-01-03") -> Map(
                            unknownHcoId
                        )
                    )

                val resultsDispensingEventsProviderTags = allProcedureDispensingEvents.collect().map(event => event.date -> event.tags.filterKeys(
                    Set(
                        Event.HcoId
                    )
                )).toMap

                resultsDispensingEventsProviderTags should contain theSameElementsAs
                    Map(
                        date("2020-01-01") -> Map(
                            Event.HcoId -> Seq("NPI13")
                        )
                    )
            })
        }

        it("should correctly identify facility for unknown claim type") {
            withTemp(path => {
                // arrange
                val etlParams = defaultEtlParams.copy(bucket = path.toString)

                val procedures = claimProcedureDataFrame(
                    Seq(
                        defaultClaimProcedure().copy(forianPatientId = "10043789", claimNumber = "100000", dateOfService = date("2015-01-01"), receivedDate = date("2015-02-01"), lClaimTypeCode = "X", dProductId = "")
                    )
                )

                saveTestData(path, Subdirectories.OpenClaimProcedureLegacy, procedures)
                saveTestData(path, Subdirectories.OpenClaimPayerLegacy, spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

                val prescribers = spark.emptyDataset[ForianPrescriber]

                val claimProvider = Seq(
                    ForianClaimProvider("100000", defaultClaimProviderState(),
                        Seq(
                            defaultProvider().copy(id = "NPI1", role = ProviderRole.Billing, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI2", role = ProviderRole.Facility, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI3", role = ProviderRole.ServiceLocation, entityTypeCode = "2"),
                            defaultProvider().copy(id = "NPI4", role = ProviderRole.Rendering, entityTypeCode = "2")
                        )
                    )
                ).toDS()

            // act
            val (_, procedureEvents, allProcedureDispensingEvents) = readClaimProcedure(claimProvider, prescribers, Open, etlParams, readRawForianClaimDiagnosis)(spark)
            // assert
            val resultsEventsProviderTags = procedureEvents.collect().map(event => event.date -> event.tags.filterKeys(
                Set(
                    Event.HcoId
                )
            )).toMap

                resultsEventsProviderTags should contain theSameElementsAs
                    Map(
                        date("2015-01-01") -> Map(
                            Event.HcoId -> Seq("NPI4")
                        )
                    )

                val resultsDispensingEventsProviderTags = allProcedureDispensingEvents.collect().map(event => event.date -> event.tags.filterKeys(
                    Set(
                        Event.HcoId
                    )
                )).toMap

            resultsDispensingEventsProviderTags should be (empty)
        })
    }
}

    describe("readClaimProcedure linked diagnosis") {
        it("should resolve linked diagnosis for professional claim") {
            withTemp(path => {
                // arrange
                val etlParams = defaultEtlParams.copy(bucket = path.toString)

                val claimProcedure = claimProcedureDataFrame(Seq(
                    ClaimProcedure("10043789", "100001", date("2015-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "P", "", "", "", date("2022-01-01"), "1", "2", "3", "5"),
                    ClaimProcedure("10043789", "300000", date("2017-01-01"), "P002", 2, date("1970-01-01"), date("1970-01-01"), "P", "", "", "", date("2022-01-01")),
                    ClaimProcedure("10043789", "100004", date("2020-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "P", "", "00003029328", "", date("2022-01-01"), "1"),
                ))

                saveTestData(path, Subdirectories.OpenClaimProcedureLegacy, claimProcedure)
                saveTestData(path, Subdirectories.OpenClaimPayerLegacy, spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

                val prescribers = spark.emptyDataset[ForianPrescriber]

                val claimProvider = spark.emptyDataset[ForianClaimProvider]

                // act
                val (_, procedureEvents, allProcedureDispensingEvents) = readClaimProcedure(claimProvider, prescribers, Open, etlParams, readRawForianClaimDiagnosis)(spark)

                // assert
                val resultsEventsProviderTags = procedureEvents.collect().map(event => event.date -> event.tags.filterKeys(
                    Set(
                        LinkedDiagnosisTag
                    )
                )).toMap.mapValues(_.mapValues(_.sorted))


                resultsEventsProviderTags should contain theSameElementsAs
                    Map(
                        date("2015-01-01") -> Map(
                            LinkedDiagnosisTag -> Seq("D001", "D002")
                        ),
                        date("2017-01-01") -> Map(
                            LinkedDiagnosisTag -> Seq.empty
                        ),
                        date("2020-01-01") -> Map(
                            LinkedDiagnosisTag -> Seq("D006")
                        )
                    )
                val resultsDispensingEventsProviderTags = allProcedureDispensingEvents.collect().map(event => event.date -> event.tags.filterKeys(
                    Set(
                        LinkedDiagnosisTag
                    )
                )).toMap

                resultsDispensingEventsProviderTags should contain theSameElementsAs
                    Map(
                        date("2020-01-01") -> Map(
                            LinkedDiagnosisTag -> Seq("D006")
                        )
                    )
            })
        }

        it("should resolve linked diagnosis for institutional claim or claim without claim type") {
            withTemp(path => {
                // arrange
                val etlParams = defaultEtlParams.copy(bucket = path.toString)

                val claimProcedure = claimProcedureDataFrame(Seq(
                    ClaimProcedure("10043789", "100002", date("2016-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "I", "", "", "", date("2022-01-01")),
                    ClaimProcedure("10043789", "100003", date("2017-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "", "", "", "", date("2022-01-01")),
                    ClaimProcedure("10043789", "300000", date("2018-01-01"), "P002", 2, date("1970-01-01"), date("1970-01-01"), "I", "", "", "", date("2022-01-01")),
                ))

                saveTestData(path, Subdirectories.OpenClaimProcedureLegacy, claimProcedure)
                saveTestData(path, Subdirectories.OpenClaimPayerLegacy, spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

                val prescribers = spark.emptyDataset[ForianPrescriber]

                val claimProvider = spark.emptyDataset[ForianClaimProvider]

                // act
                val (_, procedureEvents, _) = readClaimProcedure(claimProvider, prescribers, Open, etlParams, readRawForianClaimDiagnosis)(spark)

                // assert
                val resultsEventsProviderTags = procedureEvents.collect().map(event => event.date -> event.tags.filterKeys(
                    Set(
                        LinkedDiagnosisTag
                    )
                )).toMap.mapValues(_.mapValues(_.sorted))

                resultsEventsProviderTags should contain theSameElementsAs
                    Map(
                        date("2016-01-01") -> Map(
                            LinkedDiagnosisTag -> Seq("D003", "D004")
                        ),
                        date("2017-01-01") -> Map(
                            LinkedDiagnosisTag -> Seq("D005")
                        ),
                        date("2018-01-01") -> Map(
                            LinkedDiagnosisTag -> Seq.empty
                        )
                    )
            })
        }

        it("should resolve linked diagnosis when event has multiple claims") {
            withTemp(path => {
                // arrange
                val etlParams = defaultEtlParams.copy(bucket = path.toString)

                val rawForianClaimDiagnosis: ReadRawForianClaimDiagnosis = (_, _) => {
                    claimDiagnosesDf(
                        ("10043789", "100001", date("2015-01-01"), "D001", "1", "P", date("2022-01-01")),
                        ("10043789", "100001", date("2015-01-01"), "D002", "2", "P", date("2022-01-01")),
                        ("10043789", "100002", date("2015-01-01"), "D002", "1", "P", date("2022-01-01")),
                        ("10043789", "100002", date("2015-01-01"), "D003", "2", "P", date("2022-01-01")),
                    )
                }

                val claimProcedure = claimProcedureDataFrame(Seq(
                    ClaimProcedure("10043789", "100001", date("2015-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "P", "", "", "", date("2022-01-01"), "1", "2"),
                    ClaimProcedure("10043789", "100002", date("2015-01-01"), "P001", 1, date("1970-01-01"), date("1970-01-01"), "I", "", "", "", date("2022-01-01")),
                ))

                saveTestData(path, Subdirectories.OpenClaimProcedureLegacy, claimProcedure)
                saveTestData(path, Subdirectories.OpenClaimPayerLegacy, spark.createDataFrame(spark.sparkContext.emptyRDD[Row], claimPayerSchema))

                val prescribers = spark.emptyDataset[ForianPrescriber]

                val claimProvider = spark.emptyDataset[ForianClaimProvider]

                // act
                val (_, procedureEvents, _) = readClaimProcedure(claimProvider, prescribers, Open, etlParams, rawForianClaimDiagnosis)(spark)

                // assert
                val resultsEventsProviderTags = procedureEvents.collect().map(event => event.date -> event.tags.filterKeys(
                    Set(
                        LinkedDiagnosisTag
                    )
                )).toMap.mapValues(_.mapValues(_.sorted))

                resultsEventsProviderTags should contain theSameElementsAs
                    Map(
                        date("2015-01-01") -> Map(
                            LinkedDiagnosisTag -> Seq("D001", "D002", "D003")
                        )
                    )
            })
        }
    }

}
