package com.pharmdash.workflow.dp.etl.us.forian

import com.pharmdash.workflow.WorkflowContext
import com.pharmdash.workflow.dp.etl.EtlUtils.sqlDateOrdering
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL._
import com.pharmdash.workflow.dp.etl.us.forian.claimprocedure.{ClaimProcedure, ClaimProcedureBuilder}
import com.pharmdash.workflow.dp.etl.us.forian.claimprovider.{EnrichedProvider, Provider}
import com.pharmdash.workflow.dp.etl.us.forian.function.ProviderFunctions.ProviderStructColumns
import com.pharmdash.workflow.dp.etl.us.prescriber.{NpiCsv, NuccCsv}
import com.pharmdash.workflow.dp.meta.processorannotations.Channel
import com.pharmdash.workflow.model.StandardGender.Unknown
import com.pharmdash.workflow.model.{Event, Subject}
import com.pharmdash.workflow.test.{TechnicalModelTestSuiteBase, TempFiles}
import org.apache.spark.sql.catalyst.expressions.GenericRowWithSchema
import org.apache.spark.sql.functions.{concat, lit, monotonically_increasing_id}
import org.apache.spark.sql.types.{DateType, DoubleType, IntegerType, StringType, StructField, StructType}
import org.apache.spark.sql.{DataFrame, Dataset, Row}
import org.mockito.ArgumentMatcher
import org.mockito.ArgumentMatchers.argThat
import org.scalatest.funspec.AnyFunSpec

import java.io.File
import java.nio.file.Path
import java.sql.Date

object ForianClaimReaderTestBase {
    def providersTagObjectTags(id: String, role: String, speciality: String, detailSpeciality: String, entityTypeCode: String = "1"): Map[String, String] = {
        Map(ProviderStructColumns.Id -> id,
            ProviderStructColumns.Role -> role,
            ProviderStructColumns.Specialty -> speciality,
            ProviderStructColumns.DetailSpecialty -> detailSpeciality,
            ProviderStructColumns.EntityTypeCode -> entityTypeCode
        )
    }

    def tag[T](tuple: (String, T)): (String, Seq[String]) = {
        (tuple._1, Seq(tuple._2.toString))
    }

    def emptyTag(tag: String): (String, Seq[String]) = {
        (tag, Seq())
    }

    val unknownProviderTags: Map[String, Seq[String]] = Map(
        Event.Zip5Tag -> Seq(Unknown),
        Event.StateTag -> Seq(Unknown),
        Event.ReferrerIdTag -> Seq(Unknown),
        Event.ReferrerSpecialtyTag -> Seq(Unknown),
        Event.ReferrerDetailSpecialtyTag -> Seq(Unknown),
        Event.PrescriberSpecialtyTag -> Seq(Unknown),
        Event.PrescriberIdTag -> Seq(Unknown),
        Event.PrescriberDetailSpecialtyTag -> Seq(Unknown)
    )

    val unknownHcoId: (String, Seq[String]) =
        tag(Event.HcoId -> Unknown)

    def unordered(events: Seq[ForianEvent]): Seq[ForianEvent] = {
        // order doesn't matter for tags here
        events.map(event => event.copy(tags = event.tags.mapValues(_.sorted)))
            // order events to compare easier
            .sortBy(e => (e.FORIAN_PATIENT_ID, e.date, e.code))
    }

}

abstract class ForianClaimReaderTestBase extends AnyFunSpec with TechnicalModelTestSuiteBase with TempFiles {

    import spark.implicits._

    val NPI_GENERATED_DATE = "2023-02-03"
    val NPI_GENERATED_NEXT_DATE = "2023-02-10"

    def filterEvents(subjects: Dataset[Subject], classification: String): Dataset[Subject] = {
        subjects.map(subject => {
            subject.events = subject.events.filter(e => classification == e.classification)
            subject
        })
    }

    def saveTestData(path: Path, fileName: String, data: DataFrame): File = {
        writeParquets(data, s"$fileName", path)
    }

    private def saveTestData(path: Path, fileName: String, data: String) = {
        val specificPath = s"$path/$fileName"
        mkDir(specificPath)
        write(data, s"$fileName/data.csv", path)
    }

    def npi(npi: String, activeIndex: Int, prescriberCode: String, state: String = "", zip: String = ""): String = {
        ((1 to 15)
            .flatMap(i => if (i == activeIndex) Seq("Y", prescriberCode) else Seq("", "")) :+
            npi :+
            state).mkString(",")
    }

    def defaultClaimProviderState(): ClaimProviderStateStruct = {
        ClaimProviderStateStruct(ProviderRole.Rendering, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02"))
    }

    def defaultClaimProcedure(): ClaimProcedure = {
        new ClaimProcedureBuilder()
            .withDateOfService(date("2015-01-01"))
            .withDProcedureCode("P001")
            .withProcedureSequence(1)
            .withServiceFromDate(date("1970-01-01"))
            .withServiceToDate(date("1970-01-01"))
            .withLClaimTypeCode("")
            .withLPlaceOfServiceCode("")
            .withDProductId("")
            .withReceivedDate(date("2015-01-30"))
            .build()
    }

    def defaultProvider(): Provider = {
        Provider(id = null, role = null, specialty = "test-speciality", detailSpecialty = "test-detail-speciality", entityTypeCode = "1")
    }

    def defaultEnrichedProvider(): EnrichedProvider = {
        EnrichedProvider(id = null, role = null, specialty = "test-speciality", detailSpecialty = "test-detail-speciality", receivedDate = null, claimTypeCode = "", entityTypeCode = "1")
    }

    def defaultPrescriber(): ForianPrescriber = {
        ForianPrescriber(prescriberId = null, state = null, prescriberSpecialty = "test-speciality", prescriberDetailSpecialty = "test-detail-speciality", entityTypeCode = "1")
    }

    val PharmacyColumn: Seq[String] = Seq(
        "FORIAN_PATIENT_ID",
        "CLAIM_NUMBER",
        "NDC",
        "DATE_OF_SERVICE",
        "DAYS_SUPPLY",
        "D_PRESCRIBER_ID",
        "DATE_OF_BIRTH",
        "PATIENT_GENDER_CODE",
        "QUANTITY_DISPENSED",
        "D_DIAGNOSIS_CODE",
        "L_OTHER_NCPDP_PAYER_ID",
        "AMOUNT_OF_COPAY_OR_COINSURANCE",
        "PATIENT_PAY_AMOUNT",
        "GROSS_AMOUNT_DUE_SUBMITTED"
    )
    val ExtendedPharmacyColumns: Seq[String] = Seq(
        "FORIAN_PATIENT_ID",
        "CLAIM_NUMBER",
        "NDC",
        "DATE_OF_SERVICE",
        "DAYS_SUPPLY",
        "D_PRESCRIBER_ID",
        "DATE_OF_BIRTH",
        "PATIENT_GENDER_CODE",
        "PRESCRIPTION_OR_SERVICE_REFERENCE_NUMBER",
        "FILL_NUMBER",
        "DATE_AUTHORIZED",
        "TIME_AUTHORIZED",
        "L_TRANSACTION_RESPONSE_STATUS",
        "L_TRANSACTION_CODE",
        "PLAN_NAME",
        "QUANTITY_DISPENSED",
        "L_FINAL_STATUS",
        "D_DIAGNOSIS_CODE",
        "L_OTHER_NCPDP_PAYER_ID",
        "AMOUNT_OF_COPAY_OR_COINSURANCE",
        "PATIENT_PAY_AMOUNT",
        "GROSS_AMOUNT_DUE_SUBMITTED"
    )

    def pharmacyDf(rows: (String, String, String, Date, String, String, Date, String, String)*): DataFrame = {
        rows.toDF(PharmacyColumn: _*).withUniqueResponseCodeColumns()
    }

    def closedPharmacyDf(rows: (String, String, String, Date, String, String, Integer, String, String)*): DataFrame = {
        rows.toDF(PharmacyColumn: _*).withUniqueResponseCodeColumns()
    }

    val ClaimPatientColumns: Seq[String] = Seq(
        "FORIAN_PATIENT_ID",
        "CLAIM_NUMBER",
        "BIRTH_YEAR",
        "GENDER_CODE"
    )

    def claimPatientDf(rows: (String, String, Integer, String)*): DataFrame = rows.toDF(ClaimPatientColumns: _*)

    val ClaimHeaderColumns: Seq[String] = Seq(
        "FORIAN_PATIENT_ID",
        "CLAIM_NUMBER",
        "L_CLAIM_TYPE_CODE",
        "STATEMENT_FROM_DATE",
        "STATEMENT_TO_DATE",
        "L_TYPE_OF_BILL",
        "L_DISCHARGE_STATUS"
    )

    def claimHeaderDf(rows: (String, String, String, Date, Date, String, String)*): DataFrame = rows.toDF(ClaimHeaderColumns: _*)

    val claimProcedureSchema: StructType = StructType(
        Seq(
            StructField("FORIAN_PATIENT_ID", StringType),
            StructField("CLAIM_NUMBER", StringType),
            StructField("DATE_OF_SERVICE", DateType),
            StructField("D_PROCEDURE_CODE", StringType),
            StructField("PROCEDURE_SEQUENCE", IntegerType),
            StructField("SERVICE_FROM_DATE", DateType),
            StructField("SERVICE_TO_DATE", DateType),
            StructField("L_CLAIM_TYPE_CODE", StringType),
            StructField("L_PLACE_OF_SERVICE_CODE", StringType),
            StructField("D_PRODUCT_ID", StringType),
            StructField("D_RENDERING_PROVIDER_NPI", StringType),
            StructField("RECEIVED_DATE", DateType),
            StructField("DIAGNOSIS_POINTER_1", StringType),
            StructField("DIAGNOSIS_POINTER_2", StringType),
            StructField("DIAGNOSIS_POINTER_3", StringType),
            StructField("DIAGNOSIS_POINTER_4", StringType),
            StructField("DIAGNOSIS_POINTER_5", StringType),
            StructField("DIAGNOSIS_POINTER_6", StringType),
            StructField("DIAGNOSIS_POINTER_7", StringType),
            StructField("DIAGNOSIS_POINTER_8", StringType),
            StructField("DIAGNOSIS_POINTER_9", StringType),
            StructField("DIAGNOSIS_POINTER_10", StringType),
            StructField("DIAGNOSIS_POINTER_11", StringType),
            StructField("DIAGNOSIS_POINTER_12", StringType),
            StructField("LINE_CHARGE", DoubleType)
        )
    )

    def claimProcedureDataFrame(procedures: Seq[ClaimProcedure]): DataFrame = {
        val rows = procedures.map { claimProcedure =>
            Row(
                claimProcedure.forianPatientId,
                claimProcedure.claimNumber,
                claimProcedure.dateOfService,
                claimProcedure.dProcedureCode,
                claimProcedure.procedureSequence,
                claimProcedure.serviceFromDate,
                claimProcedure.serviceToDate,
                claimProcedure.lClaimTypeCode,
                claimProcedure.lPlaceOfServiceCode,
                claimProcedure.dProductId,
                claimProcedure.dRenderingProviderNpi,
                claimProcedure.receivedDate,
                claimProcedure.diagnosisPointer1,
                claimProcedure.diagnosisPointer2,
                claimProcedure.diagnosisPointer3,
                claimProcedure.diagnosisPointer4,
                claimProcedure.diagnosisPointer5,
                claimProcedure.diagnosisPointer6,
                claimProcedure.diagnosisPointer7,
                claimProcedure.diagnosisPointer8,
                claimProcedure.diagnosisPointer9,
                claimProcedure.diagnosisPointer10,
                claimProcedure.diagnosisPointer11,
                claimProcedure.diagnosisPointer12,
                claimProcedure.lineCharge.orNull
            )
        }.toList
        spark.createDataFrame(spark.sparkContext.parallelize(rows), claimProcedureSchema)
    }

    val ClaimDiagnosesColumns: Seq[String] = Seq(
        "FORIAN_PATIENT_ID",
        "CLAIM_NUMBER",
        "DATE_OF_SERVICE",
        "D_DIAGNOSIS_CODE",
        "L_DIAGNOSIS_SEQUENCE_CODE",
        "L_CLAIM_TYPE_CODE",
        "RECEIVED_DATE"
    )

    def claimDiagnosesDf(rows: (String, String, Date, String, String, String, Date)*): DataFrame = {
        rows.toDF(ClaimDiagnosesColumns: _*)
    }

    val ClaimProviderColumns: Seq[String] = Seq(
        "FORIAN_PATIENT_ID",
        "CLAIM_NUMBER",
        "STATE",
        "PROVIDER_ROLE",
        "ENTITY_TYPE_CODE",
        "PROVIDER_NPI",
        "ZIP"
    )

    def claimProviderDf(rows: (String, String, String, String, String, String, String)*): DataFrame = rows.toDF(ClaimProviderColumns: _*)
        .withColumn("RECEIVED_DATE", lit(date("2010-01-01")))
        .withColumn("PUBLISHED_DATE", lit(date("2010-01-01")))

    def claimProviderWithReceivedDateDf(rows: (String, String, String, String, String, String, String, Date, Date)*): DataFrame = rows.toDF(ClaimProviderColumns :+ "RECEIVED_DATE" :+ "PUBLISHED_DATE": _*)

    def npiDfDefaultVersions(rows: (String, String, String, String, String, String, String, String, String, Date)*): Map[String, DataFrame] = {
        Map(NPI_GENERATED_DATE -> rows.toDF(NpiColumns: _*))
    }

    def npiDfVersions(version: String,
                      rows: (String, String, String, String, String, String, String, String, String, Date)*
                     ): Map[String, DataFrame] = {
        Map(version -> rows.toDF(NpiColumns: _*))
    }

    def datasetEqMatcher[T](expected: Dataset[T]): Dataset[T] = {
        argThat(new DatasetMatcher(expected))
    }

    private val NpiColumns: Seq[String] = Seq(
        NpiCsv.Output.Columns.Id,
        NpiCsv.Output.Columns.State,
        NpiCsv.Output.Columns.City,
        NpiCsv.Output.Columns.Zip,
        NpiCsv.Output.Columns.Address1,
        NpiCsv.Output.Columns.Address2,
        NpiCsv.Output.Columns.SpecialityCode,
        NuccCsv.Output.Columns.PrescriberSpecialty,
        NuccCsv.Output.Columns.PrescriberDetailSpecialty,
        NpiCsv.Output.Columns.ModifiedAt
    )

    case class DfWithResponseCodeColumns(df: DataFrame) {
        def withUniqueResponseCodeColumns(version: Int = 1): DataFrame = {
            df
                .withColumn("L_FINAL_STATUS", lit("P"))
                .withColumn("L_TRANSACTION_RESPONSE_STATUS", lit("P"))
                .withColumn("L_TRANSACTION_CODE", lit("B1"))
                .withColumn("PLAN_NAME", lit("Payer A"))
                .withColumn("PRESCRIPTION_OR_SERVICE_REFERENCE_NUMBER", concat(lit(version + '-'), monotonically_increasing_id()))
                .withColumn("FILL_NUMBER", lit(1))
                .withColumn("DATE_AUTHORIZED", lit("2012-03-24"))
                .withColumn("TIME_AUTHORIZED", lit("000000"))
        }
    }

    implicit def dfWithResponseCodeColumns(df: DataFrame): DfWithResponseCodeColumns = DfWithResponseCodeColumns(df)

}

class DatasetMatcher[T](expected: Dataset[T]) extends ArgumentMatcher[Dataset[T]] {
    override def matches(argument: Dataset[T]): Boolean = {
        expected.collect().toSet == argument.collect().toSet
    }
}
