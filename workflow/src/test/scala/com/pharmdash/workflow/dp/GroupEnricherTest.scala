package com.pharmdash.workflow.dp

import com.pharmdash.workflow.Results
import com.pharmdash.workflow.dp.meta.processorannotations.Channel
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.{EVENTS, REFERENCE_DATA}
import com.pharmdash.workflow.model.{RefData, Subject}
import com.pharmdash.workflow.test.AbstractDataProcessorTestCase
import com.pharmdash.workflow.test.EventTestUtils.{date, event, events, subject}
import com.pharmdash.workflow.test.SubjectBuilder.{female, male}
import com.prospection.arch2.model.Category.{Diagnosis, Dispensing}
import com.pharmdash.workflow.model.StandardGender
import com.pharmdash.workflow.common.TechnicalModelEncoders._
import org.apache.spark.sql.Dataset
import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner

@RunWith(classOf[JUnitRunner])
class GroupEnricherTest extends AbstractDataProcessorTestCase {

    def createProcessor() = new GroupEnricher()

    test("all events should have appropriate code groups") {

        val classification = "MDV Item"

        val refData: Dataset[RefData] = spark.createDataset(Array(
            RefData("drugAAA", "event.code", Seq("A001"), classification),
            RefData("drugB00", "event.code", Seq("B001"), classification),
            RefData("drugB001", "event.code", Seq("B001"), classification),
            RefData("drugB20", "event.code", Seq("B002"), classification),
            RefData("drugB00", "event.code", Seq("B003"), classification)
        ))

        val subjects: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject("1", StandardGender.Male, 1980,
                events(
                    event(101, "B001", Dispensing, date("2006-01-05")),
                    event(102, "B002", Dispensing, date("2007-04-05"))
                )
            ),
            subject("2", StandardGender.Female, 1990,
                events(
                    event(103, "A001", Dispensing, date("2007-01-05")),
                    event(104, "B003", Dispensing, date("2008-03-05"))
                )
            )
        )

        val actualResult = createProcessor().doProcess(Map(
            EVENTS -> Results(Some(subjects)),
            REFERENCE_DATA -> Results(Some(refData))
        ))

        val expectedResult: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject("1", StandardGender.Male, 1980,
                events(
                    event(101, "B001", Dispensing, date("2006-01-05"), groups = Seq("drugB00", "drugB001")),
                    event(102, "B002", Dispensing, date("2007-04-05"), groups = Seq("drugB20"))
                )
            ),
            subject("2", StandardGender.Female, 1990,
                events(
                    event(103, "A001", Dispensing, date("2007-01-05"), groups = Seq("drugAAA")),
                    event(104, "B003", Dispensing, date("2008-03-05"), groups = Seq("drugB00"))
                )
            )
        )

        assertEqualData(expectedResult.toDF, actualResult(EVENTS).subjects.get.toDF)
    }

    test("all events should have appropriate code groups - grouping by classification") {

        val classificationItem = "MDV Item"
        val classificationDisease = "MDV Disease"
        val classificationLabResult = "MDV Lab Result"
        val classificationDischarge = "MDV Hospitalisation"

        val refData: Dataset[RefData] = spark.createDataset(Array(
            RefData("drugAAA", "event.code", Seq("A001"), classificationItem),
            RefData("drugB00", "event.code", Seq("B001"), classificationItem),
            RefData("drugB001", "event.code", Seq("B001"), classificationItem),
            RefData("drugB20", "event.code", Seq("B002"), classificationItem),

            RefData("drugB00", "event.code", Seq("B003"), classificationItem),
            RefData("drugB00", "event.code", Seq("B003"), classificationDisease),
            RefData("drugB00", "event.code", Seq("B003"), classificationLabResult),
            RefData("drugB00", "event.code", Seq("B003"), classificationDischarge)
        ))

        val subjects: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject("1", StandardGender.Male, 1980,
                events(
                    event(101, "B001", Dispensing, date("2006-01-05")),
                    event(102, "B002", Dispensing, date("2007-04-05"))
                )
            ),
            subject("2", StandardGender.Female, 1990,
                events(
                    event(103, "A001", Dispensing, date("2007-01-05")),
                    event(104, "B003", Dispensing, date("2008-03-05"), classification = classificationItem),
                    event(105, "B003", Dispensing, date("2008-03-05"), classification = classificationDisease),
                    event(106, "B003", Dispensing, date("2008-03-05"), classification = classificationLabResult),
                    event(107, "B003", Dispensing, date("2008-03-05"), classification = classificationDischarge)
                )
            )
        )

        val actualResult = createProcessor().doProcess(Map(
            EVENTS -> Results(Some(subjects)),
            REFERENCE_DATA -> Results(Some(refData))
        ))

        val expectedResult: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject("1", StandardGender.Male, 1980,
                events(
                    event(101, "B001", Dispensing, date("2006-01-05"), groups = Seq("drugB00", "drugB001")),
                    event(102, "B002", Dispensing, date("2007-04-05"), groups = Seq("drugB20"))
                )
            ),
            subject("2", StandardGender.Female, 1990,
                events(
                    event(103, "A001", Dispensing, date("2007-01-05"), groups = Seq("drugAAA")),

                    event(104, "B003", Dispensing, date("2008-03-05"), groups = Seq("drugB00")),
                    event(105, "B003", Dispensing, date("2008-03-05"), groups = Seq("drugB00"), classification = classificationDisease),
                    event(106, "B003", Dispensing, date("2008-03-05"), groups = Seq("drugB00"), classification = classificationLabResult),
                    event(107, "B003", Dispensing, date("2008-03-05"), groups = Seq("drugB00"), classification = classificationDischarge)
                )
            )
        )

        assertEqualData(expectedResult.toDF, actualResult(EVENTS).subjects.get.toDF)
    }

    test("can handle different kind of targets") {

        val classificationItem = "MDV Item"
        val classificationDisease = "MDV Disease"
        val classificationDischarge = "MDV Hospitalisation"

        val refData: Dataset[RefData] = spark.createDataset(Array(
            RefData("drugAAA", "event.code", Seq("A001"), classificationItem),
            RefData("drugB00", "event.code", Seq("B001"), classificationItem),
            RefData("drugB001", "event.code", Seq("B001"), classificationItem),
            RefData("drugB20", "event.code", Seq("B002"), classificationItem),
            RefData("drugB00", "event.code", Seq("B003"), classificationItem),
            RefData("diseaseProstateCancer", "event.tags.disease", Seq("prostate cancer"), classificationDisease),
            RefData("dead", "subject.tags.death", Seq("dischargeByDeath"), classificationDischarge)
        ))

        val subjects: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject("1", StandardGender.Male, 1980,
                events(
                    event(101, "B001", Dispensing, date("2006-01-05")),
                    event(102, "B002", Dispensing, date("2007-04-05"))
                ), Map(
                    "death" -> Seq("dischargeByDeath")
                )
            ),
            subject("2", StandardGender.Female, 1990,
                events(
                    event(103, "A001", Dispensing, date("2007-01-05")),
                    event(104, "PC001", Diagnosis, date("2007-01-05"), Map(
                        "disease" -> Seq("prostate cancer", "123")
                    ), classification = classificationDisease),
                    event(105, "B003", Dispensing, date("2008-03-05"))
                )
            )
        )

        val actualResult = createProcessor().doProcess(Map(
            EVENTS -> Results(Some(subjects)),
            REFERENCE_DATA -> Results(Some(refData))
        ))

        val expectedResult: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject("1", StandardGender.Male, 1980,
                events(
                    event(101, "B001", Dispensing, date("2006-01-05"), groups = Seq("drugB00", "drugB001")),
                    event(102, "B002", Dispensing, date("2007-04-05"), groups = Seq("drugB20"))
                ), Map(
                    "death" -> Seq("dischargeByDeath"),
                    "grouped_death" -> Seq("dead")
                )
            ),
            subject("2", StandardGender.Female, 1990,
                events(
                    event(103, "A001", Dispensing, date("2007-01-05"), groups = Seq("drugAAA")),
                    event(104, "PC001", Diagnosis, date("2007-01-05"), Map(
                        "disease" -> Seq("prostate cancer", "123"),
                        "grouped_disease" -> Seq("diseaseProstateCancer", GroupEnricher.OTHER_TAG)
                    ), classification = classificationDisease),
                    event(105, "B003", Dispensing, date("2008-03-05"), groups = Seq("drugB00"))
                )
            )
        )

        assertEqualData(expectedResult.toDF, actualResult(EVENTS).subjects.get.toDF)
    }

    test("subject tags are grouped aswell") {

        val refData: Dataset[RefData] = spark.createDataset(
            Seq(
                RefData("MALE", "subject.tags.gender", Seq(StandardGender.Male), "DOESNT MATTER"),
                RefData("FEMALE", "subject.tags.gender", Seq(StandardGender.Female), "DOESNT MATTER")
            )
        )

        val result = createProcessor().doProcess(Map(
            EVENTS -> Results(Some(
                createSubjectsWithFakeMetadata(
                    female(1111111187L)
                        born "1922-01-01"
                        withEndOfData "2018-01-30"
                        build,

                    male(1111111189L)
                        tagValues("gender", Seq(StandardGender.Male, "2"))
                        born "1922-01-01"
                        withEndOfData "2018-01-30"
                        build
                )
            )),
            REFERENCE_DATA -> Results(Some(refData))
        ))

        assertSubjectsEqual(
            subjects(
                female(1111111187L)
                    tag("grouped_gender", "FEMALE")
                    born "1922-01-01"
                    withEndOfData "2018-01-30"
                    build,

                male(1111111189L)
                    tagValues("gender", Seq(StandardGender.Male, "2"))
                    tagValues("grouped_gender", Seq("MALE", GroupEnricher.OTHER_TAG))
                    born "1922-01-01"
                    withEndOfData "2018-01-30"
                    build
            ), result(Channel.EVENTS).subjects.get)
    }
}

