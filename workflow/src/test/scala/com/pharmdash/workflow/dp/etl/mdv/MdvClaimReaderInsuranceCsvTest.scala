package com.pharmdash.workflow.dp.etl.mdv

import com.pharmdash.workflow.Results
import com.pharmdash.workflow.dp.etl.nz.moh.MohClaimReader
import com.pharmdash.workflow.dp.meta.processorannotations.Channel
import com.pharmdash.workflow.model.{StandardGender, Subject}
import com.pharmdash.workflow.test.EventBuilder._
import com.pharmdash.workflow.test.SubjectBuilder.subject
import com.pharmdash.workflow.test.{DataProcessorSpec, TempFiles}
import org.apache.commons.lang3.exception.ContextedRuntimeException
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.functions.col
import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner

import java.nio.file.{Path, Paths}

@RunWith(classOf[JUnitRunner])
class MdvClaimReaderInsuranceCsvTest extends DataProcessorSpec with TempFiles {

    val startOfData = "2000-01-01"
    val endOfData = "2020-01-01"

    describe("When generating technical model from RAW data") {

        it("Should read from nested folders") {
            withTemp(path => {
                // prepare
                val flatFiles = Paths.get(getClass.getResource("/sample/datalake/jp/mdv/hia/published=2022-09-01").toURI).toString
                val referenceFiles = Paths.get(getClass.getResource("/sample/supplemental/jp/mdv/published=2022-09-01").toURI).toString

                // perform
                val results = new MdvClaimReader(flatFiles.toString, referenceFiles.toString, "hia", startOfData, endOfData).doProcess(Map())
                val actualSubjects = results(Channel.EVENTS).subjects.get

                // verify
                actualSubjects.count() shouldBe 3

                val subjects1 = actualSubjects.where(col(Subject.IdColumn) === "A").collectAsList().get(0)

                subjects1.events(0).tags.get("region") should be(Some(List("Tohoku")))
                subjects1.events(0).tags.get("prefecture") should be(Some(List("3")))
                subjects1.events(0).classification should be("MDV HIA Disease")
                subjects1.events(0).category should be("diagnosis")

                subjects1.events(1).tags.get("region") should be(Some(List("Hokkaido")))
                subjects1.events(1).tags.get("prefecture") should be(Some(List("1")))
                subjects1.events(1).classification should be("MDV HIA Item")
                subjects1.events(1).category should be("dispensing")

                subjects1.events(2).tags.get("region") should be(Some(List("Tohoku")))
                subjects1.events(2).tags.get("prefecture") should be(Some(List("2")))
                subjects1.events(2).classification should be("MDV HIA Item")
                subjects1.events(2).category should be("dispensing")

                // should not have tags on subject
                subjects1.info.tags.get("region") should be(None)
                subjects1.info.tags.get("subRegion") should be(None)
            })
        }
    }
}
