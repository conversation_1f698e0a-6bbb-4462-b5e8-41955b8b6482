package com.pharmdash.workflow.dp.report

import com.pharmdash.workflow._
import com.pharmdash.workflow.common.BetterJson.formats
import com.pharmdash.workflow.dp.ICSDProcessor.{ICSDStruct, IcsdTag}
import com.pharmdash.workflow.dp.ProviderAttributeLayerProcessor.{Mechanisms, PalStructV2, PalTag}
import com.pharmdash.workflow.dp.ProviderFinderProcessor.ProviderFinderTag
import com.pharmdash.workflow.dp.meta.processorannotations.Channel
import com.pharmdash.workflow.dp.report.ProviderFinderReport.DontUseDaysSupplyForOnTreatment
import com.pharmdash.workflow.model.{Event, EventsMetadata, Subject}
import com.pharmdash.workflow.test.EventTestUtils.{date, event, subject}
import org.apache.spark.sql.{DataFrame, Dataset}
import org.json4s.jackson.Serialization
import org.mockito.Mockito.withSettings
import com.pharmdash.workflow.common.TechnicalModelEncoders.recordEncoder

import scala.collection.mutable.ArrayBuffer

class ProviderFinderReportTest extends BaseReportTest {

    def mock[T <: AnyRef](implicit manifest: Manifest[T]): T = super.mock[T](withSettings().serializable())

    test("should filter all the events that do not have interested tag when build the metrics") {
        val (mockContext, reportMetricsWriter, subjects) = setupMockEnvironment()
        val inputData = Map(Channel.EVENTS -> Results(Some(EventsMetadata.saveEventsMetadata(subjects, EventsMetadata(hasPalData = true)).as[Subject])))

        val unitUnderTest = createReportProcessor()

        unitUnderTest.doProcess(inputData)(mockContext)

        val metrics = captureMetrics(reportMetricsWriter)

        validateMetrics(metrics, mockContext)
    }

    private def validateMetrics(metrics: DataFrame, mockContext: WorkflowContext): Unit = {
        assert(metrics.count() == 1)
        assert(metrics.first().getAs[java.math.BigDecimal]("workflowId").longValue() == mockContext.workflowId, "Mismatch in workflowId")
        assert(metrics.first().getAs[java.math.BigDecimal]("reportId").longValue() == mockContext.nodeId, "Mismatch in reportId")
        assert(metrics.first().getAs[String]("reportName") == mockContext.getNode.getName, "Mismatch in reportName")
        assert(metrics.first().getAs[java.math.BigDecimal]("subjectCount").longValue() == 2, "Mismatch in subjectCount")
        assert(metrics.first().getAs[java.math.BigDecimal]("eventCount").longValue() == 3, "Mismatch in eventCount")
        assert(metrics.first().getAs[java.math.BigDecimal]("eventCodeCount").longValue() == 2, "Mismatch in eventCodeCount")
        assert(metrics.first().getAs[Seq[String]]("eventCodes") == Seq("A3", "A4"), "Mismatch in eventCodes")
    }

    private def createEventWithTag(id: Int, code: String, containsICSDTags: Boolean = true): Event = {
        val tagsMap = if (containsICSDTags) {
            Map(
                PalTag -> Seq(palValue(Mechanisms.Direct.toString, "Oncologist", "p1")),
                ProviderFinderTag -> Seq(Serialization.write(ICSDStruct(-1, "A")))
            )
        } else {
            Map(
                PalTag -> Seq(palValue(Mechanisms.Direct.toString, "Oncologist", "p1"))
            )
        }

        event(id, code = code,
            category = "category",
            date("2006-01-05"),
            tags = tagsMap,
            Seq("A", "D")
        )
    }

    def palValue(mechanism: String, specialty: String, providerId: String, additionalTags: (String, String)*) =
        PalStructV2(
            mechanism,
            specialty,
            providerId,
            additionalTags.toMap
        ).toJson

    def createSubjects(): Dataset[Subject] = {
        createSubjectsWithFakeMetadata(
            subject("1", "M", 1980,
                ArrayBuffer(
                    createEventWithTag(101, "A1", containsICSDTags = false)
                ),
                tags = Map("age" -> Seq("20")),
                endOfData = date("2019-01-01")),
            subject("2", "M", 1980,
                ArrayBuffer(
                    createEventWithTag(102, "A2", containsICSDTags = false),
                    createEventWithTag(103, "A3"),
                    createEventWithTag(104, "A4")
                ),
                tags = Map("age" -> Seq("20")),
                endOfData = date("2019-01-01")),
            subject("4", "M", 1980,
                ArrayBuffer(
                    createEventWithTag(105, "A4")
                ),
                tags = Map("age" -> Seq("20")),
                endOfData = date("2019-01-01"))
        )
    }

    private def createReportProcessor(): ProviderFinderReport = new ProviderFinderReport(
        maxOnTreatmentWindow = 1,
        onTreatmentWindowDefinition = DontUseDaysSupplyForOnTreatment,
        subjectTagToExport = List(),
        eventTagToExport = List(),
        referralNetworkEnabled = false,
        musicDiagramReportId = None
    )


}
