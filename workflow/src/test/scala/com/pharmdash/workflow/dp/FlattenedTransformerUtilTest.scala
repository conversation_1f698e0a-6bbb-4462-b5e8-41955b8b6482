package com.pharmdash.workflow.dp

import com.pharmdash.workflow.Results
import com.pharmdash.workflow.dp.etl.nz.moh.DataFrameHelper
import com.pharmdash.workflow.dp.meta.processorannotations.Channel
import com.pharmdash.workflow.dp.technicalmodelflattened.FlattenedTransformerUtil
import com.pharmdash.workflow.model.{Event, Subject, SubjectInfo}
import com.pharmdash.workflow.test.AbstractDataProcessorTestCase
import com.prospection.arch2.model.Category
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.catalyst.expressions.GenericRowWithSchema
import org.apache.spark.sql.types._
import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner

import java.sql.Date
import java.time.LocalDate


@RunWith(classOf[JUnitRunner])
class FlattenedTransformerUtilTest extends AbstractDataProcessorTestCase {

    import spark.implicits._


    test("should transform events in subjects being flattened") {

        val event1 = Event(
            id = 1L,
            date = Date.valueOf(LocalDate.of(1990, 10, 1)),
            code = "Diag1",
            category = Category.Diagnosis,
            amount = Some(1.0),
            value = Some(1.0),
            unit = "unit1",
            cost = Some(1.0),
            groups = Seq("group1", "group2"),
            tags = Map("tag1" -> Seq("tag1a", "tag1b"),
                "tag2" -> Seq("tag2a", "tag2b")),
            classification = "Cardiologist"
        )

        val event2 = Event(
            id = 2L,
            date = Date.valueOf(LocalDate.of(2021, 11, 1)),
            code = "Diag1",
            category = Category.Diagnosis,
            amount = Some(1.0),
            value = Some(1.0),
            unit = "unit1",
            cost = Some(1.0),
            groups = Seq("group2", "group3"),
            tags = Map("tag2" -> Seq("tag2a", "tag2b"),
                "tag3" -> Seq("tag3a", "tag3b")),
            classification = "Cardiologist"
        )

        val subj1 = Subject(
            "39821d2c-0921-4180-819c-d0c25e82d4f5",
            SubjectInfo(
                dateOfBirth = Date.valueOf(LocalDate.of(1980, 5, 10)),
                dateOfDeath = Date.valueOf(LocalDate.of(1999, 5, 10)),
                tags = Map("tag1" -> Seq("a", "b"), "tag2" -> Seq("c", "d"), SubjectInfo.GenderTag -> Seq("M"))
            ),
            Seq(
                event1,
                event2
            )
        )

        val schema = StructType(
            Seq(
                StructField("id", LongType, nullable = true),
                StructField("subjectId", StringType, nullable = true),
                StructField("date", DateType, nullable = true),
                StructField("code", StringType, nullable = true),
                StructField("category", StringType, nullable = true),
                StructField("amount", DoubleType, nullable = true),
                StructField("value", DoubleType, nullable = true),
                StructField("unit", StringType, nullable = true),
                StructField("cost", DoubleType, nullable = true),
                StructField("groups", ArrayType(StringType), nullable = true),
                StructField("tag1", ArrayType(StringType), nullable = true),
                StructField("tag2", ArrayType(StringType), nullable = true),
                StructField("tag3", ArrayType(StringType), nullable = true)
            )
        )

        val expectedEvent1 = new GenericRowWithSchema(
            Array(event1.id, subj1.id, event1.date, event1.code, event1.category, 1.0, 1.0, event1.unit, 1.0, Seq("group1", "group2"), Seq("tag1a", "tag1b"), Seq("tag2a", "tag2b"), Seq()),
            schema
        )

        val expectedEvent2 = new GenericRowWithSchema(
            Array(event2.id, subj1.id, event2.date, event2.code, event2.category, 1.0, 1.0, event2.unit, 1.0, Seq("group2", "group3"), Seq(), Seq("tag2a", "tag2b"), Seq("tag3a", "tag3b")),
            schema
        )

        val expectedList = List(expectedEvent1, expectedEvent2)

        val inputData: Map[String, Results] = Map(
            Channel.EVENTS -> Results( Option(Seq(subj1).toDS()))
        )
        val (_, events) = FlattenedTransformerUtil.getSubjectsAndEventsDatasets(inputData)

        val dfHelper = new DataFrameHelper(context)
        val expectedDf: DataFrame = dfHelper.toDF(expectedList, schema)

        assertEqualData(expectedDf, events)
    }


    test("should transform subjects with all info data and tags being flattened") {
        // Given
        val subj1 = Subject(
            "test-1",
            SubjectInfo(
                dateOfBirth = Date.valueOf(LocalDate.of(1980, 5, 10)),
                dateOfDeath = Date.valueOf(LocalDate.of(1999, 5, 10)),
                calculatedDateOfDeath = Date.valueOf(LocalDate.of(1999, 5, 10)),
                startOfData = Date.valueOf(LocalDate.of(1990, 5, 10)),
                endOfData = Date.valueOf(LocalDate.of(2000, 5, 10)),
                startOfDataset = Date.valueOf(LocalDate.of(1990, 5, 10)),
                endOfDataset = Date.valueOf(LocalDate.of(2000, 5, 10)),
                tags = Map("tag1" -> Seq("a", "b"), "tag2" -> Seq("c", "d"), SubjectInfo.GenderTag -> Seq("M"))
            ),
            Seq()
        )
        val subj2 = Subject(
            "test-2",
            SubjectInfo(
                dateOfBirth = Date.valueOf(LocalDate.of(1981, 6, 11)),
                startOfData = Date.valueOf(LocalDate.of(1990, 5, 10)),
                endOfData = Date.valueOf(LocalDate.of(2000, 3, 10)),
                startOfDataset = Date.valueOf(LocalDate.of(1993, 2, 1)),
                endOfDataset = Date.valueOf(LocalDate.of(2000, 3, 1)),
                tags = Map("tag3" -> Seq("e"), SubjectInfo.GenderTag -> Seq("F"))
            ),
            Seq()
        )

        val dsList = List(subj1, subj2)

        // When
        val (subjects, _) = FlattenedTransformerUtil.getSubjectsAndEventsDatasets(Map(Channel.EVENTS -> Results(Option(dsList.toDS()))))

        // Then
        val schema = StructType(
            Seq(
                StructField("id", StringType, nullable = true),
                StructField("dateOfBirth", DateType, nullable = true),
                StructField("dateOfDeath", DateType, nullable = true),
                StructField("calculatedDateOfDeath", DateType, nullable = true),
                StructField("startOfData", DateType, nullable = true),
                StructField("endOfData", DateType, nullable = true),
                StructField("startOfDataset", DateType, nullable = true),
                StructField("endOfDataset", DateType, nullable = true),
                StructField("tag1", ArrayType(StringType), nullable = true),
                StructField("tag2", ArrayType(StringType), nullable = true),
                StructField("gender", ArrayType(StringType), nullable = true),
                StructField("tag3", ArrayType(StringType), nullable = true)
            )
        )

        val expectedList = List(
            new GenericRowWithSchema(
                Array(
                    subj1.id,
                    Date.valueOf(LocalDate.of(1980, 5, 10)),
                    Date.valueOf(LocalDate.of(1999, 5, 10)),
                    Date.valueOf(LocalDate.of(1999, 5, 10)),
                    Date.valueOf(LocalDate.of(1990, 5, 10)),
                    Date.valueOf(LocalDate.of(2000, 5, 10)),
                    Date.valueOf(LocalDate.of(1990, 5, 10)),
                    Date.valueOf(LocalDate.of(2000, 5, 10)),
                    Seq("a", "b"),
                    Seq("c", "d"),
                    Seq("M"),
                    Seq()
                ),
                schema
            ),
            new GenericRowWithSchema(
                Array(
                    subj2.id,
                    Date.valueOf(LocalDate.of(1981, 6, 11)),
                    null,
                    null,
                    Date.valueOf(LocalDate.of(1990, 5, 10)),
                    Date.valueOf(LocalDate.of(2000, 3, 10)),
                    Date.valueOf(LocalDate.of(1993, 2, 1)),
                    Date.valueOf(LocalDate.of(2000, 3, 1)),
                    Seq(),
                    Seq(),
                    Seq("F"),
                    Seq("e")

                ),
                schema
            )
        )

        val dfHelper = new DataFrameHelper(context)
        val expectedDf: DataFrame = dfHelper.toDF(expectedList, schema)
        val transformedDf: DataFrame = subjects.toDF()
        assertEqualData(expectedDf, transformedDf)
    }

    test("should transform events in subjects that missing non required columns, group and tags being flattened") {
        val event1 = Event(
            id = 1L,
            date = Date.valueOf(LocalDate.of(1990, 10, 1)),
            code = "Diag1",
            category = Category.Diagnosis,
            classification = "Cardiologist"
        )

        val subj1 = Subject(
            "39821d2c-0921-4180-819c-d0c25e82d4f5",
            SubjectInfo(
                dateOfBirth = Date.valueOf(LocalDate.of(1980, 5, 10)),
                dateOfDeath = Date.valueOf(LocalDate.of(1999, 5, 10)),
                tags = Map("tag1" -> Seq("a", "b"), "tag2" -> Seq("c", "d"), SubjectInfo.GenderTag -> Seq("M"))
            ),
            Seq(
                event1
            )
        )

        val schema = StructType(
            Seq(
                StructField("id", LongType, nullable = true),
                StructField("subjectId", StringType, nullable = true),
                StructField("date", DateType, nullable = true),
                StructField("code", StringType, nullable = true),
                StructField("category", StringType, nullable = true),
                StructField("amount", DoubleType, nullable = true),
                StructField("value", DoubleType, nullable = true),
                StructField("unit", StringType, nullable = true),
                StructField("cost", DoubleType, nullable = true),
                StructField("groups", ArrayType(StringType), nullable = true)
            )
        )

        val expectedEvent1 = new GenericRowWithSchema(
            Array(event1.id, subj1.id, event1.date, event1.code, event1.category, null, null, null, null, Seq()),
            schema
        )

        val expectedList = List(expectedEvent1)

        val dsList = List(subj1)
        val (_, events) = FlattenedTransformerUtil.getSubjectsAndEventsDatasets(Map(Channel.EVENTS -> Results(Option(dsList.toDS()))))

        val dfHelper = new DataFrameHelper(context)
        val expectedDf: DataFrame = dfHelper.toDF(expectedList, schema)
        val transformedDf: DataFrame = events.toDF()

        assertEqualData(expectedDf, transformedDf)
    }

    test("should transform subjects that missing non-required columns and tags being flattened") {
        // Given
        val subj1 = Subject(
            "test-1",
            SubjectInfo(
                calculatedDateOfDeath = Date.valueOf(LocalDate.of(1999, 5, 10)),
                startOfData = Date.valueOf(LocalDate.of(1990, 5, 10)),
                endOfData = Date.valueOf(LocalDate.of(2000, 5, 10)),
                startOfDataset = Date.valueOf(LocalDate.of(1990, 5, 10)),
                endOfDataset = Date.valueOf(LocalDate.of(2000, 5, 10))
            ),
            Seq()
        )

        val dsList = List(subj1)

        // When
        val (subjects, _) = FlattenedTransformerUtil.getSubjectsAndEventsDatasets(Map(Channel.EVENTS -> Results(Option(dsList.toDS()))))

        // Then
        val schema = StructType(
            Seq(
                StructField("id", StringType, nullable = true),
                StructField("dateOfBirth", DateType, nullable = true),
                StructField("dateOfDeath", DateType, nullable = true),
                StructField("calculatedDateOfDeath", DateType, nullable = true),
                StructField("startOfData", DateType, nullable = true),
                StructField("endOfData", DateType, nullable = true),
                StructField("startOfDataset", DateType, nullable = true),
                StructField("endOfDataset", DateType, nullable = true)
            )
        )

        val expectedList = List(
            new GenericRowWithSchema(
                Array(
                    subj1.id,
                    null,
                    null,
                    Date.valueOf(LocalDate.of(1999, 5, 10)),
                    Date.valueOf(LocalDate.of(1990, 5, 10)),
                    Date.valueOf(LocalDate.of(2000, 5, 10)),
                    Date.valueOf(LocalDate.of(1990, 5, 10)),
                    Date.valueOf(LocalDate.of(2000, 5, 10))
                ),
                schema
            )
        )

        val dfHelper = new DataFrameHelper(context)
        val expectedDf: DataFrame = dfHelper.toDF(expectedList, schema)
        val transformedDf: DataFrame = subjects.toDF()
        assertEqualData(expectedDf, transformedDf)
    }

}
