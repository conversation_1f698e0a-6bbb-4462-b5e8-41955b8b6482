package com.pharmdash.workflow.dp.etl.nz.moh

import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner;

@RunWith(classOf[JUnitRunner])
class CancerSourceTest extends MohClaimReaderTestBase {

    import spark.implicits._

    describe("When generating Cancer events") {

        it("Values should be copied to intermediate event") {
            val helper = new DataFrameHelper(context)

            val inputData = Seq(CancerSource("1", "Jan-00", "site", "30/1/2001", 55, "Ethnic1", "cancer_location1", "facility_cancer1", "morph1", "Basis1", "Grade1", "Extent1", "Laterality1")).toDF()

            val expectedData = Seq(EventIntermediateSchema(
                subjectId = "1",
                classification = "MoH Cancer",
                dateOfBirth = helper.date(2000, 1, 1),
                code = "site",
                date = helper.date(2001, 1, 30),
                age = 55,
                ethnicity = "Ethnic1",
                location = "cancer_location1",
                facility = "facility_cancer1",
                morphology = "morph1",
                basisOfDiagnosis = "Basis1",
                tumourGrade = "Grade1",
                extentOfDisease = "Extent1",
                laterality = "Laterality1"
            )).toDF()

            val actualData = CancerSource.standard(inputData)

            // see https://stackoverflow.com/a/43880985 for a complete example - we should use spark-fast-tests
            actualData.collect() shouldEqual expectedData.collect()
            //            actualData.collect().sameElements(expectedData.collect()) should be (true)

        }
    }
}
