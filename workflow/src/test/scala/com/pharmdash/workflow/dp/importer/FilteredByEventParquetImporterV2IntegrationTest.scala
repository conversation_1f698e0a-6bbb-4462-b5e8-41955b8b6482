package com.pharmdash.workflow.dp.importer

import com.pharmdash.workflow.Results
import com.pharmdash.workflow.common.TechnicalModelEncoders._
import com.pharmdash.workflow.dp.DataProcessor
import com.pharmdash.workflow.dp.config.DatasetPartition
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.{EVENTS, REFERENCE_DATA}
import com.pharmdash.workflow.model.{RefData, Subject}
import com.pharmdash.workflow.test.EventBuilder.dispensing
import com.pharmdash.workflow.test.EventTestUtils.date
import com.pharmdash.workflow.test.{AbstractDataProcessorTestCase, SubjectBuilder}
import com.prospection.arch2.model.Category.{Dispensing, Mortality, Pathology}
import com.prospection.arch2.model.FieldConstants
import com.prospection.arch2.util.WarehouseConfiguration
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.exception.ContextedRuntimeException
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.functions.explode
import org.junit.runner.RunWith
import org.scalatest.Assertion
import org.scalatestplus.junit.JUnitRunner

import java.nio.file.Paths

@RunWith(classOf[JUnitRunner])
class FilteredByEventParquetImporterV2IntegrationTest extends AbstractDataProcessorTestCase with WarehouseConfiguration with ImporterTestUtil {

    private val classification = "MDV Item"

    test("should filter by a single event code") {
        setupTestAndCleanup(filterProcessor => {
            val codes = spark.createDataset(Array(
                RefData("drugB00", "event.code", Seq("620006542"), classification),
                RefData("drugB01www", "event.code", Seq("620006542"), classification),
                RefData("drugB20", "event.tags.somethingElse", Seq("AS1ZZXC541"), classification)
            ))

            val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

            distinctEventCodes(result) should contain theSameElementsAs Seq("620006542")
            distinctEventCategories(result) should contain theSameElementsAs Seq(Dispensing)

            result(EVENTS).dataset.get.count() shouldBe 1
        })
    }

    test("should filter by a single event code and keep all events") {
        setupTestAndCleanup(filterProcessor => {
            val codes = spark.createDataset(Array(
                RefData("drugB00", "event.code", Seq("620006542"), classification),
                RefData("drugB001", "event.code", Seq("620006542"), classification),
                RefData("drugB20", "event.tags.somethingElse", Seq("AS1ZZXC541"), classification)
            ))

            val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

            distinctEventCodes(result) should contain theSameElementsAs Seq("620006542", "B002")
            distinctEventCategories(result) should contain theSameElementsAs Seq(Dispensing, Pathology)

            result(EVENTS).dataset.get.count() shouldBe 1
        }, includeAll = true)
    }

    test("should filter by multiple event codes") {
        setupTestAndCleanup(filterProcessor => {
            val codes = spark.createDataset(Array(
                RefData("drugB00", "event.code", Seq("620006542"), classification),
                RefData("drugB001", "event.code", Seq("620006542"), classification),
                RefData("drugB20", "event.code", Seq("3B070000000000000"), classification),
                RefData(FieldConstants.DeathCode, "event.code", Seq(FieldConstants.DeathCode), classification)
            ))

            val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

            distinctEventCodes(result) should contain theSameElementsAs Seq("3B070000000000000", "620006542", FieldConstants.DeathCode)
            distinctEventCategories(result) should contain theSameElementsAs Seq(Dispensing, Pathology, Mortality)

            result(EVENTS).dataset.get.count() shouldBe 2
        })
    }

    test("should return unique subjects for duplicate events") {
        setupTestAndCleanup(filterProcessor => {
            val codes = spark.createDataset(Array(
                RefData("drugB00", "event.code", Seq("A009"), classification),
                RefData("drugB001", "event.code", Seq("A009"), classification)
            ))

            val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

            distinctEventCodes(result) should contain theSameElementsAs Seq("A009")
            distinctEventCategories(result) should contain theSameElementsAs Seq(Dispensing)

            val subjects = result(EVENTS).dataset.get.as[Subject]
            subjects.count() shouldBe 1
            subjects.flatMap(subject => subject.events).count() shouldBe 2
        })
    }

    test("Importing distinctEventCodes-enabled dataset will throw an error so admin users can replace v2 node with v3 node.") {
        val dataset = createSubjectsWithFakeMetadataAndEnableSubjectDistinctEventCodes(
            SubjectBuilder.subject(10043789)
                .withDistinctEventCodesByClassification(Map(classification -> Set("A009", "A010")))
                .having(
                    dispensing(1)
                        on date("2015-08-04")
                        ofCode "A009"
                        classifiedAs classification
                )
        )

        val tempSubjects = writeTempSubjects(dataset)

        val processor = createProcessor(tempSubjects, includeAll = false, skipGroupEnrichment = false)

        val caught = intercept[ContextedRuntimeException] {
            val result = processor.doProcess(Map((REFERENCE_DATA, Results(Some(spark.createDataset(Array(
                RefData("drugB000", "event.code", Seq("A009"), classification)
            )))))))

            result(EVENTS).subjects.get.collect()
        }

        caught.getMessage should include("Replace your Dataset Import V2 node with the V3. Your dataset contains subject-level distinctEventCodes that can be handled by the V3 and onwards.")

        FileUtils.deleteDirectory(Paths.get(tempSubjects.getSourcePath).toFile)
    }

    test("should be able to skip group enrichment") {
        val dataset = createSubjectsWithFakeMetadata(
            SubjectBuilder.subject(10043789)
                .withDistinctEventCodesByClassification(Map(classification -> Set("A009", "A010")))
                .having(
                    dispensing(1)
                        on date("2015-08-04")
                        ofCode "A009"
                        classifiedAs classification,
                    dispensing(2)
                        on date("2016-02-28")
                        ofCode "A010"
                        classifiedAs classification
                )
        )
        setupTestAndCleanup(
            filterProcessor => {
                val codes = spark.createDataset(Array(
                    RefData("drugB000", "event.code", Seq("A009"), classification),
                    RefData("drugB001", "event.code", Seq("A010"), classification)
                ))

                val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

                val subjects = result(EVENTS).subjects.get.collect()
                subjects(0).events.map(_.groups) should equal(Seq(Seq.empty[String], Seq.empty[String]))
            },
            subjects = dataset,
            skipGroupEnrichment = true
        )
    }

    private def setupTestAndCleanup(
                                       test: DataProcessor => Assertion,
                                       subjects: Dataset[Subject] = sampleSubjects,
                                       includeAll: Boolean = false,
                                       skipGroupEnrichment: Boolean = false
                                   ) = {
        val dataset = writeTempSubjects(subjects)
        val filterProcessor = createProcessor(dataset, includeAll, skipGroupEnrichment)

        test.apply(filterProcessor)

        // DON'T FORGET TO CLEAN UP.
        FileUtils.deleteDirectory(Paths.get(dataset.getSourcePath).toFile)
    }

    def distinctEventCodes(result: Map[String, Results]): Array[String] = {
        import spark.implicits._
        result(EVENTS).subjects.get
            .select(explode($"events") as EVENTS).select("events.code")
            .distinct()
            .orderBy("events.code")
            .collect()
            .map(r => r(0).asInstanceOf[String])
    }

    def distinctEventCategories(result: Map[String, Results]): Array[String] = {
        import spark.implicits._
        result(EVENTS).subjects.get
            .select(explode($"events") as EVENTS).select("events.category")
            .distinct()
            .orderBy("events.category")
            .collect()
            .map(r => r(0).asInstanceOf[String])
    }

    def createProcessor(
                           dataset: DatasetPartition,
                           includeAll: Boolean,
                           skipGroupEnrichment: Boolean
                       ): DataProcessor =
        new FilteredByEventParquetImporterV2(dataset, includeAll, skipGroupEnrichment)
}
