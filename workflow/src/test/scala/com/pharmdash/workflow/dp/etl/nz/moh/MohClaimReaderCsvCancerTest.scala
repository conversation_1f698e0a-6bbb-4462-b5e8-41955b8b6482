package com.pharmdash.workflow.dp.etl.nz.moh

import com.pharmdash.workflow.dp.meta.processorannotations.Channel
import com.pharmdash.workflow.model.Subject
import org.apache.spark.sql.{DataFrame, Dataset}
import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner

import java.nio.file.{Path, Paths}

// Useful resources here:
//   https://sparkbyexamples.com/spark/spark-write-dataframe-to-csv-file/

// Background
// https://prospection.atlassian.net/wiki/spaces/PDI/pages/2269282401/MOH+Data+Model+-+Gastro+Crohn+s+Disease+-+K50+51

/**/
@RunWith(classOf[JUnitRunner])
class MohClaimReaderCsvCancerTest extends MohClaimReaderTestBase {

    val startOfData = "2000-01-01"
    val endOfData = "2023-01-01"

    describe("When generating technical model from RAW data") {

        it("Should read from nested folders") {
            withTemp(path => {
                // prepare
                val flatFiles = Paths.get(getClass.getResource("/sample/datalake/nz/moh_cancer/published=2022-09-01").toURI).toString

                // perform
                val results = new MohClaimReader(startOfData, endOfData, flatFiles.toString).doProcess(Map())
                val actualSubjects = results(Channel.EVENTS).subjects.get

                // verify
                actualSubjects.count() shouldBe 6

                actualSubjects.collect().foreach(subject => {

                    subject.dateOfBirth should not be null

                    subject.events.foreach(event => {
                        if (event.date == null) {
                            println(event)
                        }
                        event.date should not be null
                    })
                })
            })
        }
    }


    def processData(path: Path, startOfDataset: String = startOfData, endOfDataset: String = endOfData): Dataset[Subject] = {

        val results = new MohClaimReader(startOfDataset, endOfDataset, path.toString).doProcess(Map())
        results(Channel.EVENTS).subjects.get

    }

    private def saveTestData(path: Path, fileName: String, data: DataFrame) = {
        writeCsv(data, s"$fileName", path, separator = "\t")
    }

}

