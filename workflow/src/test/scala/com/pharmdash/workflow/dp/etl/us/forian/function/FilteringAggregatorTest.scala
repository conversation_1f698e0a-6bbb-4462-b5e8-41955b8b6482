package com.pharmdash.workflow.dp.etl.us.forian.function

import com.pharmdash.workflow.dp.etl.us.forian.ForianClaimReaderTestBase
import org.apache.spark.sql.functions.{col, udaf}
import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner

@RunWith(classOf[JUnitRunner])
class FilteringAggregatorTest extends ForianClaimReaderTestBase {

    import spark.implicits._

    describe("FilteringCollector") {
        it("should collect values into array, removing empty strings and nulls and excluding values in except list") {
            val collector = udaf(new FilteringAggregator(List("1", "2")))

            val df = Seq(
                "1", "2", "3", "4", "", null
            ).toDF("value")
            val results = df.agg(collector(col("value")).as("result"))

            val expectedDf = Seq(
                List("3", "4")
            ).toDF("result")

            assertEqualData(expectedDf, results)
        }

        it("should work with empty except list") {
            val collector = udaf(new FilteringAggregator())

            val df = Seq(
                "1", "2", "3", "4", "", null
            ).toDF("value")
            val results = df.agg(collector(col("value")).as("result"))

            val expectedDf = Seq(
                List("1", "2", "3", "4")
            ).toDF("result")

            assertEqualData(expectedDf, results)
        }
    }

}
