package com.pharmdash.workflow.dp.etl

import com.pharmdash.workflow.dp.etl.EtlUtils.MostCommonValueAgg
import com.pharmdash.workflow.test.{DataProcessorSpec, TempFiles}
import org.apache.spark.sql.types.StringType
import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner

@RunWith(classOf[JUnitRunner])
class EtlUtilsTest extends DataProcessorSpec with TempFiles {

    import spark.implicits._

    describe("MostCommonValueAgg") {
        it("should be able to return the most common value in given column") {
            val df = Seq(
                ("1", "M", "2020-01-01"),
                ("2", "M", "2020-01-02"),
                ("3", "M", "2020-01-03"),
                ("4", "F", "2020-01-04"),
                ("5", "F", "2020-01-05")
            ).toDF("id", "gender", "date")

            val mode = new MostCommonValueAgg[String](StringType)

            val result = df.agg(mode('gender).as("gender")).collectAsList()

            result should have size 1
            result.get(0).getString(0) should equal("M")
        }

        it("should ignore null value by default") {
            val df = Seq(
                ("1", "M", "2020-01-01"),
                ("2", "M", "2020-01-02"),
                ("3", null, "2020-01-03"),
                ("4", null, "2020-01-04"),
                ("5", null, "2020-01-05")
            ).toDF("id", "gender", "date")

            val mode = new MostCommonValueAgg[String](StringType)

            val result = df.agg(mode('gender).as("gender")).collectAsList()

            result should have size 1
            result.get(0).getString(0) should equal("M")
        }

        it("should ignore custom ignoring values") {
            val df = Seq(
                ("1", "M", "2020-01-01"),
                ("2", "M", "2020-01-02"),
                ("3", "F", "2020-01-03"),
                ("4", "U", "2020-01-04"),
                ("5", "U", "2020-01-04"),
                ("6", "U", "2020-01-06")
            ).toDF("id", "gender", "date")

            val mode = new MostCommonValueAgg[String](StringType, Seq("U"))

            val result = df.agg(mode('gender).as("gender")).collectAsList()

            result should have size 1
            result.get(0).getString(0) should equal("M")
        }

        it("should return the most common ignoring value if non-ignoring value does not exist") {
            val df = Seq(
                ("1", null, "2020-01-01"),
                ("2", null, "2020-01-02"),
                ("3", "", "2020-01-03"),
                ("4", "U", "2020-01-04"),
                ("5", "U", "2020-01-04"),
                ("6", "U", "2020-01-06")
            ).toDF("id", "gender", "date")

            val mode = new MostCommonValueAgg[String](StringType, Seq("U", ""))

            val result = df.agg(mode('gender).as("gender")).collectAsList()

            result should have size 1
            result.get(0).getString(0) should equal("U")
        }
    }

    describe("Replace latest date") {
        it("Should return latest date") {
            withTemp(path => {

                val df1 = Seq(("1")).toDF("id")
                writeParquets(df1, "test/generated=2023-02-01", path)

                val df2 = Seq(("2")).toDF("id")
                writeParquets(df2, "test/generated=2023-02-02", path)

                val result = EtlUtils.replaceLatestDateIfExists(path + "/test/generated=${latest}")(spark)

                result should endWith("test/generated=2023-02-02/")
            })
        }

        it("Should return latest date when end path with /") {
            withTemp(path => {

                val df1 = Seq(("1")).toDF("id")
                writeParquets(df1, "test/generated=2023-02-01", path)

                val df2 = Seq(("2")).toDF("id")
                writeParquets(df2, "test/generated=2023-02-02", path)

                val result = EtlUtils.replaceLatestDateIfExists(path + "/test/generated=${latest}/")(spark)

                result should endWith("test/generated=2023-02-02/")
            })
        }

        it("Should not return latest date path") {
            withTemp(path => {

                val result = EtlUtils.replaceLatestDateIfExists(path + "/test/generated=2023-01-01/")(spark)

                result should endWith("test/generated=2023-01-01/")
            })
        }
    }
}
