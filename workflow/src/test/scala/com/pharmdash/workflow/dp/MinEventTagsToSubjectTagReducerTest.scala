package com.pharmdash.workflow.dp

import com.pharmdash.workflow.Results
import com.pharmdash.workflow.dp.meta.processorannotations.Channel
import com.pharmdash.workflow.model.Subject
import com.pharmdash.workflow.test.AbstractDataProcessorTestCase
import com.pharmdash.workflow.test.EventBuilder.intervention
import com.pharmdash.workflow.test.SubjectBuilder.female
import org.apache.spark.sql.Dataset
import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner

@RunWith(classOf[JUnitRunner])
class MinEventTagsToSubjectTagReducerTest extends AbstractDataProcessorTestCase {

    test("finds min event tag and adds to subject tag correctly") {
        val subjects: Dataset[Subject] = createSubjectsWithFakeMetadata(
            female(1) having(
                intervention(1)
                    withTags
                    (
                        "distance" -> "2"
                        )
                    classifiedAs "",
                intervention(2)
                    withTags
                    (
                        "distance" -> "10"
                        )
                    classifiedAs "",
                intervention(3)
                    classifiedAs ""
            )
        )

        val processor = new MinEventTagsToSubjectTagReducer("distance")
        val results = processor.doProcess(Map(Channel.EVENTS -> Results(Some(subjects))))

        assertSubjectsEqual(
            createSubjectsWithFakeMetadata(
                female(1) tag "distance" -> "2" having(
                    intervention(1)
                        withTags
                        (
                            "distance" -> "2"
                            )
                        classifiedAs "",
                    intervention(2)
                        withTags
                        (
                            "distance" -> "10"
                            )
                        classifiedAs "",
                    intervention(3)
                        classifiedAs ""
                )
            ),
            results(Channel.EVENTS).subjects.get
        )
    }

    test("no subject tag if no event tags") {
        val subjects: Dataset[Subject] = createSubjectsWithFakeMetadata(
            female(1) having (
                intervention(1)
                    classifiedAs ""
                )
        )

        val processor = new MinEventTagsToSubjectTagReducer("distance")
        val results = processor.doProcess(Map(Channel.EVENTS -> Results(Some(subjects))))

        assertSubjectsEqual(
            createSubjectsWithFakeMetadata(
                female(1) having (
                    intervention(1)
                        classifiedAs ""
                    )
            ),
            results(Channel.EVENTS).subjects.get
        )
    }

}
