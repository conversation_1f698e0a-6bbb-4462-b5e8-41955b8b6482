package com.pharmdash.workflow.dp.regimen

import com.pharmdash.workflow.Results
import com.pharmdash.workflow.dp.regimen.MonoComboBaseTagger._
import com.pharmdash.workflow.dp.regimen.RegimenProcessor.RegimenTag
import com.pharmdash.workflow.model.Subject
import com.pharmdash.workflow.test.DataProcessorSpec
import com.pharmdash.workflow.test.EventBuilder.dispensing
import com.pharmdash.workflow.test.SubjectBuilder.male
import org.apache.spark.sql.Dataset
import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner

@RunWith(classOf[JUnitRunner])
class MonoComboBaseTaggerTest extends DataProcessorSpec {

    describe("With Combo") {

        it("should tag events with no regimen tag") {
            def processor = new MonoComboBaseTagger("a")

            val input: Dataset[Subject] = createSubjectsWithFakeMetadata(
                male(1L) born "1980-01-01"
                    having (
                    dispensing(7L) on "2006-05-01" ofCode "D1" withGroup "B" build
                    )
            )

            val result = processor.doProcess(Map(("Events", Results(Some(input)))))
            val actual = result("Events").subjects.get

            assertSubjectsEqual(
                subjects(
                    male(1L) born "1980-01-01"
                        having (
                        dispensing(7L) on "2006-05-01" ofCode "D1" withGroup "B" withTags (MonoComboTag -> NoRegimenTag) build
                        )
                )
                , actual)
        }

        it("should tag mono regimens with only the mono tag") {
            def processor = new MonoComboBaseTagger("a")

            val input: Dataset[Subject] = createSubjectsWithFakeMetadata(
                male(1L) born "1980-01-01"
                    having(
                    dispensing(1L) on "2006-05-01" ofCode "A1" withGroup "A" withTags (RegimenTag -> "a") build,
                    dispensing(4L) on "2006-05-01" ofCode "B1" withGroup "B" withTags (RegimenTag -> "b") build,
                    dispensing(5L) on "2006-05-01" ofCode "C1" withGroup "C" withTags (RegimenTag -> "c") build
                ))

            val result = processor.doProcess(Map(("Events", Results(Some(input)))))
            val actual = result("Events").subjects.get

            assertSubjectsEqual(
                subjects(
                    male(1L) born "1980-01-01"
                        having(
                        dispensing(1L) on "2006-05-01" ofCode "A1" withGroup "A" withTags(RegimenTag -> "a", MonoComboTag -> MonoTag) build,
                        dispensing(4L) on "2006-05-01" ofCode "B1" withGroup "B" withTags(RegimenTag -> "b", MonoComboTag -> MonoTag) build,
                        dispensing(5L) on "2006-05-01" ofCode "C1" withGroup "C" withTags(RegimenTag -> "c", MonoComboTag -> MonoTag) build
                    ))
                , actual)
        }

        it("should tag combo regimens with the base item to be base combo tags") {
            def processor = new MonoComboBaseTagger("a")

            val input: Dataset[Subject] = createSubjectsWithFakeMetadata(
                male(1L) born "1980-01-01"
                    having(
                    dispensing(2L) on "2006-05-01" ofCode "B1" withGroup "B" withTags (RegimenTag -> "a+b") build,
                    dispensing(3L) on "2006-05-01" ofCode "C1" withGroup "C" withTags (RegimenTag -> "a+b+c") build
                ))

            val result = processor.doProcess(Map(("Events", Results(Some(input)))))
            val actual = result("Events").subjects.get

            assertSubjectsEqual(
                subjects(
                    male(1L) born "1980-01-01"
                        having(
                        dispensing(2L) on "2006-05-01" ofCode "B1" withGroup "B" withTags(RegimenTag -> "a+b", MonoComboTag -> BaseComboTag) build,
                        dispensing(3L) on "2006-05-01" ofCode "C1" withGroup "C" withTags(RegimenTag -> "a+b+c", MonoComboTag -> BaseComboTag) build
                    ))
                , actual)
        }

        it("should work with multiple regimen event tags") {
            def processor = new MonoComboBaseTagger("a")

            val input: Dataset[Subject] = createSubjectsWithFakeMetadata(
                male(1L) born "1980-01-01"
                    having(
                    dispensing(2L) on "2006-05-01" ofCode "B1" withGroup "B" addTag(RegimenTag, Seq("b", "a+b")) build,
                    dispensing(3L) on "2006-05-01" ofCode "C1" withGroup "C" addTag(RegimenTag, Seq("c", "a+c")) build
                ))

            val result = processor.doProcess(Map(("Events", Results(Some(input)))))
            val actual = result("Events").subjects.get

            assertSubjectsEqual(
                subjects(
                    male(1L) born "1980-01-01"
                        having(
                        dispensing(2L) on "2006-05-01" ofCode "B1" withGroup "B" addTag(RegimenTag, Seq("b", "a+b")) addTag(MonoComboTag, Seq(MonoTag, BaseComboTag)) build,
                        dispensing(3L) on "2006-05-01" ofCode "C1" withGroup "C" addTag(RegimenTag, Seq("c", "a+c")) addTag(MonoComboTag, Seq(MonoTag, BaseComboTag)) build
                    ))
                , actual)
        }

        it("should work with other combo event tags") {
            def processor = new MonoComboBaseTagger("a")

            val input: Dataset[Subject] = createSubjectsWithFakeMetadata(
                male(1L) born "1980-01-01"
                    having (
                    dispensing(6L) on "2006-05-01" ofCode "C1" withGroup "C" withTags (RegimenTag -> "b+c") build

                    ))

            val result = processor.doProcess(Map(("Events", Results(Some(input)))))
            val actual = result("Events").subjects.get

            assertSubjectsEqual(
                subjects(
                    male(1L) born "1980-01-01"
                        having (
                        dispensing(6L) on "2006-05-01" ofCode "C1" withGroup "C" withTags(RegimenTag -> "b+c", MonoComboTag -> OtherComboTag) build

                        ))
                , actual)
        }

        it("should work with a complex scenario") {
            def processor = new MonoComboBaseTagger("a")

            val input: Dataset[Subject] = createSubjectsWithFakeMetadata(
                male(1L) born "1980-01-01"
                    having(
                    dispensing(1L) on "2006-05-01" ofCode "A1" withGroup "A" withTags (RegimenTag -> "a") build,
                    dispensing(2L) on "2006-05-01" ofCode "B1" withGroup "B" withTags (RegimenTag -> "a+b") build,
                    dispensing(3L) on "2006-05-01" ofCode "C1" withGroup "C" withTags (RegimenTag -> "a+b+c") build,
                    dispensing(4L) on "2006-05-01" ofCode "B1" withGroup "B" withTags (RegimenTag -> "b") build,
                    dispensing(5L) on "2006-05-01" ofCode "C1" withGroup "C" withTags (RegimenTag -> "c") build,
                    dispensing(6L) on "2006-05-01" ofCode "C1" withGroup "C" withTags (RegimenTag -> "b+c") build,
                    dispensing(7L) on "2006-05-01" ofCode "D1" withGroup "B" build
                )
            )

            val result = processor.doProcess(Map(("Events", Results(Some(input)))))
            val actual = result("Events").subjects.get

            assertSubjectsEqual(
                subjects(
                    male(1L) born "1980-01-01"
                        having(
                        dispensing(1L) on "2006-05-01" ofCode "A1" withGroup "A" withTags(RegimenTag -> "a", MonoComboTag -> MonoTag) build,
                        dispensing(2L) on "2006-05-01" ofCode "B1" withGroup "B" withTags(RegimenTag -> "a+b", MonoComboTag -> BaseComboTag) build,
                        dispensing(3L) on "2006-05-01" ofCode "C1" withGroup "C" withTags(RegimenTag -> "a+b+c", MonoComboTag -> BaseComboTag) build,
                        dispensing(4L) on "2006-05-01" ofCode "B1" withGroup "B" withTags(RegimenTag -> "b", MonoComboTag -> MonoTag) build,
                        dispensing(5L) on "2006-05-01" ofCode "C1" withGroup "C" withTags(RegimenTag -> "c", MonoComboTag -> MonoTag) build,
                        dispensing(6L) on "2006-05-01" ofCode "C1" withGroup "C" withTags(RegimenTag -> "b+c", MonoComboTag -> OtherComboTag) build,
                        dispensing(7L) on "2006-05-01" ofCode "D1" withGroup "B" withTags (MonoComboTag -> NoRegimenTag) build
                    )
                )
                , actual)
        }
    }

    describe("Mono Combo node produced and consumed events") {
        it("should use baseEvent as consumedItemGroups") {
            def processor = new MonoComboBaseTagger("a")

            assert(processor.getConsumedItemGroups() == Seq("a"))
        }

        it("should have empty producedItemGroups") {
            def processor = new MonoComboBaseTagger("a")

            assert(processor.getProducedItemGroups().isEmpty)
        }
    }
}
