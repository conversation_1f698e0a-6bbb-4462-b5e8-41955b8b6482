package com.pharmdash.workflow.dp.etl.mdv

import com.pharmdash.workflow.dp.meta.processorannotations.Channel
import com.pharmdash.workflow.test.{DataProcessorSpec, TempFiles}
import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner

import java.nio.file.Paths

@RunWith(classOf[JUnitRunner])
class MdvClaimReaderOriginalCsvTest extends DataProcessorSpec with TempFiles {

    val startOfData = "2000-01-01"
    val endOfData = "2020-01-01"

    describe("When generating technical model from RAW data") {

        it("Should read from files") {
            withTemp(path => {
                // prepare - files are in workflow/src/test/resources/sample/jp/mdv/raw/original
                // does not contain region information and therefore does not use reference data to map regions
                val flatFiles = Paths.get(getClass.getResource("/sample/datalake/jp/mdv/janssen/published=2022-09-01").toURI).toString

                // perform - no reference data here
                val results = new MdvClaimReader(flatFiles.toString, null, "janssen", startOfData, endOfData).doProcess(Map())
                val actualSubjects = results(Channel.EVENTS).subjects.get

                // verify
                actualSubjects.count() shouldBe 1
            })
        }
    }
}
