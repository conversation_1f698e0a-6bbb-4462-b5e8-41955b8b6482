package com.pharmdash.workflow.dp.importer

import com.pharmdash.workflow.Results
import com.pharmdash.workflow.common.TechnicalModelEncoders._
import com.pharmdash.workflow.dp.DataProcessor
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.{EVENTS, REFERENCE_DATA}
import com.pharmdash.workflow.model.{RefData, Subject}
import com.pharmdash.workflow.test.AbstractDataProcessorTestCase
import com.pharmdash.workflow.test.EventTestUtils.{date, event, events, subject}
import com.prospection.arch2.model.Category.{Dispensing, Mortality, Pathology}
import com.prospection.arch2.model.FieldConstants
import com.prospection.arch2.util.WarehouseConfiguration
import org.apache.commons.io.FileUtils
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{Dataset, SaveMode}
import org.junit.runner.RunWith
import org.scalatest.Assertion
import org.scalatestplus.junit.JUnitRunner

import java.nio.file.{Files, Paths}

@RunWith(classOf[JUnitRunner])
class FilteredByEventParquetImporterIntegrationTest extends AbstractDataProcessorTestCase with WarehouseConfiguration {

    private val classification = "MDV Item"

    private def sampleSubjects = {
        createSubjectsWithFakeMetadata(
            subject("1", "M", 1980,
                events(
                    event(101, "620006542", Dispensing, date("2006-01-05")),
                    event(102, "B002", Pathology, date("2007-04-05"))
                )
            ),
            subject("2", "F", 1990,
                events(
                    event(103, "3B070000000000000", Pathology, date("2007-01-05")),
                    event(104, "B003", Dispensing, date("2008-03-05")),
                    event(105, FieldConstants.DeathCode, Mortality, date("2008-06-05"), Map("mortality" -> Seq("dead")))
                )
            ),
            subject("3", "F", 1990,
                events(
                    event(103, "A009", Dispensing, date("2007-01-05")),
                    event(105, "A009", Dispensing, date("2007-01-05")),
                    event(104, "B003", Dispensing, date("2008-03-05"))
                )
            )
        )
    }

    test("should filter by a single event code") {
        setupTestAndCleanup(filterProcessor => {
            val codes = spark.createDataset(Array(
                RefData("drugB00", "event.code", Seq("620006542"), classification),
                RefData("drugB01www", "event.code", Seq("620006542"), classification),
                RefData("drugB20", "event.tags.somethingElse", Seq("AS1ZZXC541"), classification)
            ))

            val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

            distinctEventCodes(result) should contain theSameElementsAs Seq("620006542")
            distinctEventCategories(result) should contain theSameElementsAs Seq(Dispensing)

            result(EVENTS).dataset.get.count() shouldBe 1
        })
    }

    test("should filter by a single event code and keep all events") {
        setupTestAndCleanup(filterProcessor => {
            val codes = spark.createDataset(Array(
                RefData("drugB00", "event.code", Seq("620006542"), classification),
                RefData("drugB001", "event.code", Seq("620006542"), classification),
                RefData("drugB20", "event.tags.somethingElse", Seq("AS1ZZXC541"), classification)
            ))

            val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

            distinctEventCodes(result) should contain theSameElementsAs Seq("620006542", "B002")
            distinctEventCategories(result) should contain theSameElementsAs Seq(Dispensing, Pathology)

            result(EVENTS).dataset.get.count() shouldBe 1
        }, includeAll = true)
    }

    test("should filter by multiple event codes") {
        setupTestAndCleanup(filterProcessor => {
            val codes = spark.createDataset(Array(
                RefData("drugB00", "event.code", Seq("620006542"), classification),
                RefData("drugB001", "event.code", Seq("620006542"), classification),
                RefData("drugB20", "event.code", Seq("3B070000000000000"), classification),
                RefData(FieldConstants.DeathCode, "event.code", Seq(FieldConstants.DeathCode), classification)
            ))

            val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

            distinctEventCodes(result) should contain theSameElementsAs Seq("3B070000000000000", "620006542", FieldConstants.DeathCode)
            distinctEventCategories(result) should contain theSameElementsAs Seq(Dispensing, Pathology, Mortality)

            result(EVENTS).dataset.get.count() shouldBe 2
        })
    }

    test("should return unique subjects for duplicate events") {
        setupTestAndCleanup(filterProcessor => {
            val codes = spark.createDataset(Array(
                RefData("drugB00", "event.code", Seq("A009"), classification),
                RefData("drugB001", "event.code", Seq("A009"), classification)
            ))

            val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

            distinctEventCodes(result) should contain theSameElementsAs Seq("A009")
            distinctEventCategories(result) should contain theSameElementsAs Seq(Dispensing)

            val subjects = result(EVENTS).dataset.get.as[Subject]
            subjects.count() shouldBe 1
            subjects.flatMap(subject => subject.events).count() shouldBe 2
        })
    }

    private def setupTestAndCleanup(test: DataProcessor => Assertion, includeAll: Boolean = false) = {
        val (publishDate: String, tempDir: String) = writeTempSubjects(sampleSubjects)
        val filterProcessor = createProcessor(tempDir, publishDate, includeAll)

        test.apply(filterProcessor)

        // DON'T FORGET TO CLEAN UP.
        FileUtils.deleteDirectory(Paths.get(tempDir).toFile)
    }

    def distinctEventCodes(result: Map[String, Results]): Array[String] = {
        import spark.implicits._
        result(EVENTS).subjects.get
            .select(explode($"events") as EVENTS).select("events.code")
            .distinct()
            .orderBy("events.code")
            .collect()
            .map(r => r(0).asInstanceOf[String])
    }

    def distinctEventCategories(result: Map[String, Results]): Array[String] = {
        import spark.implicits._
        result(EVENTS).subjects.get
            .select(explode($"events") as EVENTS).select("events.category")
            .distinct()
            .orderBy("events.category")
            .collect()
            .map(r => r(0).asInstanceOf[String])
    }

    def createProcessor(path: String, publishDate: String, includeAll: Boolean): DataProcessor =
        new FilteredByEventParquetImporter(new com.pharmdash.workflow.dp.config.Dataset(path, Some(1), "2018-09-01"), includeAll)

    private def writeTempSubjects(subjects: Dataset[Subject]) = {

        val publishDate: String = "2018-09-01"
        val temp = Paths.get("tmp")

        if (Files.notExists(temp)) {
            Files.createDirectory(temp)
        }

        val tempDir: String = Files.createTempDirectory(temp, "subjects-").toAbsolutePath.toString
        val path: String = tempDir + "/" + publishDate

        subjects.write.mode(SaveMode.Overwrite).parquet(path)
        (publishDate, tempDir)
    }

}


