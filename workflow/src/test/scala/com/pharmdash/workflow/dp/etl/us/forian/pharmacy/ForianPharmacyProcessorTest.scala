package com.pharmdash.workflow.dp.etl.us.forian.pharmacy

import com.pharmdash.workflow.dp.config.{ForianEtlConfig, VersionedPath}
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL.ForianDataset.{Closed, Open}
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL._
import com.pharmdash.workflow.dp.etl.us.forian.claimdiagnosis.ForianClaimDiagnosisReader.ReadRawForianClaimDiagnosis
import com.pharmdash.workflow.dp.etl.us.forian.claimprocedure.ForianClaimProcedureReader._
import com.pharmdash.workflow.dp.etl.us.forian.pharmacy.ForianPharmacyReader.ReadForianPharmacy
import com.pharmdash.workflow.dp.etl.us.forian.{ClaimProviderStateStruct, ForianClaimReaderTestBase, ForianEtlPaths}
import com.pharmdash.workflow.{DependencyProvider, WorkflowContext}
import org.apache.spark.sql.{Dataset, SaveMode}
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.{any, argThat}
import org.mockito.{ArgumentMatcher, MockitoSugar}
import org.scalatest.BeforeAndAfter
import org.scalatest.matchers.should
import org.scalatestplus.junit.JUnitRunner

import java.sql.Date

@RunWith(classOf[JUnitRunner])
class ForianPharmacyProcessorTest extends ForianClaimReaderTestBase with MockitoSugar with BeforeAndAfter with should.Matchers {

    import spark.implicits._

    implicit val context: WorkflowContext = mock[WorkflowContext]
    private val readForianPharmacy: ReadForianPharmacy = mock[ReadForianPharmacy]

    before {
        reset(readForianPharmacy)
        when(context.spark).thenReturn(spark)

        val dependencyProvider = mock[DependencyProvider]
        when(context.dependencyProvider).thenReturn(dependencyProvider)
        when(dependencyProvider.readForianPharmacy).thenReturn(readForianPharmacy)
    }

    def setupPrescribers(path: String): Seq[ForianPrescriber] = {
        val prescribers = Seq(
            defaultPrescriber().copy("3", "", "GP", "GP 1")
        )
        prescribers.toDS().write.mode(SaveMode.Overwrite).parquet(path)
        prescribers
    }

    def setupOpenPharmacy(): (Seq[ForianSubject], Seq[ForianEvent]) = {
        val subject = Seq(ForianSubject(FORIAN_PATIENT_ID = "123", dateOfBirth = Map(date("1970-01-01") -> 1), network = NetworkStruct(hasOpen = true)))
        val pharmacyEvent = Seq(ForianEvent(FORIAN_PATIENT_ID = "123", code = "D001", date = date("2015-01-01"), category = "dispensing", classification = "dispensing"))

        when(readForianPharmacy(any(), org.mockito.ArgumentMatchers.eq(ForianDataset.Open), any())).thenReturn((subject.toDS(), pharmacyEvent.toDS()))
        (subject, pharmacyEvent)
    }

    def setupClosedProcedure(): (Seq[ForianSubject], Seq[ForianEvent]) = {
        val subject = Seq(ForianSubject(FORIAN_PATIENT_ID = "123", dateOfBirth = Map(date("1970-01-01") -> 1), network = NetworkStruct(hasClosed = true)))
        val pharmacyEvent = Seq(ForianEvent(FORIAN_PATIENT_ID = "123", code = "D002", date = date("2015-01-02"), category = "dispensing", classification = "dispensing"))

        when(readForianPharmacy(any(), org.mockito.ArgumentMatchers.eq(ForianDataset.Closed), any())).thenReturn((subject.toDS(), pharmacyEvent.toDS()))
        (subject, pharmacyEvent)
    }

    it("should read raw pharmacy data and write subjects and events separately") {
        withTemp(path => {
            // setup
            val versionDate = "2025-01-01"
            val versionedPath = new VersionedPath(path.toString, versionDate, versionDate)
            val prescriberPath = s"${versionedPath.getCombinedPath}/${ForianEtlPaths.prescriberSuffix}"
            val subjectPath = s"${versionedPath.getCombinedPath}/${ForianEtlPaths.pharmacySuffix}/${ForianEtlPaths.subjectSuffix}"
            val eventPath = s"${versionedPath.getCombinedPath}/${ForianEtlPaths.pharmacySuffix}/${ForianEtlPaths.eventSuffix}"

            val prescribers = setupPrescribers(prescriberPath)
            val (openSubject, openPharmacy) = setupOpenPharmacy()
            val (closedSubject, closedPharmacy) = setupClosedProcedure()

            // run
            val processor = new ForianPharmacyProcessor(new ForianEtlConfig(versionedPath, "2018-01-01", "2025-01-01", "bucket", true, false))
            implicit val nodeId: String = "node1"
            processor.process(Map())

            // verify
            val subjectResult = spark.read.parquet(subjectPath).as[ForianSubject].collect()
            subjectResult should contain theSameElementsAs openSubject ++ closedSubject

            val eventResult = spark.read.parquet(eventPath).as[ForianEvent].collect()
            eventResult should contain theSameElementsAs openPharmacy ++ closedPharmacy

            verify(readForianPharmacy, times(1)).apply(
                datasetEqMatcher(prescribers.toDS()),
                org.mockito.ArgumentMatchers.eq(Open),
                any()
            )

            verify(readForianPharmacy, times(1)).apply(
                datasetEqMatcher(prescribers.toDS()),
                org.mockito.ArgumentMatchers.eq(Closed),
                any()
            )
        })
    }

    it("should read and output only open datasets") {
        withTemp(path => {
            // setup
            val versionDate = "2025-01-01"
            val versionedPath = new VersionedPath(path.toString, versionDate, versionDate)
            val prescriberPath = s"${versionedPath.getCombinedPath}/${ForianEtlPaths.prescriberSuffix}"
            val subjectPath = s"${versionedPath.getCombinedPath}/${ForianEtlPaths.pharmacySuffix}/${ForianEtlPaths.subjectSuffix}"
            val eventPath = s"${versionedPath.getCombinedPath}/${ForianEtlPaths.pharmacySuffix}/${ForianEtlPaths.eventSuffix}"

            val prescribers = setupPrescribers(prescriberPath)
            val (openSubject, openPharmacy) = setupOpenPharmacy()
            val (closedSubject, closedPharmacy) = setupClosedProcedure()

            // run
            val processor = new ForianPharmacyProcessor(new ForianEtlConfig(versionedPath, "2018-01-01", "2025-01-01", "bucket", false, false))
            implicit val nodeId: String = "node1"
            processor.process(Map())

            // verify
            val subjectResult = spark.read.parquet(subjectPath).as[ForianSubject].collect()
            subjectResult should contain theSameElementsAs openSubject

            val eventResult = spark.read.parquet(eventPath).as[ForianEvent].collect()
            eventResult should contain theSameElementsAs openPharmacy

            verify(readForianPharmacy, times(1)).apply(
                datasetEqMatcher(prescribers.toDS()),
                org.mockito.ArgumentMatchers.eq(Open),
                any()
            )
            verifyNoMoreInteractions(readForianPharmacy)
        })
    }
}


