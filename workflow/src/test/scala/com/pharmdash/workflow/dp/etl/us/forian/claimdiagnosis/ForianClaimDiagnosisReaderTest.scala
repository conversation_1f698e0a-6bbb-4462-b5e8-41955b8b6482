package com.pharmdash.workflow.dp.etl.us.forian.claimdiagnosis

import com.pharmdash.workflow.dp.etl.us.forian.ForianClaimReaderTestBase._
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL.ForianDataset._
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL._
import com.pharmdash.workflow.dp.etl.us.forian.claimdiagnosis.ForianClaimDiagnosisReader.readClaimDiagnosis
import com.pharmdash.workflow.dp.etl.us.forian.claimprovider.Provider
import com.pharmdash.workflow.dp.etl.us.forian.{ClaimProviderStateStruct, ForianClaimReaderTestBase}
import com.pharmdash.workflow.model.Event
import com.pharmdash.workflow.model.StandardGender.Unknown
import com.prospection.arch2.model.Category
import org.junit.runner.RunWith
import org.scalatest.matchers.should
import org.scalatestplus.junit.JUnitRunner

import java.sql.Date

@RunWith(classOf[JUnitRunner])
class ForianClaimDiagnosisReaderTest extends ForianClaimReaderTestBase with should.Matchers {

    import spark.implicits._

    private val defaultEtlParams = ForianETLParams(
        startOfDataset = "2010-01-01",
        endOfDataset = "2022-06-30",
        bucket = "",
        rawDatasources = Map(ForianDataSource.Legacy -> ""),
    )

    it("should load the first received diagnosis event per patient per diagnosis per date_of_service") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimDiagnoses = claimDiagnosesDf(
                ("10043789", "100000", date("2015-01-01"), "D001", "", "", date("2016-01-01")),
                ("10043789", "200000", date("2015-01-01"), " D001", "", "", date("2015-01-01")),
                ("10043789", "300000", date("2017-01-01"), "D001 ", "", "", date("2017-01-01")),
                ("10043789", "400000", date("2017-01-01"), " D001 ", "", "", date("2018-01-01")),
                // invalid event
                ("10043789", "400000", date("2017-01-02"), "", "", "", date("2018-01-01")),
                ("10043789", "400000", date("2017-01-03"), null, "", "", date("2018-01-01")),
                ("10043789", "400000", date("2017-01-04"), "___", "", "", date("2018-01-01")),

                ("20043789", "100000", date("2015-01-01"), "D001", "", "", date("2015-01-01")),
                ("20043789", "200000", date("2016-01-01"), "D001", "", "", date("2016-01-01")),
                ("20043789", "300000", date("2017-01-01"), "D002", "", "", date("2017-01-01")),
                ("20043789", "400000", date("2018-01-01"), "D002", "", "", date("2018-01-01"))
            )

            saveTestData(path, Subdirectories.OpenClaimDiagnosesLegacy, claimDiagnoses)

            val providers = Seq(
                ForianClaimProvider("100000", ClaimProviderStateStruct(ProviderRole.Rendering, "", "", Date.valueOf("2010-01-01"), Date.valueOf("2010-01-01")), Seq.empty),
            ).toDS()

            val (subjects, events) = readClaimDiagnosis(providers, ForianDataset.Open, etlParams)(spark)

            subjects.collect() should contain theSameElementsAs Seq(
                ForianSubject("10043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                ForianSubject("20043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
            )

            unordered(events.collect()) should contain theSameElementsAs unordered(Seq(
                ForianEvent("10043789", "D001", date("2015-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        ClaimNumbersTag -> Seq("100000", "200000"),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ),
                    Map(Event.ProvidersTag -> Seq())
                ),
                ForianEvent("10043789", "D001", date("2017-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        ClaimNumbersTag -> Seq("300000", "400000"),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ), Map(Event.ProvidersTag -> Seq())
                ),
                ForianEvent("20043789", "D001", date("2015-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        ClaimNumbersTag -> Seq("100000"),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ), Map(Event.ProvidersTag -> Seq())
                ),
                ForianEvent("20043789", "D001", date("2016-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        ClaimNumbersTag -> Seq("200000"),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ), Map(Event.ProvidersTag -> Seq())
                ),
                ForianEvent("20043789", "D002", date("2017-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        ClaimNumbersTag -> Seq("300000"),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ), Map(Event.ProvidersTag -> Seq())
                ),
                ForianEvent("20043789", "D002", date("2018-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        ClaimNumbersTag -> Seq("400000"),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ), Map(Event.ProvidersTag -> Seq())
                ),
            ))
        })
    }

    it("should load diagnoses from legacy and bimonthly") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(
                bucket = path.toString,
                rawDatasources = Map(
                    ForianDataSource.Legacy -> "legacy",
                    ForianDataSource.Rx1 -> "rx1",
                    ForianDataSource.Rx2 -> "rx2",
                    ForianDataSource.BiMonthly -> "BiMonthly"
                ))

            val claimDiagnosesLegacy = claimDiagnosesDf(
                ("10043789", "100000", date("2015-01-01"), "D001", "", "", date("2016-01-01")),
                ("10043789", "300000", date("2017-01-01"), "D001 ", "", "", date("2017-01-01")),
            )
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.Legacy)}/${Subdirectories.OpenClaimDiagnosesLegacy}", claimDiagnosesLegacy)

            val claimDiagnosesBiMonthly = claimDiagnosesDf(
                ("10043789", "300000", date("2017-01-01"), "D001 ", "", "", date("2017-01-01")),
            )
            saveTestData(path, s"${etlParams.rawDatasources(ForianDataSource.BiMonthly)}/${Subdirectories.BiMonthlyClaimDiagnoses}", claimDiagnosesBiMonthly)

            val providers = Seq(
                ForianClaimProvider("100000", ClaimProviderStateStruct(ProviderRole.Rendering, "", "", Date.valueOf("2010-01-01"), Date.valueOf("2010-01-01")), Seq.empty),
            ).toDS()

            val (subjects, events) = readClaimDiagnosis(providers, ForianDataset.Open, etlParams)(spark)

            subjects.collect() should contain theSameElementsAs Seq(
                ForianSubject("10043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy, ForianDataSource.BiMonthly))
            )

            unordered(events.collect()) should contain theSameElementsAs unordered(Seq(
                ForianEvent("10043789", "D001", date("2015-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        ClaimNumbersTag -> Seq("100000"),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ),
                    Map(Event.ProvidersTag -> Seq())
                ),
                ForianEvent("10043789", "D001", date("2017-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        ClaimNumbersTag -> Seq("300000"),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy, ForianDataSource.BiMonthly),
                    ), Map(Event.ProvidersTag -> Seq())
                ),
            ))
        })
    }

    it("should return empty dataset if claim diagnosis is empty") {
        withTemp { path =>
            val etlParams = defaultEtlParams.copy(bucket = path.toString, rawDatasources = Map(ForianDataSource.BiMonthly -> ""))

            val providers = Seq(
                ForianClaimProvider("100000", ClaimProviderStateStruct(ProviderRole.Rendering, "", "", Date.valueOf("2010-01-01"), Date.valueOf("2010-01-01")), Seq.empty),
            ).toDS()
            val (subjects, events) = readClaimDiagnosis(providers, ForianDataset.Closed, etlParams)(spark)

            subjects.count() should be(0)
            events.count() should be(0)
        }
    }

    it("should be able to load provider data from claim_provider") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimDiagnoses = claimDiagnosesDf(
                ("10043789", "100000", date("2015-01-01"), "D001", "", "", date("2022-01-01")),
                ("10043789", "200000", date("2016-01-01"), "D002", "", "", date("2022-01-01")),
                ("10043789", "300000", date("2017-01-01"), "D003", "", "", date("2022-01-01")),
                ("10043789", "400000", date("2018-01-01"), "D004", "", "", date("2022-01-01"))
            )

            saveTestData(path, Subdirectories.OpenClaimDiagnosesLegacy, claimDiagnoses)

            val providers = Seq(
                ForianClaimProvider("100000", ClaimProviderStateStruct(ProviderRole.Billing, "NY", "90027", Date.valueOf("2010-01-01"), Date.valueOf("2010-01-01")), Seq.empty),
                ForianClaimProvider("300000", ClaimProviderStateStruct(ProviderRole.Billing, "", "", Date.valueOf("2010-01-01"), Date.valueOf("2010-01-01")), Seq.empty),
            ).toDS()

            val (subjects, events) = readClaimDiagnosis(providers, ForianDataset.Open, etlParams)(spark)

            subjects.collect() should be(Seq(
                ForianSubject("10043789", null, null, null, NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
            ))

            unordered(events.collect()) should contain theSameElementsAs unordered(Seq(
                ForianEvent("10043789", "D001", date("2015-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(Event.StateTag -> "New York"),
                        tag(Event.Zip5Tag -> "90027"),
                        tag(NetworkTag -> Open),
                        ClaimNumbersTag -> Seq("100000"),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ),
                    Map(Event.ProvidersTag -> Seq())
                ),
                ForianEvent("10043789", "D002", date("2016-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        ClaimNumbersTag -> Seq("200000"),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ),
                    Map(Event.ProvidersTag -> Seq())
                ),
                ForianEvent("10043789", "D003", date("2017-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        ClaimNumbersTag -> Seq("300000"),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ),
                    Map(Event.ProvidersTag -> Seq())
                ),
                ForianEvent("10043789", "D004", date("2018-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        ClaimNumbersTag -> Seq("400000"),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ),
                    Map(Event.ProvidersTag -> Seq())
                ),
            ))
        })
    }

    it("should be able to load hcoId, prescriberId and prescriberSpecialty tags from providers") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val providers = Seq(
                ForianClaimProvider("100000", null,
                    Seq(
                        defaultProvider().copy("3", ProviderRole.Rendering, "GP", "GP 1")
                    )
                ),
                ForianClaimProvider("200000", null,
                    Seq(
                        defaultProvider().copy("3", ProviderRole.Rendering, "GP", "GP 1"),
                        defaultProvider().copy("4", ProviderRole.Rendering, "Specialist", "Specialist 1")
                    )
                ),
                ForianClaimProvider("300000", null,
                    Seq(defaultProvider().copy("5", ProviderRole.Rendering, Unknown, Unknown))
                ),
                ForianClaimProvider("400000", null,
                    Seq(
                        defaultProvider().copy("6", ProviderRole.Rendering, Unknown, Unknown, "2"),
                        defaultProvider().copy("7", ProviderRole.Referring, Unknown, Unknown, "2")
                    )
                ),
            ).toDS()

            val claimDiagnoses = claimDiagnosesDf(
                ("10043789", "100000", date("2015-01-01"), "D001", "", "", date("2022-01-01")),
                ("10043789", "200000", date("2016-01-01"), "D002", "", "", date("2022-01-01")),
                ("10043789", "300000", date("2017-01-01"), "D003", "", "", date("2022-01-01")),
                ("10043789", "400000", date("2018-01-01"), "D004", "", "", date("2022-01-01"))
            )

            saveTestData(path, Subdirectories.OpenClaimDiagnosesLegacy, claimDiagnoses)

            val (subjects, events) = readClaimDiagnosis(providers, ForianDataset.Open, etlParams)(spark)

            subjects.collect() should be(Seq(
                ForianSubject("10043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
            ))

            unordered(events.collect()) should contain theSameElementsAs unordered(Seq(
                ForianEvent("10043789", "D001", date("2015-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(Event.PrescriberIdTag -> "3"),
                        tag(Event.PrescriberSpecialtyTag -> "GP"),
                        tag(Event.PrescriberDetailSpecialtyTag -> "GP 1"),
                        ClaimNumbersTag -> Seq("100000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ),
                    Map(Event.ProvidersTag -> Seq(providersTagObjectTags("3", "Rendering", "GP", "GP 1")))
                ),
                ForianEvent("10043789", "D002", date("2016-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        Event.PrescriberIdTag -> Seq("3", "4"),
                        Event.PrescriberSpecialtyTag -> Seq("GP", "Specialist"),
                        Event.PrescriberDetailSpecialtyTag -> Seq("GP 1", "Specialist 1"),
                        ClaimNumbersTag -> Seq("200000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ),
                    Map(Event.ProvidersTag -> Seq(
                        providersTagObjectTags("3", "Rendering", "GP", "GP 1"),
                        providersTagObjectTags("4", "Rendering", "Specialist", "Specialist 1"))
                    )
                ),
                ForianEvent("10043789", "D003", date("2017-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(Event.PrescriberIdTag -> "5"),
                        ClaimNumbersTag -> Seq("300000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ),
                    Map(Event.ProvidersTag -> Seq(providersTagObjectTags("5", "Rendering", Unknown, Unknown)))
                ),
                ForianEvent("10043789", "D004", date("2018-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("400000"),
                        tag(NetworkTag -> Open),
                        Event.HcoId -> Seq("6"),
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ),
                    Map(Event.ProvidersTag -> Seq(
                        providersTagObjectTags("6", "Rendering", Unknown, Unknown, "2"),
                        providersTagObjectTags("7", "Referring", Unknown, Unknown, "2"))
                    )
                )
            ))
        })
    }

    it("should not ignore provider with unknown specialty when finding specialty tag value") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val providers = Seq(
                ForianClaimProvider("86f1ad4930eb23c9ab510c21b7b7c1084a77a34eb712dfb5a069f011d1b5a50b", null,
                    Seq(
                        Provider(id = "**********", role = "Rendering", specialty = "Internal Medicine", detailSpecialty = "Cardiovascular Disease", entityTypeCode = "1"),
                        Provider(id = "**********", role = "Billing", specialty = "General Acute Care Hospital", detailSpecialty = "Unknown", entityTypeCode = "2"),
                        Provider(id = "**********", role = "Referring", specialty = "Internal Medicine", detailSpecialty = "Cardiovascular Disease", entityTypeCode = "1"),
                    )
                ),
                ForianClaimProvider("9ba3262fe3f53aa716236b0414914d0138fe5ba33554b2c7d4417a1c614ce082", null,
                    Seq(
                        Provider(id = "**********", role = "Billing", specialty = "General Acute Care Hospital", detailSpecialty = "Unknown", entityTypeCode = "2"),
                        Provider(id = "**********", role = "Rendering", specialty = "Unknown", detailSpecialty = "Unknown", entityTypeCode = "1"),
                        Provider(id = "**********", role = "Referring", specialty = "Unknown", detailSpecialty = "Unknown", entityTypeCode = "1"),
                    )
                ),
                ForianClaimProvider("f07805348a2aa696773f06cc24bb5ec6034fe71dfddd263552c5971e33143f9a", null,
                    Seq(
                        Provider(id = "**********", role = "Attending", specialty = "Unknown", detailSpecialty = "Unknown", entityTypeCode = "1"),
                    )
                )
            ).toDS()

            val claimDiagnoses = claimDiagnosesDf(
                ("02a1dca1db68adf781f4f518e53f5cd1f584f3175c4b21576cb62cadbd003fac", "86f1ad4930eb23c9ab510c21b7b7c1084a77a34eb712dfb5a069f011d1b5a50b", date("2016-06-08"), "I10", "2", "P", date("2016-06-10")),
                ("02a1dca1db68adf781f4f518e53f5cd1f584f3175c4b21576cb62cadbd003fac", "9ba3262fe3f53aa716236b0414914d0138fe5ba33554b2c7d4417a1c614ce082", date("2016-06-08"), "I10", "6", "P", date("2016-06-10")),
                ("02a1dca1db68adf781f4f518e53f5cd1f584f3175c4b21576cb62cadbd003fac", "f07805348a2aa696773f06cc24bb5ec6034fe71dfddd263552c5971e33143f9a", date("2016-06-08"), "I10", "6", "I", date("2018-02-03"))
            )

            saveTestData(path, Subdirectories.OpenClaimDiagnosesLegacy, claimDiagnoses)

            val (_, events) = readClaimDiagnosis(providers, ForianDataset.Open, etlParams)(spark)

            unordered(events.collect()) should contain theSameElementsAs unordered(Seq(
                ForianEvent("02a1dca1db68adf781f4f518e53f5cd1f584f3175c4b21576cb62cadbd003fac", "I10", date("2016-06-08"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(Event.PrescriberIdTag -> "**********"),
                        tag(Event.PrescriberSpecialtyTag -> "Unknown"),
                        tag(Event.PrescriberDetailSpecialtyTag -> "Unknown"),
                        Event.ReferrerSpecialtyTag -> Seq("Internal Medicine", "Unknown"),
                        Event.ReferrerDetailSpecialtyTag -> Seq("Cardiovascular Disease", "Unknown"),
                        Event.ReferrerIdTag -> Seq("**********", "**********"),
                        ClaimNumbersTag -> Seq("86f1ad4930eb23c9ab510c21b7b7c1084a77a34eb712dfb5a069f011d1b5a50b", "9ba3262fe3f53aa716236b0414914d0138fe5ba33554b2c7d4417a1c614ce082", "f07805348a2aa696773f06cc24bb5ec6034fe71dfddd263552c5971e33143f9a"),
                        tag(NetworkTag -> Open),
                        tag(Event.HcoId -> "**********"),
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ),
                    Map(Event.ProvidersTag -> Seq(
                        providersTagObjectTags("**********", "Referring", "Internal Medicine", "Cardiovascular Disease"),
                        providersTagObjectTags("**********", "Rendering", "Internal Medicine", "Cardiovascular Disease"),
                        providersTagObjectTags("**********", "Billing", "General Acute Care Hospital", "Unknown", "2"),
                        providersTagObjectTags("**********", "Attending", "Unknown", "Unknown"),
                        providersTagObjectTags("**********", "Referring", "Unknown", "Unknown"),
                        providersTagObjectTags("**********", "Rendering", "Unknown", "Unknown")
                    ))
                )
            ))
        })
    }

    it("should be able to load referrer details without prescribers from providers") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val providers = Seq(
                ForianClaimProvider("100000", null,
                    Seq(
                        defaultProvider().copy("4", ProviderRole.Referring, "Specialist", "Specialist 1"),
                        defaultProvider().copy("5", ProviderRole.Referring, Unknown, Unknown)
                    )
                )
            ).toDS()

            val claimDiagnoses = claimDiagnosesDf(
                ("10043789", "100000", date("2015-01-01"), "D001", "", "", date("2022-01-01"))
            )

            saveTestData(path, Subdirectories.OpenClaimDiagnosesLegacy, claimDiagnoses)

            val (subjects, events) = readClaimDiagnosis(providers, ForianDataset.Open, etlParams)(spark)

            subjects.collect() should be(Seq(
                ForianSubject("10043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
            ))

            unordered(events.collect()) should contain theSameElementsAs unordered(Seq(
                ForianEvent("10043789", "D001", date("2015-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        Event.ReferrerIdTag -> Seq("4", "5"),
                        Event.ReferrerSpecialtyTag -> Seq("Specialist", Unknown),
                        Event.ReferrerDetailSpecialtyTag -> Seq("Specialist 1", Unknown),
                        ClaimNumbersTag -> Seq("100000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ),
                    Map(Event.ProvidersTag -> Seq(
                        providersTagObjectTags("4", "Referring", "Specialist", "Specialist 1"),
                        providersTagObjectTags("5", "Referring", Unknown, Unknown)))
                ),
            ))

        })
    }

    it("should be able to handle duplication in claim diagnosis") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimDiagnoses = claimDiagnosesDf(
                ("10043789", "100000", date("2015-01-01"), "D001", "", "", date("2022-01-01")),
                ("10043789", "100000", date("2015-01-01"), "D001", "", "", date("2022-01-01")),
                ("10043789", "200000", date("2016-01-01"), "D002", "", "", date("2022-01-01")),
                ("10043789", "200000", date("2016-01-01"), "D002", "", "", date("2022-01-01")),
                ("10043789", "300000", date("2017-01-01"), "D003", "", "", date("2022-01-01")),
                ("10043789", "400000", date("2018-01-01"), "D004", "", "", date("2022-01-01"))
            )

            saveTestData(path, Subdirectories.OpenClaimDiagnosesLegacy, claimDiagnoses)

            val providers = Seq(
                ForianClaimProvider("100000", ClaimProviderStateStruct(ProviderRole.Billing, "", "", Date.valueOf("2010-01-01"), Date.valueOf("2010-01-01")), Seq.empty),
            ).toDS()

            val (subjects, events) = readClaimDiagnosis(providers, ForianDataset.Open, etlParams)(spark)

            subjects.collect() should be(Seq(
                ForianSubject("10043789", null, null, null, NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
            ))

            unordered(events.collect()) should contain theSameElementsAs unordered(Seq(
                ForianEvent("10043789", "D001", date("2015-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("100000"),
                        tag(NetworkTag -> Open),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ),
                    Map(Event.ProvidersTag -> Seq())
                ),
                ForianEvent("10043789", "D002", date("2016-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        ClaimNumbersTag -> Seq("200000"),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ), Map(Event.ProvidersTag -> Seq())
                ),
                ForianEvent("10043789", "D003", date("2017-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        ClaimNumbersTag -> Seq("300000"),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ), Map(Event.ProvidersTag -> Seq())
                ),
                ForianEvent("10043789", "D004", date("2018-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(NetworkTag -> Open),
                        ClaimNumbersTag -> Seq("400000"),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ), Map(Event.ProvidersTag -> Seq())
                )
            ))
        })
    }

    it("should be able to read Closed diagnosis files") {
        withTemp(path => {
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimDiagnoses = claimDiagnosesDf(
                ("10043789", "100000", date("2015-01-01"), "D001", "", "", date("2022-01-01")),
                ("10043789", "200000", date("2016-01-01"), "D002", "", "", date("2022-01-01"))
            )

            saveTestData(path, Subdirectories.ClosedClaimDiagnosesLegacy, claimDiagnoses)

            val providers = spark.emptyDataset[ForianClaimProvider]

            val (subjects, events) = readClaimDiagnosis(providers, ForianDataset.Closed, etlParams)(spark)

            subjects.collect() should be(Seq(
                ForianSubject("10043789", network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy))
            ))

            unordered(events.collect()) should contain theSameElementsAs unordered(Seq(
                ForianEvent("10043789", "D001", date("2015-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        ClaimNumbersTag -> Seq("100000"),
                        tag(NetworkTag -> Closed),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ),
                    Map(Event.ProvidersTag -> Seq())
                ),
                ForianEvent("10043789", "D002", date("2016-01-01"), None, Category.Diagnosis, Classifications.Diagnosis,
                    unknownProviderTags ++ Map(
                        tag(NetworkTag -> Closed),
                        ClaimNumbersTag -> Seq("200000"),
                        unknownHcoId,
                        RawDataSourceTag -> Seq(ForianDataSource.Legacy)
                    ), Map(Event.ProvidersTag -> Seq())
                )
            ))
        })
    }

    it("should correctly identify rendering provider for professional claim type") {
        withTemp(path => {
            // arrange
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimDiagnoses = claimDiagnosesDf(
                ("10043789", "100000", date("2015-01-01"), "D001", "", "P", date("2015-02-01")),
                ("10043789", "150000", date("2015-01-01"), "D001", "", "I", date("2015-01-30")),
                ("10043789", "200000", date("2017-01-01"), "D001", "", "P", date("2017-02-01")),
                ("10043789", "300000", date("2020-01-01"), "D001", "", "P", date("2020-02-01")),
                ("10043789", "400000", date("2021-01-01"), "D001", "", "P", date("2021-02-01")),
                ("10043789", "500000", date("2022-01-01"), "D001", "", "P", date("2022-02-01")),
                ("10043789", "600000", date("2022-01-02"), "D001", "", "P", date("2022-02-01"))
            )

            saveTestData(path, Subdirectories.OpenClaimDiagnosesLegacy, claimDiagnoses)

            val claimProvider = Seq(
                ForianClaimProvider("100000", ClaimProviderStateStruct(ProviderRole.Rendering, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI1", ProviderRole.Billing, "Billing1", "DetailBilling1"),
                        defaultProvider().copy("NPI2", ProviderRole.Operating, "Operating1", "DetailOperating1"),
                        defaultProvider().copy("NPI3", ProviderRole.Supervising, "Supervising1", "DetailSupervising1"),
                        defaultProvider().copy("NPI4", ProviderRole.Attending, "Attending1", "DetailAttending1"),
                        defaultProvider().copy("NPI5", ProviderRole.Rendering, "Rendering1", "DetailRendering1")
                    )
                ),
                ForianClaimProvider("150000", ClaimProviderStateStruct(ProviderRole.Rendering, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI1_1", ProviderRole.Billing, "Billing1", "DetailBilling1"),
                        defaultProvider().copy("NPI2_1", ProviderRole.Operating, "Operating1", "DetailOperating1"),
                        defaultProvider().copy("NPI3_1", ProviderRole.Supervising, "Supervising1", "DetailSupervising1"),
                        defaultProvider().copy("NPI4_1", ProviderRole.Attending, "Attending1", "DetailAttending1"),
                        defaultProvider().copy("NPI5_1", ProviderRole.Rendering, "Rendering1", "DetailRendering1")
                    )
                ),
                ForianClaimProvider("200000", ClaimProviderStateStruct(ProviderRole.Attending, "CA", "90001", Date.valueOf("2023-02-01"), Date.valueOf("2023-02-02")),
                    Seq(
                        defaultProvider().copy("NPI6", ProviderRole.Billing, "Billing1", "DetailBilling1"),
                        defaultProvider().copy("NPI7", ProviderRole.Operating, "Operating1", "DetailOperating1"),
                        defaultProvider().copy("NPI8", ProviderRole.Supervising, "Supervising1", "DetailSupervising1"),
                        defaultProvider().copy("NPI9", ProviderRole.Attending, "Attending1", "DetailAttending1")
                    )
                ),
                ForianClaimProvider("300000", ClaimProviderStateStruct(ProviderRole.Supervising, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI10", ProviderRole.Billing, "Billing1", "DetailBilling1"),
                        defaultProvider().copy("NPI11", ProviderRole.Operating, "Operating1", "DetailOperating1"),
                        defaultProvider().copy("NPI12", ProviderRole.Supervising, "Supervising1", "DetailSupervising1")
                    )
                ),
                ForianClaimProvider("400000", ClaimProviderStateStruct(ProviderRole.Operating, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI13", ProviderRole.Billing, "Billing1", "DetailBilling1"),
                        defaultProvider().copy("NPI14", ProviderRole.Operating, "Operating1", "DetailOperating1")
                    )
                ),
                ForianClaimProvider("500000", ClaimProviderStateStruct(ProviderRole.Billing, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI15", ProviderRole.Billing, "Billing1", "DetailBilling1")
                    )
                ),
                ForianClaimProvider("600000", ClaimProviderStateStruct(ProviderRole.OtherOperating, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI16", ProviderRole.OtherOperating, "OtherOperating1", "DetailOtherOperating1")
                    )
                ),
            ).toDS()

            // act
            val (_, events) = readClaimDiagnosis(claimProvider, Open, etlParams)(spark)
            // assert
            val resultsEventsProviderTags = events.collect().map(event => event.date -> event.tags.filterKeys(
                Set(
                    Event.Zip5Tag,
                    Event.StateTag,
                    Event.ReferrerIdTag,
                    Event.PrescriberSpecialtyTag,
                    Event.PrescriberIdTag,
                    Event.PrescriberDetailSpecialtyTag,
                    Event.HcoId
                )
            )).toMap

            resultsEventsProviderTags(date("2015-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Rendering1"),
                Event.PrescriberIdTag -> Seq("NPI5"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailRendering1"),
                Event.HcoId -> Seq(Unknown)
            )

            resultsEventsProviderTags(date("2017-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Attending1"),
                Event.PrescriberIdTag -> Seq("NPI9"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailAttending1"),
                Event.HcoId -> Seq(Unknown)
            )

            resultsEventsProviderTags(date("2020-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Supervising1"),
                Event.PrescriberIdTag -> Seq("NPI12"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailSupervising1"),
                Event.HcoId -> Seq(Unknown)
            )

            resultsEventsProviderTags(date("2021-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Operating1"),
                Event.PrescriberIdTag -> Seq("NPI14"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailOperating1"),
                Event.HcoId -> Seq(Unknown)
            )

            resultsEventsProviderTags(date("2022-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Billing1"),
                Event.PrescriberIdTag -> Seq("NPI15"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailBilling1"),
                Event.HcoId -> Seq(Unknown)
            )

            resultsEventsProviderTags(date("2022-01-02")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq(Unknown),
                Event.PrescriberIdTag -> Seq(Unknown),
                Event.PrescriberDetailSpecialtyTag -> Seq(Unknown),
                Event.HcoId -> Seq(Unknown)
            )
        })
    }

    it("should correctly identify rendering provider for institutional claim type") {
        withTemp(path => {
            // arrange
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimDiagnoses = claimDiagnosesDf(
                ("10043789", "100000", date("2015-01-01"), "D001", "", "I", date("2015-02-01")),
                ("10043789", "150000", date("2015-01-01"), "D001", "", "P", date("2015-01-30")),
                ("10043789", "200000", date("2017-01-01"), "D001", "", "I", date("2017-02-01")),
                ("10043789", "300000", date("2020-01-01"), "D001", "", "I", date("2020-02-01")),
                ("10043789", "400000", date("2021-01-01"), "D001", "", "I", date("2021-02-01")),
                ("10043789", "500000", date("2022-01-01"), "D001", "", "I", date("2022-02-01")),
                ("10043789", "600000", date("2022-01-02"), "D001", "", "I", date("2022-02-01"))
            )

            saveTestData(path, Subdirectories.OpenClaimDiagnosesLegacy, claimDiagnoses)

            val claimProvider = Seq(
                ForianClaimProvider("100000", ClaimProviderStateStruct(ProviderRole.Rendering, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI1", ProviderRole.Billing, "Billing1", "DetailBilling1"),
                        defaultProvider().copy("NPI2", ProviderRole.Operating, "Operating1", "DetailOperating1"),
                        defaultProvider().copy("NPI3", ProviderRole.Supervising, "Supervising1", "DetailSupervising1"),
                        defaultProvider().copy("NPI4", ProviderRole.Attending, "Attending1", "DetailAttending1"),
                        defaultProvider().copy("NPI5", ProviderRole.Rendering, "Rendering1", "DetailRendering1"),
                        defaultProvider().copy("NPI6", ProviderRole.OtherOperating, "OtherOperating1", "DetailOtherOperating1")
                    )
                ),
                ForianClaimProvider("150000", ClaimProviderStateStruct(ProviderRole.Rendering, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI1_1", ProviderRole.Billing, "Billing1", "DetailBilling1"),
                        defaultProvider().copy("NPI2_1", ProviderRole.Operating, "Operating1", "DetailOperating1"),
                        defaultProvider().copy("NPI3_1", ProviderRole.Supervising, "Supervising1", "DetailSupervising1"),
                        defaultProvider().copy("NPI4_1", ProviderRole.Attending, "Attending1", "DetailAttending1"),
                        defaultProvider().copy("NPI5_1", ProviderRole.Rendering, "Rendering1", "DetailRendering1"),
                        defaultProvider().copy("NPI6_1", ProviderRole.OtherOperating, "OtherOperating1", "DetailOtherOperating1")
                    )
                ),
                ForianClaimProvider("200000", ClaimProviderStateStruct(ProviderRole.Attending, "CA", "90001", Date.valueOf("2023-02-01"), Date.valueOf("2023-02-02")),
                    Seq(
                        defaultProvider().copy("NPI7", ProviderRole.Billing, "Billing1", "DetailBilling1"),
                        defaultProvider().copy("NPI8", ProviderRole.Operating, "Operating1", "DetailOperating1"),
                        defaultProvider().copy("NPI9", ProviderRole.Supervising, "Supervising1", "DetailSupervising1"),
                        defaultProvider().copy("NPI10", ProviderRole.Attending, "Attending1", "DetailAttending1"),
                        defaultProvider().copy("NPI11", ProviderRole.OtherOperating, "OtherOperating1", "DetailOtherOperating1")
                    )
                ),
                ForianClaimProvider("300000", ClaimProviderStateStruct(ProviderRole.Supervising, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI12", ProviderRole.Billing, "Billing1", "DetailBilling1"),
                        defaultProvider().copy("NPI13", ProviderRole.Operating, "Operating1", "DetailOperating1"),
                        defaultProvider().copy("NPI14", ProviderRole.Supervising, "Supervising1", "DetailSupervising1"),
                        defaultProvider().copy("NPI15", ProviderRole.OtherOperating, "OtherOperating1", "DetailOtherOperating1")
                    )
                ),
                ForianClaimProvider("400000", ClaimProviderStateStruct(ProviderRole.Operating, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI16", ProviderRole.Billing, "Billing1", "DetailBilling1"),
                        defaultProvider().copy("NPI17", ProviderRole.Supervising, "Supervising1", "DetailSupervising1"),
                        defaultProvider().copy("NPI18", ProviderRole.OtherOperating, "OtherOperating1", "DetailOtherOperating1")
                    )
                ),
                ForianClaimProvider("500000", ClaimProviderStateStruct(ProviderRole.Billing, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI19", ProviderRole.Billing, "Billing1", "DetailBilling1"),
                        defaultProvider().copy("NPI20", ProviderRole.OtherOperating, "OtherOperating1", "DetailOtherOperating1")
                    )
                ),
                ForianClaimProvider("600000", ClaimProviderStateStruct(ProviderRole.OtherOperating, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI21", ProviderRole.Billing, "Billing1", "DetailBilling1")
                    )
                ),
            ).toDS()

            // act
            val (_, events) = readClaimDiagnosis(claimProvider, Open, etlParams)(spark)
            // assert
            val resultsEventsProviderTags = events.collect().map(event => event.date -> event.tags.filterKeys(
                Set(
                    Event.Zip5Tag,
                    Event.StateTag,
                    Event.ReferrerIdTag,
                    Event.PrescriberSpecialtyTag,
                    Event.PrescriberIdTag,
                    Event.PrescriberDetailSpecialtyTag,
                    Event.HcoId
                )
            )).toMap

            resultsEventsProviderTags(date("2015-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Rendering1"),
                Event.PrescriberIdTag -> Seq("NPI5"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailRendering1"),
                Event.HcoId -> Seq(Unknown)
            )

            resultsEventsProviderTags(date("2017-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Attending1"),
                Event.PrescriberIdTag -> Seq("NPI10"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailAttending1"),
                Event.HcoId -> Seq(Unknown)
            )

            resultsEventsProviderTags(date("2020-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Operating1"),
                Event.PrescriberIdTag -> Seq("NPI13"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailOperating1"),
                Event.HcoId -> Seq(Unknown)
            )

            resultsEventsProviderTags(date("2021-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Supervising1"),
                Event.PrescriberIdTag -> Seq("NPI17"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailSupervising1"),
                Event.HcoId -> Seq(Unknown)
            )

            resultsEventsProviderTags(date("2022-01-01")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("OtherOperating1"),
                Event.PrescriberIdTag -> Seq("NPI20"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailOtherOperating1"),
                Event.HcoId -> Seq(Unknown)
            )

            resultsEventsProviderTags(date("2022-01-02")) should contain theSameElementsAs Map(
                Event.Zip5Tag -> Seq("90001"),
                Event.StateTag -> Seq("California"),
                Event.ReferrerIdTag -> Seq(Unknown),
                Event.PrescriberSpecialtyTag -> Seq("Billing1"),
                Event.PrescriberIdTag -> Seq("NPI21"),
                Event.PrescriberDetailSpecialtyTag -> Seq("DetailBilling1"),
                Event.HcoId -> Seq(Unknown)
            )
        })
    }

    it("should correctly identify hcoId only from facility provider") {
        withTemp(path => {
            // arrange
            val etlParams = defaultEtlParams.copy(bucket = path.toString)

            val claimDiagnoses = claimDiagnosesDf(
                ("10043789", "100000", date("2015-01-01"), "D001", "", "P", date("2015-02-01")),
                ("10043789", "150000", date("2015-01-01"), "D001", "", "I", date("2015-01-30")),
                ("10043789", "200000", date("2017-01-01"), "D001", "", "P", date("2017-02-01")),
                ("10043789", "300000", date("2020-01-01"), "D001", "", "P", date("2020-02-01")),
            )

            saveTestData(path, Subdirectories.OpenClaimDiagnosesLegacy, claimDiagnoses)

            val claimProvider = Seq(
                ForianClaimProvider("100000", ClaimProviderStateStruct(ProviderRole.Rendering, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI1", ProviderRole.Billing, "Billing1", "DetailBilling1", "2"),
                        defaultProvider().copy("NPI2", ProviderRole.Operating, "Operating1", "DetailOperating1", "2"),
                        defaultProvider().copy("NPI3", ProviderRole.Supervising, "Supervising1", "DetailSupervising1", "2"),
                        defaultProvider().copy("NPI4", ProviderRole.Attending, "Attending1", "DetailAttending1", "2"),
                        defaultProvider().copy("NPI5", ProviderRole.Rendering, "Rendering1", "DetailRendering1", "2")
                    )
                ),
                ForianClaimProvider("150000", ClaimProviderStateStruct(ProviderRole.Rendering, "CA", "90001", Date.valueOf("2023-01-01"), Date.valueOf("2023-01-02")),
                    Seq(
                        defaultProvider().copy("NPI1_1", ProviderRole.Billing, "Billing1", "DetailBilling1", "2"),
                        defaultProvider().copy("NPI2_1", ProviderRole.Operating, "Operating1", "DetailOperating1", "2"),
                        defaultProvider().copy("NPI3_1", ProviderRole.Supervising, "Supervising1", "DetailSupervising1", "2"),
                        defaultProvider().copy("NPI4_1", ProviderRole.Attending, "Attending1", "DetailAttending1", "2"),
                        defaultProvider().copy("NPI5_1", ProviderRole.Rendering, "Rendering1", "DetailRendering1", "2")
                    )
                ),
                ForianClaimProvider("200000", ClaimProviderStateStruct(ProviderRole.Attending, "CA", "90001", Date.valueOf("2023-02-01"), Date.valueOf("2023-02-02")),
                    Seq(
                        defaultProvider().copy("NPI6", ProviderRole.Billing, "Billing1", "DetailBilling1", "1"),
                        defaultProvider().copy("NPI7", ProviderRole.Operating, "Operating1", "DetailOperating1", "1"),
                        defaultProvider().copy("NPI8", ProviderRole.Supervising, "Supervising1", "DetailSupervising1", "1"),
                        defaultProvider().copy("NPI9", ProviderRole.Attending, "Attending1", "DetailAttending1", "1")
                    )
                )
            ).toDS()

            // act
            val (_, events) = readClaimDiagnosis(claimProvider, Open, etlParams)(spark)

            // assert
            val resultsEventsProviderTags = events.collect().map(event => event.date -> event.tags.filterKeys(
                Set(
                    Event.HcoId
                )
            )).toMap

            resultsEventsProviderTags should contain theSameElementsAs
                Map(
                    date("2015-01-01") -> Map(
                        Event.HcoId -> Seq("NPI5")
                    ),
                    date("2017-01-01") -> Map(
                        unknownHcoId
                    ),
                    date("2020-01-01") -> Map(
                        unknownHcoId
                    )
                )
        })
    }

}
