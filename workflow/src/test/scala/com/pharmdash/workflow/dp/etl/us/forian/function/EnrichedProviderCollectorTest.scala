package com.pharmdash.workflow.dp.etl.us.forian.function

import com.pharmdash.workflow.dp.etl.us.forian.ForianClaimReaderTestBase
import com.pharmdash.workflow.dp.etl.us.forian.function.ProviderFunctions.ProviderStructColumns
import org.apache.spark.sql.functions.{col, udaf}
import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner

@RunWith(classOf[JUnitRunner])
class EnrichedProviderCollectorTest extends ForianClaimReaderTestBase {

    import spark.implicits._

    val columns: Seq[String] = Seq("id", "role", "specialty", "detailSpecialty", "receivedDate", "claimTypeCode", ProviderStructColumns.EntityTypeCode)
    private val collectProviders = udaf(new EnrichedProviderCollector)

    describe("ClaimProviderCollector") {
        it("should collect providers into an array output removing duplicated value (id & role) and replacing null values with 'Unknown'") {
            val df = Seq(
                ("1", "Rendering", "A", "B", "2024-01-01", "P", "1"),
                ("1", "Rendering", "A", "B", "2024-01-01", "P", "1"),
                ("2", "Rendering", null, null, null, null, "1"),
                ("3", "Rendering", "", "", "", "", "1")
            ).toDF(columns: _*)
            val results = df.agg(collectProviders(columns.map(col): _*).as("result"))

            val expectedDf = Seq(
                Array(
                    defaultEnrichedProvider().copy("1", "Rendering", "A", "B", date("2024-01-01"), "P"),
                    defaultEnrichedProvider().copy("2", "Rendering", "Unknown", "Unknown", null, "Unknown"),
                    defaultEnrichedProvider().copy("3", "Rendering", "Unknown", "Unknown", null, "Unknown")
                )
            ).toDF("result")

            assertEqualData(expectedDf, results)
        }

        it("Should ignore null/empty providerId record") {
            val df = Seq(
                ("1", "", "A", "B", "2024-01-01", "P", "1"),
                ("", "Rendering", "A", "B", "2024-01-01", "P", "1"),
                (null, "Rendering", "A", "B", "2024-01-01", "P", "1"),
                ("1", null, "A", "B", "2024-01-01", "P", "1"),
                ("1", "Billing", "C", "D", "2024-01-01", "P", "1"),

            ).toDF(columns: _*)
            val results = df.agg(collectProviders(columns.map(col): _*).as("result"))

            val expectedDf = Seq(
                Array(
                    defaultEnrichedProvider().copy("1", "Billing", "C", "D", date("2024-01-01"), "P")
                )
            ).toDF("result")

            assertEqualData(expectedDf, results)
        }
    }
}
