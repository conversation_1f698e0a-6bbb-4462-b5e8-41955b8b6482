package com.pharmdash.workflow.dp.report.transformer

import com.pharmdash.workflow.dp.A2BProcessor._
import com.pharmdash.workflow.dp.ExportType
import com.pharmdash.workflow.dp.SurvivalProcessor._
import com.pharmdash.workflow.dp.report.ExportTag
import com.pharmdash.workflow.model.Subject
import com.pharmdash.workflow.test.AbstractDataProcessorTestCase
import com.pharmdash.workflow.test.EventTestUtils._
import org.apache.spark.sql.{Dataset, Encoders}
import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner

import java.sql.Date
import scala.collection.mutable.ArrayBuffer

@RunWith(classOf[JUnitRunner])
class TimeBetweenEventsEsSubjectTransformerTest extends AbstractDataProcessorTestCase {

    test("a2b tags should be transformed") {

        val testDataRDD: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject("1", "M", 1980,
                ArrayBuffer(
                    event(101, "A1", "category", date("2006-01-05"),
                        Map(
                            "journeySequence" -> Seq("1"),
                            A2bTag -> Seq(
                                """{"eventId":101,"startEvent":"A","endEvent":"B","duration":0,"censored":false,"isSameDay":false}""",
                                """{"eventId":101,"startEvent":"A","endEvent":"C","duration":11,"censored":true,"isSameDay":false}"""
                            )),
                        Seq("A", "D")),
                    event(102, "B1", "category", date("2006-01-10"), Map(), Seq("B", "E"))
                ),
                tags = Map("ethnicGroup" -> Seq("martian", "earthling"), "age" -> Seq("1"))
            )
        )

        val exporter = createProcessorFor(A2bTag, A2bTag)

        val result = exporter.transform(testDataRDD)

        val expectedResult = spark.read.schema(result.schema).json(
            spark.createDataset(Seq(
                """
                  |{
                  |    "_subjectId": "1",
                  |    "ethnicGroup": ["earthling", "martian"],
                  |    "age": [1],
                  |    "a2b": [{
                  |        "startEvent": "A",
                  |        "endEvent": "B",
                  |        "length": 0,
                  |        "censored": false,
                  |        "isSameDay": false,
                  |        "month": "2006-01",
                  |        "prescriberAtInitiation": [],
                  |        "journeySequenceAtInitiation": [1]
                  |    }, {
                  |        "startEvent": "A",
                  |        "endEvent": "C",
                  |        "length": 11,
                  |        "censored": true,
                  |        "isSameDay": false,
                  |        "month": "2006-01",
                  |        "prescriberAtInitiation": [],
                  |        "journeySequenceAtInitiation": [1]
                  |    }]
                  |}
        """.stripMargin))(Encoders.STRING)
        )

        assertDataFrameEquals(expectedResult.toDF(), result.toDF())
    }

    test("survival tags should be transformed") {

        val testDataRDD: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject("1", "M", 1980,
                ArrayBuffer(
                    event(101, "A1", "category", date("2006-01-05"),
                        Map(
                            "journeySequence" -> Seq("1"),
                            SurvivalTag -> Seq("""{"eventId":101,"startEvent":"A","endEvent":"death","duration":11,"censored":true,"isSameDay":false}""")),
                        Seq("A", "D")),
                    event(102, "B1", "category", date("2006-01-10"), Map(), Seq("B", "E"))
                ),
                endOfData = Date.valueOf("2006-12-31")
            ),
            subject("2", "F", 1970,
                ArrayBuffer(
                    event(201, "A1", "category", date("2006-01-05"),
                        Map(
                            "journeySequence" -> Seq("1"),
                            SurvivalTag -> Seq("""{"eventId":201,"startEvent":"A","endEvent":"death","duration":9,"censored":false,"isSameDay":false}""")),
                        Seq("A", "D")),
                    event(202, "B1", "category", date("2006-01-10"), Map(), Seq("B", "E"))
                ),
                tags = Map("ethnicGroup" -> Seq("martian", "earthling"), "age" -> Seq("1")),
                calculatedDateOfDeath = Date.valueOf("2006-10-31")
            )
        )

        val exporter = createProcessorFor(SurvivalTag, SurvivalTag)
        val result = exporter.transform(testDataRDD)

        val expectedResult = spark.read.schema(result.schema).json(
            spark.createDataset(Seq(
                """
                  |{
                  |    "_subjectId": "1",
                  |    "dateOfDeath": null,
                  |    "calculatedDateOfDeath": null,
                  |    "gender": "M",
                  |    "dateOfBirth": "1980-01-01",
                  |    "ethnicGroup": [],
                  |    "age": [],
                  |    "survival": [{
                  |        "startEvent": "A",
                  |        "endEvent": "death",
                  |        "length": 11,
                  |        "censored": true,
                  |        "isSameDay": false,
                  |        "month": "2006-01",
                  |        "prescriberAtInitiation": [],
                  |        "journeySequenceAtInitiation": [1]
                  |    }]
                  |}
        """.stripMargin,
                """
                  |{
                  |    "_subjectId": "2",
                  |    "dateOfDeath": null,
                  |    "calculatedDateOfDeath": "2006-10-31",
                  |    "gender": "F",
                  |    "dateOfBirth": "1970-01-01",
                  |    "ethnicGroup": ["earthling", "martian"],
                  |    "age": [1],
                  |    "survival": [{
                  |        "startEvent": "A",
                  |        "endEvent": "death",
                  |        "length": 9,
                  |        "censored": false,
                  |        "isSameDay": false,
                  |        "month": "2006-01",
                  |        "prescriberAtInitiation": [],
                  |        "journeySequenceAtInitiation": [1]
                  |    }]
                  |}
        """.stripMargin))(Encoders.STRING)
        )

        assertDataFrameEquals(expectedResult.toDF(), result.toDF())
    }

    test("should exclude null end event from metadata") {

        val testDataRDD: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject("1", "M", 1980,
                ArrayBuffer(
                    event(101, "A1", "category", date("2006-01-05"),
                        Map(
                            "journeySequence" -> Seq("1"),
                            A2bTag -> Seq(
                                """{"eventId":101,"startEvent":"A","endEvent":"B","duration":0,"censored":false,"isSameDay":false}"""
                            )),
                        Seq("A")),
                    event(102, "B1", "category", date("2006-01-10"),
                        Map(
                            "journeySequence" -> Seq("1"),
                            A2bTag -> Seq(
                                """{"eventId":102,"startEvent":"B","endEvent":null,"duration":0,"censored":false,"isSameDay":false}"""
                            )),
                        Seq("B"))
                )
            )
        )

        val exporter = createProcessorFor(A2bTag, A2bTag)

        val result = exporter.transform(testDataRDD)

        val expectedResult = spark.read.schema(result.schema).json(
            spark.createDataset(Seq(
                """
                  |{
                  |    "_subjectId": "1",
                  |    "ethnicGroup": [],
                  |    "age": [],
                  |    "a2b": [{
                  |        "startEvent": "A",
                  |        "endEvent": "B",
                  |        "length": 0,
                  |        "censored": false,
                  |        "isSameDay": false,
                  |        "month": "2006-01",
                  |        "prescriberAtInitiation": [],
                  |        "journeySequenceAtInitiation": [1]
                  |    }, {
                  |        "startEvent": "B",
                  |        "endEvent": null,
                  |        "length": 0,
                  |        "censored": false,
                  |        "isSameDay": false,
                  |        "month": "2006-01",
                  |        "prescriberAtInitiation": [],
                  |        "journeySequenceAtInitiation": [1]
                  |    }]
                  |}
        """.stripMargin))(Encoders.STRING)
        )

        assertDataFrameEquals(expectedResult.toDF(), result.toDF())
    }

    def createProcessorFor(eventTag: String, outputPath: String): TimeBetweenEventsEsSubjectTransformer = TimeBetweenEventsEsSubjectTransformer(
        "index_1_20",
        eventTag,
        outputPath,
        eventTagToExport = List(new ExportTag("prescriber"), new ExportTag("journeySequence", ExportType.LONG)),
        subjectTagToExport = List(new ExportTag("ethnicGroup"), new ExportTag("age", ExportType.LONG))
    )
}
