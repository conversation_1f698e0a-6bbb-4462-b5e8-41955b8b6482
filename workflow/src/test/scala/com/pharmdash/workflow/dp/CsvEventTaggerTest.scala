package com.pharmdash.workflow.dp

import com.pharmdash.workflow.Results
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.{DATASET, EVENTS}
import com.pharmdash.workflow.model.Subject
import com.pharmdash.workflow.test.AbstractDataProcessorTestCase
import com.pharmdash.workflow.test.EventTestUtils.{event, subject, tag}
import org.apache.commons.lang3.exception.ContextedRuntimeException
import org.apache.spark.sql.{Dataset, Row}
import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner

import scala.collection.mutable.ArrayBuffer

@RunWith(classOf[JUnitRunner])
class CsvEventTaggerTest extends AbstractDataProcessorTestCase {

    test("can tag events with code given csv input that is trimmed") {
        import spark.implicits._

        val processor = createProcessor()

        val csvData: Dataset[Row] = Seq(
            ("A1", "A1tag1value1", "A1tag2value1"),
            ("B1", "B1tag1value2", "B1tag2value2"),
            ("C1    ", "    C1tag1value3", "    C1tag2value3    "),
            ("     D1", "   1tag1value4     ", "    D1tag2value4    ") //doesn't exist as an event
        ).toDF("event.code    ", "     tag1", "tag2       ")


        val subjects = createSubjectsWithFakeMetadata(
            // no existing tags, will get 2 new tags
            subject("1", "M", 1980,
                ArrayBuffer(
                    event(101, "A1", "category", date("2006-01-05"), tags = tag("zip" -> Seq("A112345"))),
                    event(102, "B1", "category", date("2006-01-10"), tags = tag("zip" -> Seq("B123456"))),
                    event(103, "C1", "category", date("2006-01-10"), tags = tag("zip" -> Seq("C178484"))),
                    event(103, "E1", "category", date("2006-01-10"), tags = tag("zip" -> Seq("E178434"))), //doesn't exist in csv
                    event(103, "F1", "category", date("2006-01-10"))
                )
            )
        )

        val result = processor.doProcess(Map(
            (EVENTS, Results(Some(subjects))),
            (DATASET, Results(Some(csvData)))
        ))

        val expected: Dataset[Subject] = insertFakeMetadata(Seq(
            subject("1", "M", 1980,
                ArrayBuffer(
                    event(101, "A1", "category", date("2006-01-05"), tags = tag("zip" -> Seq("A112345"), "tag1" -> Seq("A1tag1value1"), "tag2" -> Seq("A1tag2value1"))),
                    event(102, "B1", "category", date("2006-01-10"), tags = tag("zip" -> Seq("B123456"), "tag1" -> Seq("B1tag1value2"), "tag2" -> Seq("B1tag2value2"))),
                    event(103, "C1", "category", date("2006-01-10"), tags = tag("zip" -> Seq("C178484"), "tag1" -> Seq("C1tag1value3"), "tag2" -> Seq("C1tag2value3"))),
                    event(103, "E1", "category", date("2006-01-10"), tags = tag("zip" -> Seq("E178434"))),
                    event(103, "F1", "category", date("2006-01-10"))
                )
            )
        ).toDS)

        assertEqualData(expected.toDF(), result(EVENTS).subjects.get.toDF())
    }

    test("can tag events with given trimmed csv input using a tag matching identifier") {
        import spark.implicits._

        val processor = createProcessor()

        //5 character ids to be compared to 9 character ids that get shortend
        //Trim the CSV data including the header rows,
        val csvData: Dataset[Row] = Seq(
            ("     A1123", "    A1tag1value1", "   A1tag2value1   "),
            ("B1234     ", "B1tag1value2    ", "   B1tag2value2"),
            ("C1134", "C1tag1value3", "C1tag2value3"), //control - So zip is different
            ("D1134", "D1tag1tag.value4", "D1tag2value4") //doesn't exist as an event
        ).toDF("   zip    ", "   tag1   ", "    tag2   ")

        //should match the 9 digit zips to the 5 digit zips in csvData by cutting to the max length and doing it.
        val subjects = createSubjectsWithFakeMetadata(
            //no existing tags, will get 2 new tags
            subject("1", "M", 1980,
                ArrayBuffer(
                    event(101, "A1", "category", date("2006-01-05"), tags = tag("zip" -> Seq("A1123"))),
                    event(102, "B1", "category", date("2006-01-10"), tags = tag("zip" -> Seq("B1234"))),
                    event(103, "C1", "category", date("2006-01-10"), tags = tag("zip" -> Seq("C1234"))), //doesn't match zip
                    event(103, "E1", "category", date("2006-01-10")), //isn't in csv
                    event(103, "F1", "category", date("2006-01-10"))
                )
            )
        )

        val result = processor.doProcess(Map(
            (EVENTS, Results(Some(subjects))),
            (DATASET, Results(Some(csvData)))
        ))

        //Zip code remains unchanged (ie 9 characters) and still compared to 5 characters however we now have tags added.
        val expected: Dataset[Subject] = insertFakeMetadata(Seq(
            subject("1", "M", 1980,
                ArrayBuffer(
                    event(101, "A1", "category", date("2006-01-05"), tags = tag("zip" -> Seq("A1123"), "tag1" -> Seq("A1tag1value1"), "tag2" -> Seq("A1tag2value1"))),
                    event(102, "B1", "category", date("2006-01-10"), tags = tag("zip" -> Seq("B1234"), "tag1" -> Seq("B1tag1value2"), "tag2" -> Seq("B1tag2value2"))),
                    event(103, "C1", "category", date("2006-01-10"), tags = tag("zip" -> Seq("C1234"))), //zip isn't in csv
                    event(103, "E1", "category", date("2006-01-10")), //not in csv at all
                    event(103, "F1", "category", date("2006-01-10"))
                )
            )
        ).toDS)

        assertEqualData(expected.toDF(), result(EVENTS).subjects.get.toDF())
    }

    test("can tag events with given csv input using a tag identifier and replacing existing tags") {
        import spark.implicits._

        val processor = createProcessor()

        //5 character ids to be compared to 9 character ids that get shortend
        //Trim the CSV data including the header rows,
        val csvData: Dataset[Row] = Seq(
            ("A1123", "A1tag1value1", "A1tag2value1"),
            ("B1234", "B1tag1value2", "B1tag2value2"),
            ("C1134", "C1tag1value3", "C1tag2value3"), //control - So zip is different
            ("D1134", "D1tag1tag.value4", "D1tag2value4") //doesn't exist as an event
        ).toDF("zip", "tag1", "tag2")

        //should match the 9 digit zips to the 5 digit zips in csvData by cutting to the max length and doing it.
        val subjects = createSubjectsWithFakeMetadata(
            //no existing tags, will get 2 new tags
            subject("1", "M", 1980,
                ArrayBuffer(
                    event(101, "A1", "category", date("2006-01-05"), tags = tag("zip" -> Seq("test", "A1123"), "tag1" -> Seq("A1tag1value1XXX"), "tag2" -> Seq("A1tag2value1YYY"))),
                    event(102, "B1", "category", date("2006-01-10"), tags = tag("zip" -> Seq("B1234"), "tag1" -> Seq("B1tag1value2XXX"), "tag2" -> Seq("B1tag2value2YYY"))),
                    event(103, "C1", "category", date("2006-01-10"), tags = tag("zip" -> Seq("C1234"))), //doesn't match zip
                    event(103, "E1", "category", date("2006-01-10")), //isn't in csv
                    event(103, "F1", "category", date("2006-01-10"))
                )
            )
        )

        val result = processor.doProcess(Map(
            (EVENTS, Results(Some(subjects))),
            (DATASET, Results(Some(csvData)))
        ))

        //Zip code remains unchanged (ie 9 characters) and still compared to 5 characters however we now have tags added.
        val expected: Dataset[Subject] = insertFakeMetadata(Seq(
            subject("1", "M", 1980,
                ArrayBuffer(
                    event(101, "A1", "category", date("2006-01-05"), tags = tag("zip" -> Seq("test", "A1123"), "tag1" -> Seq("A1tag1value1"), "tag2" -> Seq("A1tag2value1"))),
                    event(102, "B1", "category", date("2006-01-10"), tags = tag("zip" -> Seq("B1234"), "tag1" -> Seq("B1tag1value2"), "tag2" -> Seq("B1tag2value2"))),
                    event(103, "C1", "category", date("2006-01-10"), tags = tag("zip" -> Seq("C1234"))), //zip isn't in csv
                    event(103, "E1", "category", date("2006-01-10")), //not in csv at all
                    event(103, "F1", "category", date("2006-01-10"))
                )
            )
        ).toDS)

        assertEqualData(expected.toDF(), result(EVENTS).subjects.get.toDF())
    }

    test("should throw specific null pointer exception as the CSV data is not present") {
        val processor = createProcessor()

        //should match the 9 digit zips to the 5 digit zips in csvData by cutting to the max length and doing it.
        val subjects = createSubjectsWithFakeMetadata(
            //no existing tags, will get 2 new tags
            subject("1", "M", 1980,
                ArrayBuffer(
                    event(101, "A1", "category", date("2006-01-05"), tags = tag("zip" -> Seq("A1123"), "tag1" -> Seq("A1tag1value1XXX"), "tag2" -> Seq("A1tag2value1YYY"))),
                    event(102, "B1", "category", date("2006-01-10"), tags = tag("zip" -> Seq("B1234"), "tag1" -> Seq("B1tag1value2XXX"), "tag2" -> Seq("B1tag2value2YYY"))),
                    event(103, "C1", "category", date("2006-01-10"), tags = tag("zip" -> Seq("C1234"))), //doesn't match zip
                    event(103, "E1", "category", date("2006-01-10")), //isn't in csv
                    event(103, "F1", "category", date("2006-01-10"))
                )
            )
        )

        assertThrows[ContextedRuntimeException] {
            processor.doProcess(Map(
                (EVENTS, Results(Some(subjects))),
                (DATASET, Results(null))
            ))
        }
    }

    test("should throw specific exception with event code as the tag value is null in CSV") {
        import spark.implicits._

        val processor = createProcessor()

        val csvData: Dataset[Row] = Seq(
            ("A1", null, "A1tag2value1"),
            ("B1", "B1tag1value2", ""),
        ).toDF("event.code", "tag1", "tag2")

        val subjects = createSubjectsWithFakeMetadata(
            subject("1", "M", 1980,
                ArrayBuffer(
                    event(101, "A1", "category", date("2006-01-05")),
                    event(102, "B1", "category", date("2006-01-10"))
                )
            )
        )

        val caught = intercept[Throwable] {
            val result = processor.doProcess(Map(
                (EVENTS, Results(Some(subjects))),
                (DATASET, Results(Some(csvData)))
            ))
            result(EVENTS).subjects.get.collect()
        }.getCause

        val contextException = caught.asInstanceOf[ContextedRuntimeException]
        contextException.getContextValues("Reason").get(0) should equal("Found null or empty tag value for tag1, event: A1")
    }

    test("should throw exception when no matching events with specified id columns from csv") {
        import spark.implicits._

        val processor = createProcessor()

        val csvData: Dataset[Row] = Seq(
            ("A1", "A1tag1value1", "A1tag2value1")
        ).toDF("zip", "tag1", "tag2")

        val subjects = createSubjectsWithFakeMetadata(
            subject("1", "M", 1980,
                ArrayBuffer(
                    event(101, "A1", "category", date("2006-01-05"), tags = tag("zip" -> Seq.empty)),
                    event(102, "B1", "category", date("2006-01-10"), tags = tag("zip" -> Seq.empty))
                )
            )
        )

        val caught = intercept[Throwable] {
            val result = processor.doProcess(Map(
                (EVENTS, Results(Some(subjects))),
                (DATASET, Results(Some(csvData)))
            ))
            result(EVENTS).subjects.get.collect()
        }.getCause

        val contextException = caught.asInstanceOf[ContextedRuntimeException]
        contextException.getContextValues("Reason").get(0) should equal("Cannot find any events for subject 1 with zip values specified in csv file")
    }


    def createProcessor(): DataProcessor = new CsvEventTagger()
}
