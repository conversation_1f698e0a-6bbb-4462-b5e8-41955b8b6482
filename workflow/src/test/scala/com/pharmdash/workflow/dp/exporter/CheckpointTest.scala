package com.pharmdash.workflow.dp.exporter

import com.pharmdash.workflow.dp.exporter.Checkpoint.{DataProfilesFolder, ExportsFolder}
import com.pharmdash.workflow.dp.exporter.CheckpointTest.UpstreamNodesHash
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS
import com.pharmdash.workflow.{DataProfile, Node, Results, Tag, WorkflowContext}
import com.pharmdash.workflow.test.AbstractDataProcessorTestCase
import com.pharmdash.workflow.test.EventTestUtils.{date, event, subject}
import com.pharmdash.workflow.test.SubjectBuilder.male
import org.apache.hadoop.fs.{FileSystem, Path}
import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner

import java.sql.Date
import scala.collection.mutable.ArrayBuffer

object CheckpointTest {
    val UpstreamNodesHash = "test"
}

@RunWith(classOf[JUnitRunner])
class CheckpointTest extends AbstractDataProcessorTestCase {

    test("given input then checkpoint node should write data profile into json file") {
        import spark.implicits._
        val processor = new Checkpoint(true)

        context = createWorkflowContext(createTestNode(processor), isDryRun = true)
        val testInput = createSubjectsWithFakeMetadata(
            subject("1", "M", 1980,
                ArrayBuffer(
                    event(101, "A1", "category", date("2006-01-05"),
                        Map("a2b" -> Seq("""{"eventId":101,"startEvent":"A","endEvent":"B","duration":0,"censored":false,"isSameDay":true}""")), Seq("A")),
                    event(102, "B1", "category", date("2006-01-05"),
                        Map("authorityFormId" -> Seq("1", "2"), "double" -> Seq("1.0", "2.1")), Seq("B"))
                ),
                endOfData = Date.valueOf("2006-12-31")
            ),
            subject("2", "F", 1980,
                ArrayBuffer(
                    event(101, "A1", "category", date("2006-01-05"),
                        Map("a2b" -> Seq("1")), Seq("A")),
                    event(102, "B1", "category", date("2006-01-05"),
                        Map("authorityFormId" -> Seq(), "double" -> Seq("1", "2")), Seq("B"))
                ),
                endOfData = Date.valueOf("2006-12-31")
            )
        )

        processor.doProcess(Map((EVENTS, Results(Some(testInput)))))
        val dataProfilePath = processor.target(DataProfilesFolder, UpstreamNodesHash);
        val dataProfile = spark.read.json(dataProfilePath).as[DataProfile].collect().head
        val expectedDataProfile = DataProfile(Seq(Tag("gender", "String")),
            Seq(Tag("authorityFormId", "Long"), Tag("double", "Double"), Tag("a2b", "String")))

        dataProfile.eventTags should contain theSameElementsAs expectedDataProfile.eventTags
        dataProfile.subjectTags should contain theSameElementsAs expectedDataProfile.subjectTags

        deleteTestData(dataProfilePath)
        deleteTestData(processor.target(ExportsFolder, UpstreamNodesHash))
    }

    test("checkpoint node should not write data profile into json file if flag is not checked") {
        val processor = new Checkpoint(false)

        context = createWorkflowContext(createTestNode(processor), isDryRun = true)
        val testInput = createSubjectsWithFakeMetadata(
            subject("1", "M", 1980,
                ArrayBuffer(
                    event(101, "A1", "category", date("2006-01-05"),
                        Map("a2b" -> Seq("""{"eventId":101,"startEvent":"A","endEvent":"B","duration":0,"censored":false,"isSameDay":true}""")), Seq("A")),
                    event(102, "B1", "category", date("2006-01-05"),
                        Map("authorityFormId" -> Seq("1", "2"), "double" -> Seq("1.0", "2.1")), Seq("B"))
                ),
                endOfData = Date.valueOf("2006-12-31")
            )
        )

        processor.doProcess(Map((EVENTS, Results(Some(testInput)))))

        assert(!isPathExist(processor.target(DataProfilesFolder, UpstreamNodesHash)))

        deleteTestData(processor.target(ExportsFolder, UpstreamNodesHash))
    }

    test("checkpoint should not change the data if not told to resume") {
        val processor = new Checkpoint

        context = createWorkflowContext(createTestNode(processor))
        val testInput = createSubjectsWithFakeMetadata(
            male(1) having()
        )

        val results = processor.doProcess(Map((EVENTS, Results(Some(testInput)))))

        results(EVENTS).subjects.get.collect() should equal(List(male(1) having()))
    }

    test("checkpoint should resume existing data when told to resume") {
        val processor = new Checkpoint()

        val exportsPath = processor.target(ExportsFolder, UpstreamNodesHash)

        createSubjectsWithFakeMetadata(
            male(1) having()
        ).write.parquet(exportsPath)

        context = createWorkflowContext(createTestNode(processor).copy(shouldResume = true))

        val results = processor.doProcess(Map.empty)

        results(EVENTS).subjects.get.collect() should equal(List(male(1) having()))

        deleteTestData(exportsPath)
    }

    private def createTestNode(processor: Checkpoint) = {
        Node(-1, "testCheckPoint", processor, upstreamNodesHash = UpstreamNodesHash)
    }

    private def isPathExist(source: String)(implicit context: WorkflowContext): Boolean = {
        val hdfs = FileSystem.get(new java.net.URI(source), context.spark.sparkContext.hadoopConfiguration)
        hdfs.exists(new Path(source))
    }

    private def deleteTestData(source: String)(implicit context: WorkflowContext): Boolean = {
        val hdfs = FileSystem.get(new java.net.URI(source), context.spark.sparkContext.hadoopConfiguration)
        hdfs.delete(new Path(source), true)
    }

}
