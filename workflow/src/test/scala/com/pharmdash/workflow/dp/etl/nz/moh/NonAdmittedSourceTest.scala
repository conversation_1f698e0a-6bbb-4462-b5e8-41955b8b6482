package com.pharmdash.workflow.dp.etl.nz.moh

import org.apache.spark.sql.Row
import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner

import java.sql.{Date, Timestamp}
import java.time.LocalDate
import java.time.format.{DateTimeFormatter, DateTimeFormatterBuilder}

@RunWith(classOf[JUnitRunner])
class NonAdmittedSourceTest extends MohClaimReaderTestBase {

    import spark.implicits._

    describe("When generating pharmacy events") {
        val formatter: DateTimeFormatter = new DateTimeFormatterBuilder().parseCaseInsensitive.appendPattern("ddMMMyyyy").toFormatter

        it("Standard events should have required schema") {
            val dfHelper = new DataFrameHelper(context)

            val standard = NonAdmittedSource.standard(
                Seq.empty[NonAdmittedSource].toDF()
            )

            val expectedSchema = EventIntermediateSchema.schema

            standard.schema.fields.map(f => f.name) shouldBe expectedSchema.fields.map(f => f.name)

        }

        it("Fields should be set") {
            val dfHelper = new DataFrameHelper(context)

            val source = Seq(NonAdmittedSource(
                "a",
                "Jan-10",
                "M",
                "ethnicity",
                "BB",
                50,
                "some test location",
                "Some facility code",
                "31DEC2000:23:59:00",
                "31DEC2001:23:59:00",
                "31DEC2002:23:59:00",
                "31DEC2003:23:59:00",
                "31DEC2004:23:59:00"
            )).toDF()

            val standard = NonAdmittedSource.standard(source)

            standard.count() shouldBe 1L

            val event: Row = standard.collect()(0)

            // TODO if we had a case class for EventIntermediate these asserts would be much cleaner
            event.getAs[java.lang.Long](StandardMOHColumnHeaders.EventId) shouldBe (null)
            event.getAs[String](StandardMOHColumnHeaders.SubjectId) shouldBe "a"
            event.getAs[String](StandardMOHColumnHeaders.Classification) shouldBe "MoH Non-admitted Patient"
            event.getAs[String](StandardMOHColumnHeaders.Gender) shouldBe "M"
            event.getAs[String](StandardMOHColumnHeaders.Ethnicity) shouldBe "ethnicity"
            event.getAs[Date](StandardMOHColumnHeaders.DateOfBirth) shouldBe dfHelper.date(2010, 1, 1)
            event.getAs[Date](StandardMOHColumnHeaders.DateOfDeath) shouldBe null
            event.getAs[String](StandardMOHColumnHeaders.Code) shouldBe "BB"

            event.getAs[Date](StandardMOHColumnHeaders.Date) shouldBe dfHelper.date(2004, 12, 31)
            event.getAs[java.lang.Double](StandardMOHColumnHeaders.Amount) shouldBe null
            event.getAs[java.lang.Double](StandardMOHColumnHeaders.Cost) shouldBe null

            event.getAs[Integer](StandardMOHColumnHeaders.Tag.Age) shouldBe 50
            event.getAs[String](StandardMOHColumnHeaders.Tag.Location) shouldBe "some test location"
            event.getAs[Integer](StandardMOHColumnHeaders.Tag.DaysSupply) shouldBe null
            event.getAs[Integer](StandardMOHColumnHeaders.Tag.Strength) shouldBe null
            event.getAs[Integer](StandardMOHColumnHeaders.Tag.ReferrerId) shouldBe null
            event.getAs[String](StandardMOHColumnHeaders.Tag.PrescriberId) shouldBe null
            event.getAs[Integer](StandardMOHColumnHeaders.Tag.RepeatSequence) shouldBe null

            event.getAs[Timestamp](StandardMOHColumnHeaders.Tag.StartDate) shouldBe null
            event.getAs[Timestamp](StandardMOHColumnHeaders.Tag.EndDate) shouldBe null
            event.getAs[String](StandardMOHColumnHeaders.Tag.Facility) shouldBe "Some facility code"
            event.getAs[String](StandardMOHColumnHeaders.Tag.FacilityType) shouldBe null
            event.getAs[String](StandardMOHColumnHeaders.Tag.HealthSpecialityCode) shouldBe null
            event.getAs[Timestamp](StandardMOHColumnHeaders.Tag.DatetimeOfDeparture) shouldBe dfHelper.toSqlDate(LocalDate.parse("31DEC2000", formatter))
            event.getAs[Timestamp](StandardMOHColumnHeaders.Tag.DatetimeOfEventEnd) shouldBe dfHelper.toSqlDate(LocalDate.parse("31DEC2001", formatter))
            event.getAs[Timestamp](StandardMOHColumnHeaders.Tag.DatetimeOfFirstContact) shouldBe dfHelper.toSqlDate(LocalDate.parse("31DEC2002", formatter))
            event.getAs[Timestamp](StandardMOHColumnHeaders.Tag.DatetimeOfPresentation) shouldBe dfHelper.toSqlDate(LocalDate.parse("31DEC2003", formatter))
            event.getAs[Timestamp](StandardMOHColumnHeaders.Tag.DatetimeOfService) shouldBe dfHelper.toSqlDate(LocalDate.parse("31DEC2004", formatter))

        }
    }


}
