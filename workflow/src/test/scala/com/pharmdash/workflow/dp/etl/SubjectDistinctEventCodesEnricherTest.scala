package com.pharmdash.workflow.dp.etl

import com.pharmdash.workflow.Results
import com.pharmdash.workflow.dp.meta.processorannotations.Channel
import com.pharmdash.workflow.model.Subject
import com.pharmdash.workflow.test.AbstractDataProcessorTestCase
import com.pharmdash.workflow.test.EventBuilder.dispensing
import com.pharmdash.workflow.test.EventTestUtils.date
import com.pharmdash.workflow.test.SubjectBuilder.subject
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.types.MetadataBuilder
import org.junit.runner.RunWith
import org.mockito.scalatest.MockitoSugar
import org.scalatestplus.junit.JUnitRunner

@RunWith(classOf[JUnitRunner])
class SubjectDistinctEventCodesEnricherTest extends AbstractDataProcessorTestCase with MockitoSugar {

    test("Should populate all distinct event codes when not specifying classifications") {

        val testDataRDD: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject(10043789).having(
                dispensing(1)
                    on date("2015-08-04")
                    ofCode "CODE_A"
                    classifiedAs Classifications.Dispensing,
                dispensing(2)
                    on date("2016-02-28")
                    ofCode "CODE_A"
                    classifiedAs Classifications.Dispensing,
                dispensing(3)
                    on date("2017-01-01")
                    ofCode "CODE_B"
                    classifiedAs Classifications.Dispensing,
                dispensing(4)
                    on date("2017-06-01")
                    ofCode "CODE_A"
                    classifiedAs Classifications.Dispensing,
                dispensing(5)
                    on date("2017-06-30")
                    ofCode "CODE_A"
                    classifiedAs Classifications.Dispensing,
                dispensing(6)
                    on date("2017-07-01")
                    ofCode "CODE_C"
                    classifiedAs Classifications.Dispensing,
                dispensing(7)
                    on date("2018-01-01")
                    ofCode "CODE_C"
                    classifiedAs Classifications.Dispensing
            ),
            subject(10043790).having(
                dispensing(1)
                    on date("2015-08-04")
                    ofCode "CODE_D"
                    classifiedAs Classifications.Dispensing,
                dispensing(2)
                    on date("2016-02-28")
                    ofCode "CODE_A"
                    classifiedAs Classifications.Dispensing,
                dispensing(3)
                    on date("2017-01-01")
                    ofCode "CODE_F"
                    classifiedAs Classifications.Dispensing,
                dispensing(4)
                    on date("2017-06-01")
                    ofCode "CODE_A"
                    classifiedAs Classifications.Dispensing,
                dispensing(5)
                    on date("2017-06-30")
                    ofCode "CODE_A"
                    classifiedAs Classifications.Dispensing,
                dispensing(6)
                    on date("2017-07-01")
                    ofCode "CODE_C"
                    classifiedAs Classifications.Dispensing,
                dispensing(7)
                    on date("2018-01-01")
                    ofCode "CODE_C"
                    classifiedAs Classifications.Dispensing
            )
        )

        val result = processAndExtractSubject(testDataRDD)

        result.schema(Subject.DistinctEventCodesColumn).metadata should equal(
            new MetadataBuilder()
                .putBoolean("enabled", value = true)
                .putStringArray("classifications", Array())
                .build()
        )

        result.collect().map(_.distinctEventCodesByClassification) should equal(Seq(
            Map(Classifications.Dispensing -> Set("CODE_A", "CODE_B", "CODE_C")),
            Map(Classifications.Dispensing -> Set("CODE_A", "CODE_C", "CODE_D", "CODE_F"))
        ))

    }

    test("Should populate required distinct event codes when specifying classifications") {

        val testDataRDD: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject(10043789).having(
                dispensing(1)
                    on date("2015-08-04")
                    ofCode "CODE_A"
                    classifiedAs Classifications.Dispensing,
                dispensing(2)
                    on date("2016-02-28")
                    ofCode "CODE_A"
                    classifiedAs Classifications.Dispensing,
                dispensing(3)
                    on date("2017-01-01")
                    ofCode "CODE_B"
                    classifiedAs Classifications.Diagnosis,
                dispensing(4)
                    on date("2017-06-01")
                    ofCode "CODE_A"
                    classifiedAs Classifications.Dispensing,
                dispensing(5)
                    on date("2017-06-30")
                    ofCode "CODE_A"
                    classifiedAs Classifications.Dispensing,
                dispensing(6)
                    on date("2017-07-01")
                    ofCode "CODE_C"
                    classifiedAs Classifications.Procesdure,
                dispensing(7)
                    on date("2018-01-01")
                    ofCode "CODE_C"
                    classifiedAs Classifications.Procesdure
            ),
            subject(10043790).having(
                dispensing(1)
                    on date("2015-08-04")
                    ofCode "CODE_D"
                    classifiedAs Classifications.Diagnosis,
                dispensing(2)
                    on date("2016-02-28")
                    ofCode "CODE_A"
                    classifiedAs Classifications.Dispensing,
                dispensing(3)
                    on date("2017-01-01")
                    ofCode "CODE_F"
                    classifiedAs Classifications.Diagnosis,
                dispensing(4)
                    on date("2017-06-01")
                    ofCode "CODE_A"
                    classifiedAs Classifications.Dispensing,
                dispensing(5)
                    on date("2017-06-30")
                    ofCode "CODE_A"
                    classifiedAs Classifications.Dispensing,
                dispensing(6)
                    on date("2017-07-01")
                    ofCode "CODE_C"
                    classifiedAs Classifications.Procesdure,
                dispensing(7)
                    on date("2018-01-01")
                    ofCode "CODE_C"
                    classifiedAs Classifications.Procesdure
            )
        )

        val result = processAndExtractSubject(testDataRDD, Set(Classifications.Dispensing, Classifications.Diagnosis))

        result.schema(Subject.DistinctEventCodesColumn).metadata should equal(
            new MetadataBuilder()
                .putBoolean("enabled", value = true)
                .putStringArray("classifications", Array(Classifications.Diagnosis, Classifications.Dispensing))
                .build()
        )

        result.collect().map(_.distinctEventCodesByClassification) should equal(Seq(
            Map(
                Classifications.Dispensing -> Set("CODE_A"),
                Classifications.Diagnosis -> Set("CODE_B")
            ),
            Map(
                Classifications.Dispensing -> Set("CODE_A"),
                Classifications.Diagnosis -> Set("CODE_D", "CODE_F")
            )
        ))

    }

    test("Should populate distinct codes by classification") {
        val CodeInDispensingOnly = "A_DISPENSING_CODE"
        val CodeInDiagnosisOnly = "A_DIAGNOSIS_CODE"
        val OverlappingCode = "OVERLAPPING_CODE"

        val whateverDate = "2000-01-01"

        val testDataRDD: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject("whatever").having(
                dispensing(1)
                    on date(whateverDate)
                    ofCode CodeInDispensingOnly
                    classifiedAs Classifications.Dispensing,
                dispensing(2)
                    on date(whateverDate)
                    ofCode OverlappingCode
                    classifiedAs Classifications.Dispensing,
                dispensing(3)
                    on date(whateverDate)
                    ofCode OverlappingCode
                    classifiedAs Classifications.Diagnosis,
                dispensing(4)
                    on date(whateverDate)
                    ofCode CodeInDiagnosisOnly
                    classifiedAs Classifications.Diagnosis
            )
        )

        val result = processAndExtractSubject(testDataRDD)

        result.collect().map(_.distinctEventCodesByClassification) should equal(Seq(
            Map(
                Classifications.Dispensing -> Set(CodeInDispensingOnly, OverlappingCode),
                Classifications.Diagnosis -> Set(OverlappingCode, CodeInDiagnosisOnly)
            )
        ))
    }

    private def processAndExtractSubject(dataset: Dataset[Subject], classifications: Set[String] = Set.empty): Dataset[Subject] = {
        val proc = new SubjectDistinctEventCodesEnricher(classifications);
        val result = proc.doProcess(Map((Channel.EVENTS, Results(Some(dataset)))))
        result(Channel.EVENTS).subjects.get
    }

    object Classifications {
        val Dispensing = "Dispensing"
        val Procesdure = "Procesdure"
        val Diagnosis = "Diagnosis"
    }

}
