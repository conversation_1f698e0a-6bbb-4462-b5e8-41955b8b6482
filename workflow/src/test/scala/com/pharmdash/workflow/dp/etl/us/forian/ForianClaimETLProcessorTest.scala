package com.pharmdash.workflow.dp.etl.us.forian

import com.pharmdash.workflow.WorkflowContext
import com.pharmdash.workflow.dp.config.{ForianEtlConfig, VersionedPath}
import com.pharmdash.workflow.dp.etl.us.forian.ForianClaimReaderTestBase.{tag, unknownProviderTags}
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL.ForianDataset.{Closed, ForianDataset, Open}
import com.pharmdash.workflow.dp.etl.us.forian.ForianETL.{RawDataSourceTag, _}
import com.pharmdash.workflow.dp.etl.us.forian.function.ProviderFunctions.ProviderStructColumns
import com.pharmdash.workflow.dp.meta.processorannotations.Channel
import com.pharmdash.workflow.model.StandardGender.{Female, Male, Unknown}
import com.pharmdash.workflow.model.{Event, Subject}
import com.pharmdash.workflow.test.EventBuilder.{diagnosis, dispensing, procedure}
import com.pharmdash.workflow.test.SubjectBuilder._
import com.prospection.arch2.model.Category
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}
import org.junit.runner.RunWith
import org.mockito.ArgumentMatcher
import org.mockito.scalatest.MockitoSugar
import org.scalatest.BeforeAndAfter
import org.scalatest.matchers.should
import org.scalatestplus.junit.JUnitRunner

import java.nio.file.Path
import java.sql.Date
import scala.language.postfixOps

object ForianClaimETLProcessorTest {

    def datasetMatcher[T](expected: Dataset[T]): ArgumentMatcher[Dataset[T]] = {
        (actual: Dataset[T]) => actual != null && (actual.collect() sameElements expected.collect())
    }
}

@RunWith(classOf[JUnitRunner])
class ForianClaimETLProcessorTest extends ForianClaimReaderTestBase with MockitoSugar with BeforeAndAfter with should.Matchers {

    import spark.implicits._

    implicit val context: WorkflowContext = mock[WorkflowContext]

    private val versionedPath = new VersionedPath("ForianClaimETLProcessorTest", "2025-01-31", "2025-01-31")

    private def processData(path: Path,
                       startOfDataset: String = "2010-01-01",
                       endOfDataset: String = "2020-06-30",
                   )(implicit context: WorkflowContext): Dataset[Subject] = {

        val intermediateOutputPath = new VersionedPath(s"${path.toString}/${versionedPath.getPath}", versionedPath.getPublished, versionedPath.getGenerated)
        val forianEtlConfig = new ForianEtlConfig(intermediateOutputPath = intermediateOutputPath, startOfDataset = startOfDataset, endOfDataset = endOfDataset, rawDataBucket = "dummy_not_used_here")
        val results = new ForianClaimETLProcessor(forianEtlConfig).doProcess(Map())

        results(Channel.EVENTS).subjects.get
    }

    private def fakePharmacySubjectPerPatient(claimPatient: Seq[ForianSubject], isOpen: Boolean): Seq[ForianSubject] = {
        claimPatient.flatMap(row => {
          Seq(ForianSubject(FORIAN_PATIENT_ID = row.FORIAN_PATIENT_ID, network = NetworkStruct(hasOpen = isOpen, hasClosed = !isOpen), rawDatasource = row.rawDatasource))
        })
    }

    private def setupFakePharmacyDataPerPatient(claimPatient: Seq[ForianSubject], date: Date, code: String, isOpen: Boolean = true): (Seq[ForianSubject], Seq[ForianEvent]) = {
        val subjects = fakePharmacySubjectPerPatient(claimPatient, isOpen)
        val events = claimPatient.flatMap(row => Seq(
          ForianEvent(FORIAN_PATIENT_ID = row.FORIAN_PATIENT_ID, code = code, date = date, category = Category.Dispensing, classification = Classifications.Dispensing)
        ))
        (subjects, events)
    }

    before {
        when(context.spark).thenReturn(spark)
    }

    private def setupEmptyTestData(tempTestPath: Path): Unit = {
        saveSubjectTestData(tempTestPath, ForianEtlPaths.claimPatientSuffix, Seq.empty)
        saveSubjectTestData(tempTestPath, ForianEtlPaths.claimProcedureSuffix, Seq.empty)
        saveSubjectTestData(tempTestPath, ForianEtlPaths.claimDiagnosisSuffix, Seq.empty)
        saveSubjectTestData(tempTestPath, ForianEtlPaths.pharmacySuffix, Seq.empty)
        saveSubjectTestData(tempTestPath, ForianEtlPaths.claimHeaderSuffix, Seq.empty)

        saveEventTestData(tempTestPath, ForianEtlPaths.claimProcedureSuffix, Seq.empty)
        saveEventTestData(tempTestPath, ForianEtlPaths.claimDiagnosisSuffix, Seq.empty)
        saveEventTestData(tempTestPath, ForianEtlPaths.pharmacySuffix, Seq.empty)
    }

    def saveSubjectTestData(tempPath: Path, pathSuffix: String, subjects: Seq[ForianSubject]): Unit = {
        saveTestData(tempPath, s"${versionedPath.getCombinedPath}/$pathSuffix/${ForianEtlPaths.subjectSuffix}", subjects.toDF())
    }

    def saveEventTestData(tempPath: Path, pathSuffix: String, events: Seq[ForianEvent]): Unit = {
        saveTestData(tempPath, s"${versionedPath.getCombinedPath}/$pathSuffix/${ForianEtlPaths.eventSuffix}", events.toDF())
    }

    it("should skip age tag when the patient does not have date of birth information") {
        withTemp(path => {
            setupEmptyTestData(path)

            val pharmacySubjects = Seq(
                ForianSubject("1", Map.empty, null, Map("M" -> 1), NetworkStruct(hasOpen = true))
            )
            saveSubjectTestData(path, ForianEtlPaths.pharmacySuffix, pharmacySubjects)

            val pharmacyEvents = Seq(
                ForianEvent("1", "**********1", date("2015-08-04"), Some(1), Category.Dispensing, Classifications.Dispensing)
            )
            saveEventTestData(path, ForianEtlPaths.pharmacySuffix, pharmacyEvents)

            val actualSubjects = processData(path)

            val expectedSubjects = Seq(
                male(1).having(
                    dispensing(1)
                        on date("2015-08-04")
                        ofCode "**********1"
                        amount 1
                        classifiedAs Classifications.Dispensing,
                )
            ).toDS()

            assertEventsEqual(expectedSubjects, actualSubjects)
        })
    }

    it("should integrate with other readers and union events correctly") {
        withTemp(path => {
            setupEmptyTestData(path)

            // prepare claim patient
            val claimPatient = Seq(
                ForianSubject("1", Map(date("1980-01-01") -> 1), null, Map("Male" -> 1), NetworkStruct(hasOpen = true), rawDatasource=Seq(ForianDataSource.Legacy))
            )
            saveSubjectTestData(path, ForianEtlPaths.claimPatientSuffix, claimPatient)

            // prepare pharmacy data
            val pharmacySubjects = fakePharmacySubjectPerPatient(claimPatient, isOpen = true)
            val closedPharmacySubjects = fakePharmacySubjectPerPatient(claimPatient, isOpen = false)
            saveSubjectTestData(path, ForianEtlPaths.pharmacySuffix, pharmacySubjects ++ closedPharmacySubjects)

            val pharmacyEvents = Seq(
                ForianEvent("1", "1", date("2020-01-01"), Some(0), Category.Dispensing, Classifications.Dispensing,
                    Map(
                        Event.DataSourceTag -> Seq(DispensingDataSource.Pharmacy),
                        NetworkTag -> Seq("Open")
                    ),
                    Map(
                        Event.ProvidersTag -> Seq(
                            Map(ProviderStructColumns.Id -> "2")
                        )
                    ),
                    Some(10.5)
                ),
                ForianEvent("1", "2", date("2020-02-01"), Some(1), Category.Dispensing, Classifications.Dispensing,
                    Map(
                        Event.DataSourceTag -> Seq(DispensingDataSource.Pharmacy),
                        NetworkTag -> Seq("Closed")
                    ),
                    Map(
                        Event.ProvidersTag -> Seq(
                            Map(ProviderStructColumns.Id -> "1")
                        )
                    ),
                    Some(20.0)
                )
            )
            saveEventTestData(path, ForianEtlPaths.pharmacySuffix, pharmacyEvents)

            // prepare diagnosis
            val diagnosisEvents = Seq(
                ForianEvent("1", "3", date("2020-05-01"), Some(1), Category.Diagnosis, Classifications.Diagnosis,
                    Map(
                        NetworkTag -> Seq("Open")
                    ),
                    Map(
                        Event.ProvidersTag -> Seq(
                            Map(ProviderStructColumns.Id -> "1")
                        )
                    )
                ),
                ForianEvent("1", "4", date("2020-03-01"), Some(1), Category.Diagnosis, Classifications.Diagnosis,
                    Map(
                        NetworkTag -> Seq("Closed")
                    ),
                    Map(
                        Event.ProvidersTag -> Seq(
                            Map(ProviderStructColumns.Id -> "3")
                        )
                    )
                )
            )
            saveSubjectTestData(path, ForianEtlPaths.claimDiagnosisSuffix, claimPatient)
            saveEventTestData(path, ForianEtlPaths.claimDiagnosisSuffix, diagnosisEvents)

            // prepare procedure
            val procedureEvents = Seq(
                ForianEvent("1", "3", date("2021-02-01"), Some(1), Category.Procedure, Classifications.Procedure,
                    Map(
                        NetworkTag -> Seq("Open")
                    ),
                    Map(
                        Event.ProvidersTag -> Seq(
                            Map(ProviderStructColumns.Id -> "1")
                        )
                    )
                ),
                ForianEvent("1", "3", date("2021-05-01"), Some(1), Category.Dispensing, Classifications.Dispensing,
                    Map(
                        Event.DataSourceTag -> Seq(DispensingDataSource.Procedure),
                        NetworkTag -> Seq("Open")
                    ),
                    Map(
                        Event.ProvidersTag -> Seq(
                            Map(ProviderStructColumns.Id -> "1")
                        )
                    )
                ),
                ForianEvent("1", "4", date("2021-01-01"), Some(1), Category.Procedure, Classifications.Procedure,
                    Map(
                        NetworkTag -> Seq("Closed")
                    ),
                    Map(
                        Event.ProvidersTag -> Seq(
                            Map(ProviderStructColumns.Id -> "3")
                        )
                    )
                ),
                ForianEvent("1", "4", date("2021-03-01"), Some(1), Category.Dispensing, Classifications.Dispensing,
                    Map(
                        Event.DataSourceTag -> Seq(DispensingDataSource.Procedure),
                        NetworkTag -> Seq("Closed")
                    ),
                    Map(
                        Event.ProvidersTag -> Seq(
                            Map(ProviderStructColumns.Id -> "3")
                        )
                    )
                )
            )
            saveSubjectTestData(path, ForianEtlPaths.claimProcedureSuffix, claimPatient)
            saveEventTestData(path, ForianEtlPaths.claimProcedureSuffix, procedureEvents)

            // read
            val results = processData(path, "2010-01-01", "2021-06-30")

            results.collect() should be(Seq(
                subject("1")
                    born "1980-01-01"
                    withStartOfData "2020-01-01"
                    withEndOfData "2021-05-01"
                    withStartOfDataset "2010-01-01"
                    withEndOfDataset "2021-06-30"
                    gender Male
                    tagValues(NetworkTag, Seq("Closed", "Open"))
                    tagValues(RawDataSourceTag, Seq(ForianDataSource.Legacy))
                    firstDispensingDate "2020-01-01"
                    having(
                    // order matters
                    dispensing(0)
                        on date("2020-01-01")
                        ofCode "1"
                        costing 10.5
                        amount 0
                        tag (NetworkTag -> "Open")
                        tag (Event.DataSourceTag -> DispensingDataSource.Pharmacy)
                        tag (Event.AgeTag -> "40")
                        seqObjectTag (Event.ProvidersTag -> Seq(Map(ProviderStructColumns.Id -> "2")))
                        classifiedAs Classifications.Dispensing,
                    dispensing(1)
                        on date("2020-02-01")
                        ofCode "2"
                        costing 20.0
                        amount 1
                        tag (NetworkTag -> "Closed")
                        tag (Event.DataSourceTag -> DispensingDataSource.Pharmacy)
                        tag (Event.AgeTag -> "40")
                        seqObjectTag (Event.ProvidersTag -> Seq(Map(ProviderStructColumns.Id -> "1")))
                        classifiedAs Classifications.Dispensing,
                    diagnosis(2)
                        on date("2020-03-01")
                        ofCode "4"
                        amount 1
                        tag (NetworkTag -> "Closed")
                        tag (Event.AgeTag -> "40")
                        seqObjectTag (Event.ProvidersTag -> Seq(Map(ProviderStructColumns.Id -> "3")))
                        classifiedAs Classifications.Diagnosis,
                    diagnosis(3)
                        on date("2020-05-01")
                        ofCode "3"
                        amount 1
                        tag (NetworkTag -> "Open")
                        tag (Event.AgeTag -> "40")
                        seqObjectTag (Event.ProvidersTag -> Seq(Map(ProviderStructColumns.Id -> "1")))
                        classifiedAs Classifications.Diagnosis,
                    procedure(4)
                        on date("2021-01-01")
                        ofCode "4"
                        amount 1
                        tag (NetworkTag -> "Closed")
                        tag (Event.AgeTag -> "41")
                        seqObjectTag (Event.ProvidersTag -> Seq(Map(ProviderStructColumns.Id -> "3")))
                        classifiedAs Classifications.Procedure,
                    procedure(5)
                        on date("2021-02-01")
                        ofCode "3"
                        amount 1
                        tag (NetworkTag -> "Open")
                        tag (Event.AgeTag -> "41")
                        seqObjectTag (Event.ProvidersTag -> Seq(Map(ProviderStructColumns.Id -> "1")))
                        classifiedAs Classifications.Procedure,
                    dispensing(6)
                        on date("2021-03-01")
                        ofCode "4"
                        amount 1
                        tag (NetworkTag -> "Closed")
                        tag (Event.DataSourceTag -> DispensingDataSource.Procedure)
                        tag (Event.AgeTag -> "41")
                        seqObjectTag (Event.ProvidersTag -> Seq(Map(ProviderStructColumns.Id -> "3")))
                        classifiedAs Classifications.Dispensing,
                    dispensing(7)
                        on date("2021-05-01")
                        ofCode "3"
                        amount 1
                        tag (NetworkTag -> "Open")
                        tag (Event.DataSourceTag -> DispensingDataSource.Procedure)
                        tag (Event.AgeTag -> "41")
                        seqObjectTag (Event.ProvidersTag -> Seq(Map(ProviderStructColumns.Id -> "1")))
                        classifiedAs Classifications.Dispensing
                )
            ))

        })
    }

    describe("subject ingestion") {

        it("should treat dateOfBirth later than endOfData as null values") {
            withTemp(path => {
                setupEmptyTestData(path)

                val claimPatient = Seq(
                    ForianSubject("10043789", Map(date("2020-01-01") -> 1), null, Map("Male" -> 1), NetworkStruct(hasOpen = true), rawDatasource=Seq(ForianDataSource.Legacy)),
                    ForianSubject("20054329", Map(date("2021-01-01") -> 1), null, Map("Male" -> 1), NetworkStruct(hasOpen = true), rawDatasource=Seq(ForianDataSource.Legacy)),
                    ForianSubject("300QWERT", Map(date("1970-01-01") -> 1), null, Map("Female" -> 1), NetworkStruct(hasOpen = true), rawDatasource=Seq(ForianDataSource.Legacy)),
                    ForianSubject("400ASDFG", Map(date("2020-01-01") -> 1), null, Map("Female" -> 1), NetworkStruct(hasOpen = true), rawDatasource=Seq(ForianDataSource.Legacy)),
                )
                saveSubjectTestData(path, ForianEtlPaths.claimPatientSuffix, claimPatient)

                val (pharmacySubject, pharmacyEvent) = setupFakePharmacyDataPerPatient(claimPatient, date("2020-06-20"), "0")
                saveSubjectTestData(path, ForianEtlPaths.pharmacySuffix, pharmacySubject)
                saveEventTestData(path, ForianEtlPaths.pharmacySuffix, pharmacyEvent)

                val expectedSubjects = subjects(
                    male("10043789")
                        withStartOfData "2020-06-20"
                        withEndOfData "2020-06-20"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2020-06-20"
                        born "2020-01-01"
                        build,
                    male("20054329")
                        withStartOfData "2020-06-20"
                        withEndOfData "2020-06-20"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2020-06-20"
                        build,
                    female("300QWERT")
                        withStartOfData "2020-06-20"
                        withEndOfData "2020-06-20"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1970-01-01"
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2020-06-20"
                        build,
                    female("400ASDFG")
                        withStartOfData "2020-06-20"
                        withEndOfData "2020-06-20"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "2020-01-01"
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2020-06-20"
                        build
                )

                val actualSubjects = processData(path).sort("id")

                assertSubjectsEqualIgnoringEvents(expectedSubjects, actualSubjects)
            })
        }

        it("should select the most common value for dateOfBirth when there is multiple dateOfBirth value for one subject") {
            withTemp(path => {
                setupEmptyTestData(path)

                val claimPatient = Seq(
                    ForianSubject(
                        "10043789",
                        Map(date("1960-01-01") -> 3, date("1970-01-01") -> 1, date("1980-01-01") -> 2),
                        null,
                        Map("Male" -> 6),
                        NetworkStruct(hasOpen = true),
                        rawDatasource=Seq(ForianDataSource.Legacy)
                    )
                )
                saveSubjectTestData(path, ForianEtlPaths.claimPatientSuffix, claimPatient)

                val (pharmacySubject, pharmacyEvent) = setupFakePharmacyDataPerPatient(claimPatient, date("2020-06-20"), "0")
                saveSubjectTestData(path, ForianEtlPaths.pharmacySuffix, pharmacySubject)
                saveEventTestData(path, ForianEtlPaths.pharmacySuffix, pharmacyEvent)

                val expectedSubjects = subjects(
                    male("10043789")
                        withStartOfData "2020-06-20"
                        withEndOfData "2020-06-20"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1960-01-01"
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2020-06-20"
                        build
                )

                val actualSubjects = processData(path).sort("id")

                assertSubjectsEqualIgnoringEvents(expectedSubjects, actualSubjects)
            })
        }

        it("should use null as dateOfBirth when all dateOfBirth records are null for a subject") {
            withTemp(path => {
                setupEmptyTestData(path)

                val claimPatient = Seq(
                    ForianSubject("10043789", Map.empty, null, Map("Male" -> 6), NetworkStruct(hasOpen = true), rawDatasource=Seq(ForianDataSource.Legacy))
                )
                saveSubjectTestData(path, ForianEtlPaths.claimPatientSuffix, claimPatient)
                val (pharmacySubject, pharmacyEvent) = setupFakePharmacyDataPerPatient(claimPatient, date("2020-06-20"), "0")
                saveSubjectTestData(path, ForianEtlPaths.pharmacySuffix, pharmacySubject)
                saveEventTestData(path, ForianEtlPaths.pharmacySuffix, pharmacyEvent)

                val expectedSubjects = subjects(
                    male("10043789")
                        withStartOfData "2020-06-20"
                        withEndOfData "2020-06-20"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2020-06-20"
                        build
                )

                val actualSubjects = processData(path).sort("id")

                assertSubjectsEqualIgnoringEvents(expectedSubjects, actualSubjects)
            })
        }

        it("should use Unknown as gender when there isn't any Male/Female gender in one subject") {
            withTemp(path => {
                setupEmptyTestData(path)

                val claimPatient = Seq(
                    ForianSubject("10043789", Map(date("1980-01-01") -> 1), null, Map.empty, NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("20054329", Map(date("1980-01-01") -> 1), null, Map.empty, NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("300QWERT", Map(date("1970-01-01") -> 1), null, Map.empty, NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("400ASDFG", Map(date("1960-01-01") -> 1), null, Map.empty, NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
                )
                saveSubjectTestData(path, ForianEtlPaths.claimPatientSuffix, claimPatient)
                val (pharmacySubject, pharmacyEvent) = setupFakePharmacyDataPerPatient(claimPatient, date("2020-06-20"), "0")
                saveSubjectTestData(path, ForianEtlPaths.pharmacySuffix, pharmacySubject)
                saveEventTestData(path, ForianEtlPaths.pharmacySuffix, pharmacyEvent)

                val expectedSubjects = subjects(
                    subject("10043789")
                        gender Unknown
                        withStartOfData "2020-06-20"
                        withEndOfData "2020-06-20"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1980-01-01"
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2020-06-20"
                        build,
                    subject("20054329")
                        gender Unknown
                        withStartOfData "2020-06-20"
                        withEndOfData "2020-06-20"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1980-01-01"
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2020-06-20"
                        build,
                    subject("300QWERT")
                        gender Unknown
                        withStartOfData "2020-06-20"
                        withEndOfData "2020-06-20"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1970-01-01"
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2020-06-20"
                        build,
                    subject("400ASDFG")
                        gender Unknown
                        withStartOfData "2020-06-20"
                        withEndOfData "2020-06-20"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1960-01-01"
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2020-06-20"
                        build
                )

                val actualSubjects = processData(path).sort("id")

                assertSubjectsEqualIgnoringEvents(expectedSubjects, actualSubjects)
            })
        }

        it("should select the most common Male/Female value for gender when there is multiple gender value for one subject") {
            withTemp(path => {
                setupEmptyTestData(path)

                val claimPatient = Seq(
                    ForianSubject("10043789", Map(date("1980-01-01") -> 1), null, Map("Male" -> 3, "Female" -> 4), NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
                )
                saveSubjectTestData(path, ForianEtlPaths.claimPatientSuffix, claimPatient)
                val (pharmacySubject, pharmacyEvent) = setupFakePharmacyDataPerPatient(claimPatient, date("2020-06-20"), "0")
                saveSubjectTestData(path, ForianEtlPaths.pharmacySuffix, pharmacySubject)
                saveEventTestData(path, ForianEtlPaths.pharmacySuffix, pharmacyEvent)

                val expectedSubjects = subjects(
                    female("10043789")
                        withStartOfData "2020-06-20"
                        withEndOfData "2020-06-20"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1980-01-01"
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2020-06-20"
                        build
                )

                val actualSubjects = processData(path).sort("id")

                assertSubjectsEqualIgnoringEvents(expectedSubjects, actualSubjects)
            })
        }

        it("should include gender and date of birth from pharmacy when deciding the most common gender and date of birth") {
            withTemp(path => {
                setupEmptyTestData(path)

                val claimPatient = Seq(
                    ForianSubject("10043789", Map(date("1980-01-01") -> 2, date("1960-01-01") -> 3, date("1970-01-01") -> 1), null, Map(Male -> 1, Female -> 2), NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("2", Map(date("1980-01-01") -> 2), null, Map(Female -> 2), NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
                )
                saveSubjectTestData(path, ForianEtlPaths.claimPatientSuffix, claimPatient)

                val pharmacySubjects = Seq(
                    ForianSubject(
                        "10043789",
                        Map(date("1980-01-01") -> 4, date("1960-01-01") -> 1, date("1970-01-01") -> 2),
                        null,
                        Map(Male -> 5, Female -> 3),
                        NetworkStruct(hasOpen = true),
                        rawDatasource = Seq(ForianDataSource.Legacy)
                    ),
                    ForianSubject(
                        "2",
                        Map(date("1980-01-01") -> 1),
                        null,
                        Map(Male -> 1),
                        NetworkStruct(hasOpen = true),
                        rawDatasource = Seq(ForianDataSource.Legacy)
                    )
                )
                saveSubjectTestData(path, ForianEtlPaths.pharmacySuffix, pharmacySubjects)

                val pharmacyEvents = Seq(
                    ForianEvent("10043789", "**********1", date("2011-01-01"), Some(1), Category.Dispensing, Classifications.Dispensing),
                    ForianEvent("2", "**********1", date("2011-01-01"), Some(1), Category.Dispensing, Classifications.Dispensing)
                )
                saveEventTestData(path, ForianEtlPaths.pharmacySuffix, pharmacyEvents)


                val expectedSubjects = subjects(
                    male("10043789")
                        withStartOfData "2011-01-01"
                        withEndOfData "2011-01-01"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1980-01-01"
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2011-01-01"
                        build,
                    female("2")
                        withStartOfData "2011-01-01"
                        withEndOfData "2011-01-01"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1980-01-01"
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2011-01-01"
                        build
                )

                val actualSubjects = processData(path).sort("id")

                assertSubjectsEqualIgnoringEvents(expectedSubjects, actualSubjects)
            })
        }

        it("should load patients that only exists in PHARMACY file") {
            // checks:
            //   subject id creation,
            //   startOfDate
            //   endOfDate
            //   dateOfDeath
            //   calculatedDateOfDeath
            //   subject tags from CLAIM_PATIENT file
            withTemp(path => {
                setupEmptyTestData(path)

                val pharmacySubjects = Seq(
                    ForianSubject("1", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("2", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
                )
                saveSubjectTestData(path, ForianEtlPaths.pharmacySuffix, pharmacySubjects)

                val pharmacyEvents = Seq(
                    ForianEvent("1", "**********1", date("2015-08-04"), Some(1), Category.Dispensing, Classifications.Dispensing),
                    ForianEvent("2", "**********1", date("2015-08-04"), Some(1), Category.Dispensing, Classifications.Dispensing)
                )
                saveEventTestData(path, ForianEtlPaths.pharmacySuffix, pharmacyEvents)

                val expectedSubjects = subjects(
                    subject("1")
                        withStartOfData "2015-08-04"
                        withEndOfData "2015-08-04"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        gender Unknown
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2015-08-04"
                        build,
                    subject("2")
                        withStartOfData "2015-08-04"
                        withEndOfData "2015-08-04"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        gender Unknown
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2015-08-04"
                        build
                )

                val actualSubjects = processData(path).sort("id")

                assertSubjectsEqualIgnoringEvents(expectedSubjects, actualSubjects)
            })
        }

        it("should load patients that only exists in procedure, diagnosis and header files if they have at least 1 valid event") {
            withTemp(path => {
                setupEmptyTestData(path)

                val diagnosisSubjects = Seq(
                    ForianSubject("3", Map.empty, null, Map.empty, network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )
                saveSubjectTestData(path, ForianEtlPaths.claimDiagnosisSuffix, diagnosisSubjects)

                val diagnosisEvents = Seq(
                    ForianEvent("3", "D001", date("2014-01-01"), None, Category.Diagnosis, Classifications.Diagnosis),
                )
                saveEventTestData(path, ForianEtlPaths.claimDiagnosisSuffix, diagnosisEvents)

                val claimProcedureDS = Seq(
                    ForianEvent("5", "D001", date("2012-06-01"), None, Category.Procedure, Classifications.Procedure)
                )
                saveEventTestData(path, ForianEtlPaths.claimProcedureSuffix, claimProcedureDS)

                val procedureSubjects = Seq(
                    ForianSubject("5", Map.empty, null, Map.empty, network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )
                saveSubjectTestData(path, ForianEtlPaths.claimProcedureSuffix, procedureSubjects)

                val expectedSubjects = subjects(
                    subject("3")
                        withStartOfData "2014-01-01"
                        withEndOfData "2014-01-01"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        gender Unknown
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2010-01-01"
                        build,
                    subject("5")
                        withStartOfData "2012-06-01"
                        withEndOfData "2012-06-01"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        gender Unknown
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2010-01-01"
                        build
                )
                val actualSubjects = processData(path)

                assertSubjectsEqualIgnoringEvents(expectedSubjects, actualSubjects)
            })
        }

        it("should consolidate date of death from claim_header and calculate date of death from last event") {
            withTemp(path => {
                setupEmptyTestData(path)

                val claimPatient = Seq(
                    ForianSubject("1", dateOfBirth = Map(date("1970-01-01") -> 1), gender = Map("M" -> 1), network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("2", dateOfBirth = Map(date("1970-01-01") -> 1), gender = Map("F" -> 1), network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
                )
                saveSubjectTestData(path, ForianEtlPaths.claimPatientSuffix, claimPatient)

                val (pharmacySubject, pharmacyEvent) = setupFakePharmacyDataPerPatient(claimPatient, date("2020-06-20"), "0")
                val (closedPharmacySubject, closedPharmacyEvent) = setupFakePharmacyDataPerPatient(claimPatient, date("2020-06-21"), "0", isOpen = false)
                saveSubjectTestData(path, ForianEtlPaths.pharmacySuffix, pharmacySubject ++ closedPharmacySubject)
                saveEventTestData(path, ForianEtlPaths.pharmacySuffix, pharmacyEvent ++ closedPharmacyEvent)

                val openedClaimHeaderDs = Seq(
                    ForianSubject("1", dateOfDeath = date("2020-04-01"), network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("2", dateOfDeath = date("2020-05-01"), network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("1", dateOfDeath = date("2020-04-05"), network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("2", dateOfDeath = date("2020-02-05"), network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy))
                )
                saveSubjectTestData(path, ForianEtlPaths.claimHeaderSuffix, openedClaimHeaderDs)

                val actualSubjects = processData(path).sort("id").collectAsList()

                actualSubjects.get(0).info.dateOfDeath should be(date("2020-04-05"))
                actualSubjects.get(0).info.calculatedDateOfDeath should be(date("2020-06-21")) // adjusted with last event
                actualSubjects.get(1).info.dateOfDeath should be(date("2020-05-01"))
                actualSubjects.get(1).info.calculatedDateOfDeath should be(date("2020-06-21"))
            })
        }

        it("when calculating date of birth should pick the earliest date when there are more than one date of birth with the same count") {
            withTemp(path => {
                setupEmptyTestData(path)

                val claimPatient = Seq(
                    // two subjects with the same count of date of birth. One subject has later date first and another subject has earlier date first. The result should be the earlier date in both cases regardless of order.
                    ForianSubject("1", dateOfBirth = Map(date("1971-01-01") -> 1, date("1970-01-01") -> 1), gender = Map("M" -> 1), network = NetworkStruct(hasOpen = true)),
                    ForianSubject("2", dateOfBirth = Map(date("1970-01-01") -> 1, date("1971-01-01") -> 1), gender = Map("M" -> 1), network = NetworkStruct(hasOpen = true)),
                )
                saveSubjectTestData(path, ForianEtlPaths.claimPatientSuffix, claimPatient)

                val (pharmacySubject, pharmacyEvent) = setupFakePharmacyDataPerPatient(claimPatient, date("2020-06-20"), "0")
                saveSubjectTestData(path, ForianEtlPaths.pharmacySuffix, pharmacySubject)
                saveEventTestData(path, ForianEtlPaths.pharmacySuffix, pharmacyEvent)

                val actualSubjects = processData(path).collectAsList()

                actualSubjects.get(0).info.dateOfBirth should be(date("1970-01-01"))
                actualSubjects.get(1).info.dateOfBirth should be(date("1970-01-01"))
            })
        }
    }

    describe("Open and Closed datasets") {
        it("should load and tag subject network correctly for patients come from closed or open or both claim patient files") {
            withTemp(path => {
                setupEmptyTestData(path)

                val claimPatient = Seq(
                    ForianSubject("10043789", Map(date("1980-01-01") -> 1), null, Map(Male -> 1), NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("400ASDFG", Map(date("1960-01-01") -> 1), null, Map(Female -> 1), NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
                )
                val closedClaimPatient = Seq(
                    ForianSubject("54321", Map(date("1970-01-01") -> 1), null, Map(Male -> 1), NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("400ASDFG", Map(date("1960-01-01") -> 1), null, Map(Female -> 1), NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy))
                )


                val (pharmacySubject, pharmacyEvent) = setupFakePharmacyDataPerPatient(claimPatient, date("2020-06-20"), "0")
                val (closedPharmacySubject, closedPharmacyEvent) = setupFakePharmacyDataPerPatient(closedClaimPatient, date("2020-06-20"), "0", isOpen = false)

                saveSubjectTestData(path, ForianEtlPaths.claimPatientSuffix, claimPatient ++ closedClaimPatient)
                saveSubjectTestData(path, ForianEtlPaths.pharmacySuffix, pharmacySubject ++ closedPharmacySubject)
                saveEventTestData(path, ForianEtlPaths.pharmacySuffix, pharmacyEvent ++ closedPharmacyEvent)

                val expectedSubjects = subjects(
                    male("10043789")
                        withStartOfData "2020-06-20"
                        withEndOfData "2020-06-20"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1980-01-01"
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2020-06-20"
                        build,
                    male("54321")
                        withStartOfData "2020-06-20"
                        withEndOfData "2020-06-20"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1970-01-01"
                        tag NetworkTag -> "Closed"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2020-06-20"
                        build,
                    female("400ASDFG")
                        withStartOfData "2020-06-20"
                        withEndOfData "2020-06-20"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        tagValues(NetworkTag, Seq("Closed", "Open"))
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2020-06-20"
                        born "1960-01-01"
                        build
                )
                val actualSubjects = processData(path).sort("id")

                assertSubjectsEqualIgnoringEvents(expectedSubjects, actualSubjects)
            })
        }

        it("should load and tag subject network correctly for patients come from closed or open or both pharmacy files") {
            withTemp(path => {
                setupEmptyTestData(path)
                val pharmacySubjects = Seq(
                    ForianSubject("10043789", dateOfBirth = Map(date("1965-01-01") -> 1), network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("20092745", dateOfBirth = Map(date("1960-01-01") -> 2), network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("20092745", dateOfBirth = Map(date("1965-01-01") -> 1), network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("30043902", dateOfBirth = Map(date("1970-01-01") -> 1), network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy))
                )
                saveSubjectTestData(path, ForianEtlPaths.pharmacySuffix, pharmacySubjects)

                val pharmacyEvents = Seq(
                    ForianEvent("10043789", "**********1", date("2015-08-04"), Some(0), Category.Dispensing, Classifications.Dispensing),
                    ForianEvent("20092745", "**********1", date("2016-02-28"), Some(0), Category.Dispensing, Classifications.Dispensing),
                    ForianEvent("20092745", "**********2", date("2016-08-15"), Some(0), Category.Dispensing, Classifications.Dispensing), // overlapping patient
                    ForianEvent("30043902", "**********2", date("2017-01-01"), Some(0), Category.Dispensing, Classifications.Dispensing)
                )
                saveEventTestData(path, ForianEtlPaths.pharmacySuffix, pharmacyEvents)

                val expectedSubjects = subjects(
                    subject("10043789")
                        withStartOfData "2015-08-04"
                        withEndOfData "2015-08-04"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1965-01-01"
                        gender Unknown
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2015-08-04"
                        build,
                    subject("20092745")
                        withStartOfData "2016-02-28"
                        withEndOfData "2016-08-15"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1960-01-01"
                        gender Unknown
                        tagValues(NetworkTag, Seq("Closed", "Open"))
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2016-02-28"
                        build,
                    subject("30043902")
                        withStartOfData "2017-01-01"
                        withEndOfData "2017-01-01"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1970-01-01"
                        gender Unknown
                        tag NetworkTag -> "Closed"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2017-01-01"
                        build
                )

                val actualSubjects = processData(path).sort("id")

                assertSubjectsEqualIgnoringEvents(expectedSubjects, actualSubjects)
            })
        }

        it("should load and tag subject network correctly for patients that only exists in procedure, diagnosis and header files from both datasets") {
            withTemp(path => {
                setupEmptyTestData(path)

                val diagnosisSubjects = Seq(
                    ForianSubject("3", Map.empty, null, Map.empty, network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("4", Map.empty, null, Map.empty, network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )

                val diagnosisEvents = Seq(
                    ForianEvent("3", "D001", date("2014-01-01"), None, Category.Diagnosis, Classifications.Diagnosis),
                    ForianEvent("4", "D001", date("2014-01-01"), None, Category.Diagnosis, Classifications.Diagnosis),
                )

                val closedClaimDiagnosisSubjects = Seq(
                    ForianSubject("8", Map.empty, null, Map.empty, network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("4", Map.empty, null, Map.empty, network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )

                val closedClaimDiagnosisEvents = Seq(
                    ForianEvent("8", "D001", date("2014-01-01"), None, Category.Diagnosis, Classifications.Diagnosis),
                    ForianEvent("4", "D001", date("2014-01-01"), None, Category.Diagnosis, Classifications.Diagnosis),
                )

                saveSubjectTestData(path, ForianEtlPaths.claimDiagnosisSuffix, diagnosisSubjects ++ closedClaimDiagnosisSubjects)
                saveEventTestData(path, ForianEtlPaths.claimDiagnosisSuffix, diagnosisEvents ++ closedClaimDiagnosisEvents)

                val openClaimProcedureDS = Seq(
                    ForianEvent("5", "D001", date("2012-06-01"), None, Category.Procedure, Classifications.Procedure),
                    ForianEvent("6", "D001", date("2012-06-01"), None, Category.Procedure, Classifications.Procedure),
                    ForianEvent("9", "D001", date("2012-06-01"), None, Category.Procedure, Classifications.Procedure)
                )

                val procedureSubjects = Seq(
                    ForianSubject("5", Map.empty, null, Map.empty, network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("6", Map.empty, null, Map.empty, network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )

                val closedClaimProcedureDS = Seq(
                    ForianEvent("6", "D001", date("2012-06-01"), None, Category.Procedure, Classifications.Procedure),
                    ForianEvent("9", "D001", date("2012-06-01"), None, Category.Procedure, Classifications.Procedure)
                )

                val closedProcedureSubjects = Seq(
                    ForianSubject("6", Map.empty, null, Map.empty, network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("9", Map.empty, null, Map.empty, network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )

                saveSubjectTestData(path, ForianEtlPaths.claimProcedureSuffix, procedureSubjects ++ closedProcedureSubjects)
                saveEventTestData(path, ForianEtlPaths.claimProcedureSuffix, openClaimProcedureDS ++ closedClaimProcedureDS)

                val expectedSubjects = subjects(
                    subject("3")
                        withStartOfData "2014-01-01"
                        withEndOfData "2014-01-01"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        gender Unknown
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2010-01-01"
                        build,
                    subject("4")
                        withStartOfData "2014-01-01"
                        withEndOfData "2014-01-01"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        gender Unknown
                        tagValues(NetworkTag, Seq("Closed", "Open"))
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2010-01-01"
                        build,
                    subject("5")
                        withStartOfData "2012-06-01"
                        withEndOfData "2012-06-01"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        gender Unknown
                        tag NetworkTag -> "Open"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2010-01-01"
                        build,
                    subject("6")
                        withStartOfData "2012-06-01"
                        withEndOfData "2012-06-01"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        gender Unknown
                        tagValues(NetworkTag, Seq("Closed", "Open"))
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2010-01-01"
                        build,
                    subject("8")
                        withStartOfData "2014-01-01"
                        withEndOfData "2014-01-01"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        gender Unknown
                        tag NetworkTag -> "Closed"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2010-01-01"
                        build,
                    subject("9")
                        withStartOfData "2012-06-01"
                        withEndOfData "2012-06-01"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        gender Unknown
                        tag NetworkTag -> "Closed"
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2010-01-01"
                        build
                )
                val actualSubjects = processData(path)

                assertSubjectsEqualIgnoringEvents(expectedSubjects, actualSubjects)
            })
        }

        // edge case
        it("should load and tag subject network correctly for patients come from closed procedure and open patient file only") {
            withTemp(path => {
                setupEmptyTestData(path)

                val claimPatient = Seq(
                    ForianSubject("10043789", Map(date("1980-01-01") -> 1), null, Map(Male -> 1), NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
                )
                saveSubjectTestData(path, ForianEtlPaths.claimPatientSuffix, claimPatient)

                val claimProcedureDS = Seq(
                    ForianEvent("10043789", "P001", date("2012-06-01"), None, Category.Procedure, Classifications.Procedure)
                )

                val openProcedureSubjects = Seq(
                    ForianSubject("10043789", Map(date("1980-01-01") -> 1), null, Map(Male -> 1), NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
                )

                val closedProcedureSubjects = Seq(
                    ForianSubject("10043789", Map(date("1980-01-01") -> 1), null, Map(Male -> 1), NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy))
                )

                saveSubjectTestData(path, ForianEtlPaths.claimProcedureSuffix, openProcedureSubjects ++ closedProcedureSubjects)
                saveEventTestData(path, ForianEtlPaths.claimProcedureSuffix, claimProcedureDS)

                val expectedSubjects = subjects(
                    male("10043789")
                        withStartOfData "2012-06-01"
                        withEndOfData "2012-06-01"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1980-01-01"
                        tagValues(NetworkTag, Seq("Closed", "Open"))
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2010-01-01"
                        build
                )
                val actualSubjects = processData(path).sort("id")

                assertSubjectsEqualIgnoringEvents(expectedSubjects, actualSubjects)
            })
        }

        it("should use both dataset to decide patient DoB, DoD and Gender for the same patient id") {
            withTemp(path => {
                setupEmptyTestData(path)

                val claimPatient = Seq(
                    ForianSubject("400ASDFG", Map(date("1960-01-01") -> 1), null, Map(Male -> 1), NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )
                val (openPharmacySubject, openPharmacyEvent) = setupFakePharmacyDataPerPatient(claimPatient, date("2020-06-20"), "0")

                val closedClaimPatient = Seq(
                    ForianSubject("400ASDFG", Map(date("1961-01-01") -> 2), null, Map(Male -> 1, Female -> 1), NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )
                val (closedPharmacySubject, closedPharmacyEvent) = setupFakePharmacyDataPerPatient(closedClaimPatient, date("2020-06-20"), "0", isOpen = false)

                saveSubjectTestData(path, ForianEtlPaths.claimPatientSuffix, claimPatient ++ closedClaimPatient)
                saveSubjectTestData(path, ForianEtlPaths.pharmacySuffix, openPharmacySubject ++ closedPharmacySubject)
                saveEventTestData(path, ForianEtlPaths.pharmacySuffix, openPharmacyEvent ++ closedPharmacyEvent)

                val claimHeaders = Seq(
                    ForianSubject("400ASDFG", dateOfDeath = date("2020-07-01"), network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("400ASDFG", dateOfDeath = date("2020-09-01"), network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy))
                )
                saveSubjectTestData(path, ForianEtlPaths.claimHeaderSuffix, claimHeaders)

                val expectedSubjects = subjects(
                    subject("400ASDFG")
                        withStartOfData "2020-06-20"
                        withEndOfData "2020-09-01"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1961-01-01" // majority of both dataset
                        gender Male // majority of both dataset
                        withDOD "2020-09-01" // latest date of both dataset
                        withCalculatedDOD "2020-09-01" // latest date of both dataset
                        tagValues(NetworkTag, Seq("Closed", "Open"))
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2020-06-20"
                        build
                )
                val actualSubjects = processData(path)

                assertSubjectsEqualIgnoringEvents(expectedSubjects, actualSubjects)
            })
        }

        it("should load and tag network correctly for dispensing, procedure, diagnosis events come from both dataset for same patient id") {
            withTemp(path => {
                setupEmptyTestData(path)

                val claimPatient = Seq(
                    ForianSubject("10043789", Map(date("1960-01-01") -> 1), null, Map(Female -> 1), NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )

                val (openPharmacySubject, openPharmacyEvent) = setupFakePharmacyDataPerPatient(claimPatient, date("2010-08-04"), "**********1")

                val closedClaimPatient = Seq(
                    ForianSubject("10043789", Map(date("1960-01-01") -> 1), null, Map(Female -> 1), NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )

                val (closedPharmacySubject, closedPharmacyEvent) = setupFakePharmacyDataPerPatient(closedClaimPatient, date("2011-02-28"), "**********2", isOpen = false)

                saveSubjectTestData(path, ForianEtlPaths.claimPatientSuffix, claimPatient ++ closedClaimPatient)
                saveSubjectTestData(path, ForianEtlPaths.pharmacySuffix, openPharmacySubject ++ closedPharmacySubject)
                saveEventTestData(path, ForianEtlPaths.pharmacySuffix, openPharmacyEvent ++ closedPharmacyEvent)

                val diagnosisSubjects = Seq(
                    ForianSubject("10043789", Map.empty, null, Map.empty, network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )

                val diagnosisEvents = Seq(
                    ForianEvent("10043789", "D001", date("2014-01-01"), None, Category.Diagnosis, Classifications.Diagnosis),
                )

                val closedDiagnosisSubjects = Seq(
                    ForianSubject("10043789", Map.empty, null, Map.empty, network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                ).toDS()

                val closedDiagnosisEvents = Seq(
                    ForianEvent("10043789", "D002", date("2015-01-01"), None, Category.Diagnosis, Classifications.Diagnosis),
                ).toDS()

                saveSubjectTestData(path, ForianEtlPaths.claimDiagnosisSuffix, diagnosisSubjects ++ closedDiagnosisSubjects.collect())
                saveEventTestData(path, ForianEtlPaths.claimDiagnosisSuffix, diagnosisEvents ++ closedDiagnosisEvents.collect())

                val openProcedureSubject = Seq(
                    ForianSubject("10043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )
                val openProcedureEvent = Seq(
                    ForianEvent("10043789", "P001", date("2012-06-01"), None, Category.Procedure, Classifications.Procedure,
                        unknownProviderTags ++ Map(
                            tag(Event.StateTag -> "New York"),
                            tag(NetworkTag -> "Open")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    ),
                    ForianEvent("10043789", "P001", date("2013-01-01"), None, Category.Procedure, Classifications.Procedure,
                        unknownProviderTags ++ Map(
                            tag(Event.StateTag -> "New York"),
                            tag(NetworkTag -> "Open")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    ),
                    ForianEvent("10043789", "00003029328", date("2013-01-01"), None, Category.Dispensing, Classifications.Dispensing,
                        unknownProviderTags ++ Map(
                            tag(Event.StateTag -> "New York"),
                            tag(Event.ClaimStatusTag -> "Dispensed"),
                            tag(Event.DataSourceTag -> "Procedure"),
                            tag(NetworkTag -> "Open")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    )
                )

                val closedProcedureSubject = Seq(
                    ForianSubject("10043789", network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )
                val closedProcedureEvent = Seq(
                    ForianEvent("10043789", "P002", date("2012-01-01"), None, Category.Procedure, Classifications.Procedure,
                        unknownProviderTags ++ Map(
                            tag(Event.StateTag -> "Washington"),
                            tag(NetworkTag -> "Closed")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    ),
                    ForianEvent("10043789", "P003", date("2013-06-01"), None, Category.Procedure, Classifications.Procedure,
                        unknownProviderTags ++ Map(
                            tag(Event.StateTag -> "Washington"),
                            tag(NetworkTag -> "Closed")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    ),
                    ForianEvent("10043789", "00003029328", date("2013-06-01"), None, Category.Dispensing, Classifications.Dispensing,
                        unknownProviderTags ++ Map(
                            tag(Event.StateTag -> "Washington"),
                            tag(Event.ClaimStatusTag -> "Dispensed"),
                            tag(Event.DataSourceTag -> "Procedure"),
                            tag(NetworkTag -> "Closed")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    )
                )

                saveSubjectTestData(path, ForianEtlPaths.claimProcedureSuffix, openProcedureSubject ++ closedProcedureSubject)
                saveEventTestData(path, ForianEtlPaths.claimProcedureSuffix, openProcedureEvent ++ closedProcedureEvent)

                val expectedSubjects = subjects(
                    subject(10043789).having(
                        dispensing(1)
                            on "2010-08-04"
                            ofCode "**********1"
                            tag (Event.AgeTag -> "50")
                            classifiedAs Classifications.Dispensing,
                        dispensing(2)
                            on "2011-02-28"
                            ofCode "**********2"
                            tag (Event.AgeTag -> "51")
                            classifiedAs Classifications.Dispensing,
                        procedure(3)
                            on "2012-01-01"
                            ofCode "P002"
                            tag (Event.StateTag -> "Washington")
                            tag (Event.AgeTag -> "52")
                            tag (Event.PrescriberSpecialtyTag -> Unknown)
                            tag (Event.PrescriberDetailSpecialtyTag -> Unknown)
                            tag (Event.PrescriberIdTag -> Unknown)
                            tag (Event.ReferrerIdTag -> Unknown)
                            tag (Event.ReferrerSpecialtyTag -> Unknown)
                            tag (Event.ReferrerDetailSpecialtyTag -> Unknown)
                            seqObjectTag (Event.ProvidersTag -> Seq())
                            tag (Event.Zip5Tag -> Unknown)
                            tag (NetworkTag -> "Closed")
                            classifiedAs Classifications.Procedure,
                        procedure(4)
                            on "2012-06-01"
                            ofCode "P001"
                            tag (Event.StateTag -> "New York")
                            tag (Event.AgeTag -> "52")
                            tag (Event.PrescriberSpecialtyTag -> Unknown)
                            tag (Event.PrescriberDetailSpecialtyTag -> Unknown)
                            tag (Event.PrescriberIdTag -> Unknown)
                            tag (Event.ReferrerIdTag -> Unknown)
                            tag (Event.ReferrerSpecialtyTag -> Unknown)
                            tag (Event.ReferrerDetailSpecialtyTag -> Unknown)
                            seqObjectTag (Event.ProvidersTag -> Seq())
                            tag (Event.Zip5Tag -> Unknown)
                            tag (NetworkTag -> "Open")
                            classifiedAs Classifications.Procedure,
                        dispensing(6)
                            on "2013-01-01"
                            ofCode "00003029328"
                            tag (Event.StateTag -> "New York")
                            tag (Event.AgeTag -> "53")
                            tag (Event.PrescriberSpecialtyTag -> Unknown)
                            tag (Event.PrescriberDetailSpecialtyTag -> Unknown)
                            tag (Event.PrescriberIdTag -> Unknown)
                            tag (Event.ReferrerIdTag -> Unknown)
                            tag (Event.ReferrerSpecialtyTag -> Unknown)
                            tag (Event.ReferrerDetailSpecialtyTag -> Unknown)
                            seqObjectTag (Event.ProvidersTag -> Seq())
                            tag (Event.DataSourceTag -> "Procedure")
                            tag (Event.ClaimStatusTag -> "Dispensed")
                            tag (Event.Zip5Tag -> Unknown)
                            tag (NetworkTag -> "Open")
                            classifiedAs Classifications.Dispensing,
                        procedure(5)
                            on "2013-01-01"
                            ofCode "P001"
                            tag (Event.StateTag -> "New York")
                            tag (Event.AgeTag -> "53")
                            tag (Event.PrescriberSpecialtyTag -> Unknown)
                            tag (Event.PrescriberDetailSpecialtyTag -> Unknown)
                            tag (Event.PrescriberIdTag -> Unknown)
                            tag (Event.ReferrerIdTag -> Unknown)
                            tag (Event.ReferrerSpecialtyTag -> Unknown)
                            tag (Event.ReferrerDetailSpecialtyTag -> Unknown)
                            seqObjectTag (Event.ProvidersTag -> Seq())
                            tag (Event.Zip5Tag -> Unknown)
                            tag (NetworkTag -> "Open")
                            classifiedAs Classifications.Procedure,
                        dispensing(8)
                            on "2013-06-01"
                            ofCode "00003029328"
                            tag (Event.StateTag -> "Washington")
                            tag (Event.AgeTag -> "53")
                            tag (Event.PrescriberSpecialtyTag -> Unknown)
                            tag (Event.PrescriberDetailSpecialtyTag -> Unknown)
                            tag (Event.ReferrerIdTag -> Unknown)
                            tag (Event.PrescriberIdTag -> Unknown)
                            tag (Event.ReferrerSpecialtyTag -> Unknown)
                            tag (Event.ReferrerDetailSpecialtyTag -> Unknown)
                            seqObjectTag (Event.ProvidersTag -> Seq())
                            tag (Event.DataSourceTag -> "Procedure")
                            tag (Event.ClaimStatusTag -> "Dispensed")
                            tag (Event.Zip5Tag -> Unknown)
                            tag (NetworkTag -> "Closed")
                            classifiedAs Classifications.Dispensing,
                        procedure(7)
                            on "2013-06-01"
                            ofCode "P003"
                            tag (Event.StateTag -> "Washington")
                            tag (Event.AgeTag -> "53")
                            tag (Event.PrescriberSpecialtyTag -> Unknown)
                            tag (Event.PrescriberDetailSpecialtyTag -> Unknown)
                            tag (Event.PrescriberIdTag -> Unknown)
                            tag (Event.ReferrerIdTag -> Unknown)
                            tag (Event.ReferrerSpecialtyTag -> Unknown)
                            tag (Event.ReferrerDetailSpecialtyTag -> Unknown)
                            seqObjectTag (Event.ProvidersTag -> Seq())
                            tag (Event.Zip5Tag -> Unknown)
                            tag (NetworkTag -> "Closed")
                            classifiedAs Classifications.Procedure,
                        diagnosis(9)
                            on date("2014-01-01")
                            ofCode "D001"
                            tag (Event.AgeTag -> "54")
                            classifiedAs Classifications.Diagnosis,
                        diagnosis(10)
                            on date("2015-01-01")
                            ofCode "D002"
                            tag (Event.AgeTag -> "55")
                            classifiedAs Classifications.Diagnosis
                    )
                )
                val actualSubjects = processData(path)

                assertEventsEqual(expectedSubjects, actualSubjects)
            })
        }

        it("should load and tag network correctly for dispensing, procedure, diagnosis events come from both dataset for different patient id") {
            withTemp(path => {
                setupEmptyTestData(path)

                val claimPatient = Seq(
                    ForianSubject("10043789", Map(date("1960-01-01") -> 1), null, Map(Female -> 1), NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )

                val (openPharmacySubject, openPharmacyEvent) = setupFakePharmacyDataPerPatient(claimPatient, date("2010-08-04"), "**********1")

                val closedClaimPatient = Seq(
                    ForianSubject("20043789", Map(date("1960-01-01") -> 1), null, Map(Female -> 1), NetworkStruct(hasClosed = true)),
                )

                val (closedPharmacySubject, closedPharmacyEvent) = setupFakePharmacyDataPerPatient(closedClaimPatient, date("2011-02-28"), "**********2", isOpen = false)

                saveSubjectTestData(path, ForianEtlPaths.claimPatientSuffix, claimPatient ++ closedClaimPatient)
                saveSubjectTestData(path, ForianEtlPaths.pharmacySuffix, openPharmacySubject ++ closedPharmacySubject)
                saveEventTestData(path, ForianEtlPaths.pharmacySuffix, openPharmacyEvent ++ closedPharmacyEvent)

                val diagnosisSubjects = Seq(
                    ForianSubject("10043789", Map.empty, null, Map.empty, network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )

                val diagnosisEvents = Seq(
                    ForianEvent("10043789", "D001", date("2014-01-01"), None, Category.Diagnosis, Classifications.Diagnosis),
                )

                val closedDiagnosisSubjects = Seq(
                    ForianSubject("20043789", Map.empty, null, Map.empty, network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )

                val closedDiagnosisEvents = Seq(
                    ForianEvent("20043789", "D002", date("2015-01-01"), None, Category.Diagnosis, Classifications.Diagnosis),
                )

                saveSubjectTestData(path, ForianEtlPaths.claimDiagnosisSuffix, diagnosisSubjects ++ closedDiagnosisSubjects)
                saveEventTestData(path, ForianEtlPaths.claimDiagnosisSuffix, diagnosisEvents ++ closedDiagnosisEvents)

                val openProcedureSubject = Seq(
                    ForianSubject("10043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )
                val openProcedureEvent = Seq(
                    ForianEvent("10043789", "P001", date("2012-06-01"), None, Category.Procedure, Classifications.Procedure,
                    unknownProviderTags ++ Map(
                        tag(Event.StateTag -> "New York"),
                        tag(NetworkTag -> "Open")
                    ),
                    Map(Event.ProvidersTag -> Seq())
                ),
                    ForianEvent("10043789", "P001", date("2013-01-01"), None, Category.Procedure, Classifications.Procedure,
                        unknownProviderTags ++ Map(
                            tag(Event.StateTag -> "New York"),
                            tag(NetworkTag -> "Open")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    ),
                    ForianEvent("10043789", "00003029328", date("2013-01-01"), None, Category.Dispensing, Classifications.Dispensing,
                        unknownProviderTags ++ Map(
                            tag(Event.StateTag -> "New York"),
                            tag(Event.ClaimStatusTag -> "Dispensed"),
                            tag(Event.DataSourceTag -> "Procedure"),
                            tag(NetworkTag -> "Open")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    )
                )

                val closedProcedureSubject = Seq(
                    ForianSubject("20043789", network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )
                val closedProcedureEvent = Seq(
                    ForianEvent("20043789", "P002", date("2012-01-01"), None, Category.Procedure, Classifications.Procedure,
                        unknownProviderTags ++ Map(
                            tag(Event.StateTag -> "Washington"),
                            tag(NetworkTag -> "Closed")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    ),
                    ForianEvent("20043789", "P003", date("2013-06-01"), None, Category.Procedure, Classifications.Procedure,
                        unknownProviderTags ++ Map(
                            tag(Event.StateTag -> "Washington"),
                            tag(NetworkTag -> "Closed")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    ),
                    ForianEvent("20043789", "00003029328", date("2013-06-01"), None, Category.Dispensing, Classifications.Dispensing,
                        unknownProviderTags ++ Map(
                            tag(Event.StateTag -> "Washington"),
                            tag(Event.ClaimStatusTag -> "Dispensed"),
                            tag(Event.DataSourceTag -> "Procedure"),
                            tag(NetworkTag -> "Closed")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    )
                )

                saveSubjectTestData(path, ForianEtlPaths.claimProcedureSuffix, openProcedureSubject ++ closedProcedureSubject)
                saveEventTestData(path, ForianEtlPaths.claimProcedureSuffix, openProcedureEvent ++ closedProcedureEvent)

                val expectedSubjects = subjects(
                    subject(10043789).having(
                        dispensing(1)
                            on "2010-08-04"
                            ofCode "**********1"
                            tag (Event.AgeTag -> "50")
                            classifiedAs Classifications.Dispensing,
                        procedure(4)
                            on "2012-06-01"
                            ofCode "P001"
                            tag (Event.StateTag -> "New York")
                            tag (Event.AgeTag -> "52")
                            tag (Event.PrescriberSpecialtyTag -> Unknown)
                            tag (Event.PrescriberDetailSpecialtyTag -> Unknown)
                            tag (Event.ReferrerSpecialtyTag -> Unknown)
                            tag (Event.ReferrerDetailSpecialtyTag -> Unknown)
                            tag (Event.PrescriberIdTag -> Unknown)
                            tag (Event.ReferrerIdTag -> Unknown)
                            seqObjectTag (Event.ProvidersTag -> Seq())
                            tag (Event.Zip5Tag -> Unknown)
                            tag (NetworkTag -> "Open")
                            classifiedAs Classifications.Procedure,
                        dispensing(6)
                            on "2013-01-01"
                            ofCode "00003029328"
                            tag (Event.StateTag -> "New York")
                            tag (Event.AgeTag -> "53")
                            tag (Event.PrescriberSpecialtyTag -> Unknown)
                            tag (Event.PrescriberDetailSpecialtyTag -> Unknown)
                            tag (Event.ReferrerSpecialtyTag -> Unknown)
                            tag (Event.ReferrerDetailSpecialtyTag -> Unknown)
                            tag (Event.PrescriberIdTag -> Unknown)
                            tag (Event.ReferrerIdTag -> Unknown)
                            seqObjectTag (Event.ProvidersTag -> Seq())
                            tag (Event.DataSourceTag -> "Procedure")
                            tag (Event.ClaimStatusTag -> "Dispensed")
                            tag (Event.Zip5Tag -> Unknown)
                            tag (NetworkTag -> "Open")
                            classifiedAs Classifications.Dispensing,
                        procedure(5)
                            on "2013-01-01"
                            ofCode "P001"
                            tag (Event.StateTag -> "New York")
                            tag (Event.AgeTag -> "53")
                            tag (Event.PrescriberSpecialtyTag -> Unknown)
                            tag (Event.PrescriberDetailSpecialtyTag -> Unknown)
                            tag (Event.ReferrerSpecialtyTag -> Unknown)
                            tag (Event.ReferrerDetailSpecialtyTag -> Unknown)
                            tag (Event.PrescriberIdTag -> Unknown)
                            tag (Event.ReferrerIdTag -> Unknown)
                            seqObjectTag (Event.ProvidersTag -> Seq())
                            tag (Event.Zip5Tag -> Unknown)
                            tag (NetworkTag -> "Open")
                            classifiedAs Classifications.Procedure,
                        diagnosis(9)
                            on date("2014-01-01")
                            ofCode "D001"
                            tag (Event.AgeTag -> "54")
                            classifiedAs Classifications.Diagnosis
                    ),
                    subject(20043789).having(
                        dispensing(2)
                            on "2011-02-28"
                            ofCode "**********2"
                            tag (Event.AgeTag -> "51")
                            classifiedAs Classifications.Dispensing,
                        procedure(3)
                            on "2012-01-01"
                            ofCode "P002"
                            tag (Event.StateTag -> "Washington")
                            tag (Event.AgeTag -> "52")
                            tag (Event.PrescriberSpecialtyTag -> Unknown)
                            tag (Event.PrescriberDetailSpecialtyTag -> Unknown)
                            tag (Event.ReferrerSpecialtyTag -> Unknown)
                            tag (Event.ReferrerDetailSpecialtyTag -> Unknown)
                            tag (Event.PrescriberIdTag -> Unknown)
                            tag (Event.ReferrerIdTag -> Unknown)
                            seqObjectTag (Event.ProvidersTag -> Seq())
                            tag (Event.Zip5Tag -> Unknown)
                            tag (NetworkTag -> "Closed")
                            classifiedAs Classifications.Procedure,
                        dispensing(8)
                            on "2013-06-01"
                            ofCode "00003029328"
                            tag (Event.StateTag -> "Washington")
                            tag (Event.AgeTag -> "53")
                            tag (Event.PrescriberSpecialtyTag -> Unknown)
                            tag (Event.PrescriberDetailSpecialtyTag -> Unknown)
                            tag (Event.ReferrerSpecialtyTag -> Unknown)
                            tag (Event.ReferrerDetailSpecialtyTag -> Unknown)
                            tag (Event.ReferrerIdTag -> Unknown)
                            tag (Event.PrescriberIdTag -> Unknown)
                            seqObjectTag (Event.ProvidersTag -> Seq())
                            tag (Event.DataSourceTag -> "Procedure")
                            tag (Event.ClaimStatusTag -> "Dispensed")
                            tag (Event.Zip5Tag -> Unknown)
                            tag (NetworkTag -> "Closed")
                            classifiedAs Classifications.Dispensing,
                        procedure(7)
                            on "2013-06-01"
                            ofCode "P003"
                            tag (Event.StateTag -> "Washington")
                            tag (Event.AgeTag -> "53")
                            tag (Event.PrescriberSpecialtyTag -> Unknown)
                            tag (Event.PrescriberDetailSpecialtyTag -> Unknown)
                            tag (Event.ReferrerSpecialtyTag -> Unknown)
                            tag (Event.ReferrerDetailSpecialtyTag -> Unknown)
                            tag (Event.PrescriberIdTag -> Unknown)
                            tag (Event.ReferrerIdTag -> Unknown)
                            seqObjectTag (Event.ProvidersTag -> Seq())
                            tag (Event.Zip5Tag -> Unknown)
                            tag (NetworkTag -> "Closed")
                            classifiedAs Classifications.Procedure,
                        diagnosis(10)
                            on date("2015-01-01")
                            ofCode "D002"
                            tag (Event.AgeTag -> "55")
                            classifiedAs Classifications.Diagnosis
                    )
                )
                val actualSubjects = processData(path)

                assertEventsEqual(expectedSubjects, actualSubjects)
            })
        }
    }

    describe("StartOfData And EndOfData calculation") {
        it("startOfData should be the latest date from startOfDataset, first event, dateOfBirth") {
            withTemp(path => {
                setupEmptyTestData(path)

                val claimPatient = Seq(
                    ForianSubject("10043789", Map(date("1960-01-01") -> 1), null, Map.empty, NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("10043790", Map(date("1970-01-01") -> 1), null, Map.empty, NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("10043791", Map(date("2012-01-01") -> 1), null, Map.empty, NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
                )

                val closedClaimPatient = Seq(
                    ForianSubject("10043789", Map(date("1960-01-01") -> 1), null, Map.empty, NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )
                saveSubjectTestData(path, ForianEtlPaths.claimPatientSuffix, claimPatient ++ closedClaimPatient)

                val openClaimProcedureSubjects = Seq(
                    ForianSubject("10043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("10043790", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("10043791", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
                )
                val openClaimProcedureEvents =  Seq(
                    ForianEvent("10043789", "P001", date("2016-01-01"), None, Category.Procedure, Classifications.Procedure,
                        unknownProviderTags ++ Map(
                            tag(NetworkTag -> "Open")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    ),
                    ForianEvent("10043790", "P001", date("2016-01-01"), None, Category.Procedure, Classifications.Procedure,
                        unknownProviderTags ++ Map(
                            tag(NetworkTag -> "Open")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    ),
                    ForianEvent("10043791", "P001", date("2011-01-01"), None, Category.Procedure, Classifications.Procedure,
                        unknownProviderTags ++ Map(
                            tag(NetworkTag -> "Open")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    ),
                    ForianEvent("10043791", "P001", date("2019-01-01"), None, Category.Procedure, Classifications.Procedure,
                        unknownProviderTags ++ Map(
                            tag(NetworkTag -> "Open")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    )
                )

                val closedClaimProcedureSubjects = Seq(
                    ForianSubject("10043789", network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )

                val closedClaimProcedureEvents = Seq(
                    ForianEvent("10043789", "P001", date("2018-01-01"), None, Category.Procedure, Classifications.Procedure,
                        unknownProviderTags ++ Map(
                            tag(NetworkTag -> "Closed")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    ),
                    ForianEvent("10043789", "P001", date("2017-01-01"), None, Category.Procedure, Classifications.Procedure,
                        unknownProviderTags ++ Map(
                            tag(NetworkTag -> "Closed")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    )
                )

                saveSubjectTestData(path, ForianEtlPaths.claimProcedureSuffix, openClaimProcedureSubjects ++ closedClaimProcedureSubjects)
                saveEventTestData(path, ForianEtlPaths.claimProcedureSuffix, openClaimProcedureEvents ++ closedClaimProcedureEvents)

                val expectedSubjects = subjects(
                    subject(10043789)
                        withStartOfData "2016-01-01"
                        withEndOfData "2018-01-01"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1960-01-01"
                        gender Unknown
                        tagValues(NetworkTag, Seq("Closed", "Open"))
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2010-01-01"
                        build,
                    subject(10043790)
                        withStartOfData "2016-01-01"
                        withEndOfData "2016-01-01"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1970-01-01"
                        gender Unknown
                        tagValues(NetworkTag, Seq("Open"))
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2010-01-01"
                        build,
                    subject(10043791)
                        withStartOfData "2012-01-01"
                        withEndOfData "2019-01-01"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "2012-01-01"
                        gender Unknown
                        tagValues(NetworkTag, Seq("Open"))
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2010-01-01"
                        build
                )

                val actualSubjects = processData(path)

                assertSubjectsEqualIgnoringEvents(expectedSubjects, actualSubjects)
            })
        }

        it("endOfData should be the later date from endOfDataset, last event, dateOfDeath") {
            withTemp(path => {
                setupEmptyTestData(path)

                val claimPatient = Seq(
                    ForianSubject("10043789", Map(date("1960-01-01") -> 1), null, Map.empty, NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("10043790", Map(date("1970-01-01") -> 1), null, Map.empty, NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )

                val closedClaimPatient = Seq(
                    ForianSubject("10043789", Map(date("1960-01-01") -> 1), null, Map.empty, NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )
                saveSubjectTestData(path, ForianEtlPaths.claimPatientSuffix, claimPatient ++ closedClaimPatient)

                val claimHeader = Seq(
                    ForianSubject("10043789", dateOfDeath = date("2020-07-01"), network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("10043790", dateOfDeath = date("2019-03-01"), network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy))
                )
                saveSubjectTestData(path, ForianEtlPaths.claimHeaderSuffix, claimHeader)

                val openClaimProcedureSubjects = Seq(
                    ForianSubject("10043789", network = NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                )
                val openClaimProcedureEvents = Seq(
                    ForianEvent("10043789", "P001", date("2012-01-01"), None, Category.Procedure, Classifications.Procedure,
                        unknownProviderTags ++ Map(
                            tag(NetworkTag -> "Open")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    )
                )

               val closedClaimProcedureSubjects = Seq(
                   ForianSubject("10043789", network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                   ForianSubject("10043790", network = NetworkStruct(hasClosed = true), rawDatasource = Seq(ForianDataSource.Legacy))
                )
                val closedClaimProcedureEvents = Seq(
                    ForianEvent("10043789", "P001", date("2018-01-01"), None, Category.Procedure, Classifications.Procedure,
                        unknownProviderTags ++ Map(
                            tag(NetworkTag -> "Closed")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    ),
                    ForianEvent("10043790", "P001", date("2019-01-01"), None, Category.Procedure, Classifications.Procedure,
                        unknownProviderTags ++ Map(
                            tag(NetworkTag -> "Closed")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    ),
                    ForianEvent("10043790", "P001", date("2020-01-01"), None, Category.Procedure, Classifications.Procedure,
                        unknownProviderTags ++ Map(
                            tag(NetworkTag -> "Closed")
                        ),
                        Map(Event.ProvidersTag -> Seq())
                    )
                )
                saveSubjectTestData(path, ForianEtlPaths.claimProcedureSuffix, openClaimProcedureSubjects ++ closedClaimProcedureSubjects)
                saveEventTestData(path, ForianEtlPaths.claimProcedureSuffix, openClaimProcedureEvents ++ closedClaimProcedureEvents)

                val expectedSubjects = subjects(
                    subject(10043789)
                        withStartOfData "2012-01-01"
                        withEndOfData "2020-07-01"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1960-01-01"
                        withDOD "2020-07-01"
                        withCalculatedDOD "2020-07-01"
                        gender Unknown
                        tagValues(NetworkTag, Seq("Closed", "Open"))
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2010-01-01"
                        build,
                    subject(10043790)
                        withStartOfData "2019-01-01"
                        withEndOfData "2020-01-01"
                        withStartOfDataset "2010-01-01"
                        withEndOfDataset "2020-06-30"
                        born "1970-01-01"
                        withDOD "2019-03-01"
                        withCalculatedDOD "2020-01-01"
                        gender Unknown
                        tagValues(NetworkTag, Seq("Closed", "Open"))
                        tag RawDataSourceTag -> ForianDataSource.Legacy
                        firstDispensingDate "2010-01-01"
                        build
                )

                val actualSubjects = processData(path)

                assertSubjectsEqualIgnoringEvents(expectedSubjects, actualSubjects)
            })
        }
    }

    describe("Raw data sources") {
        it("should load and tag subject rawDatasource correctly for patients come from different raw data sources") {
            withTemp(path => {
                setupEmptyTestData(path)

                val claimPatient = Seq(
                    ForianSubject("10043789", Map(date("1980-01-01") -> 1), null, Map(Male -> 1), NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("400ASDFG", Map(date("1960-01-01") -> 1), null, Map(Female -> 1), NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy, ForianDataSource.BiMonthly))
                )

                val pharmacySubjects = Seq(
                    ForianSubject("10043789", Map(date("1980-01-01") -> 1), null, Map(Male -> 1), NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Legacy)),
                    ForianSubject("400ASDFG", Map(date("1960-01-01") -> 1), null, Map(Female -> 1), NetworkStruct(hasOpen = true), rawDatasource = Seq(ForianDataSource.Rx1))
                )


                val (pharmacySubject, pharmacyEvent) = setupFakePharmacyDataPerPatient(pharmacySubjects, date("2020-06-20"), "0")

                saveSubjectTestData(path, ForianEtlPaths.claimPatientSuffix, claimPatient)
                saveSubjectTestData(path, ForianEtlPaths.pharmacySuffix, pharmacySubject)
                saveEventTestData(path, ForianEtlPaths.pharmacySuffix, pharmacyEvent)

                // run
                val actualSubjects = processData(path)

                // assert
                val actualSubjectsMap = actualSubjects.collect().map(subject => subject.id -> subject).toMap
                actualSubjectsMap("10043789").info.tags should contain (RawDataSourceTag -> Seq(ForianDataSource.Legacy))
                actualSubjectsMap("400ASDFG").info.tags should contain (RawDataSourceTag -> Seq(ForianDataSource.Legacy, ForianDataSource.BiMonthly, ForianDataSource.Rx1))
            })
        }
    }

}
