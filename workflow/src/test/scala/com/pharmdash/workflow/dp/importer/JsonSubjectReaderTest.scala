package com.pharmdash.workflow.dp.importer

import com.pharmdash.workflow.dp.meta.processorannotations.Channel
import com.pharmdash.workflow.model.TagMetadata.toMetadata
import com.pharmdash.workflow.model.{Subject, TagMetadata}
import com.pharmdash.workflow.test.AbstractDataProcessorTestCase
import com.pharmdash.workflow.test.EventTestUtils.{date, event, subject}
import com.prospection.arch2.util.WarehouseConfiguration
import org.apache.commons.io.FileUtils
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.types.MetadataBuilder
import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner

import java.nio.file.Paths
import scala.collection.mutable.ArrayBuffer

@RunWith(classOf[JUnitRunner])
class JsonSubjectReaderTest extends AbstractDataProcessorTestCase with WarehouseConfiguration with <PERSON>mporterTestUtil {
    test("should tag metadata") {
        import spark.implicits._

        val inputDataset: Dataset[Subject] = spark.createDataset[Subject](Seq(subject("1", "M", 1900,
            ArrayBuffer(
                event(
                    101, "A", "category", date("2006-01-01"),
                    Map(
                        "age" -> Seq("10"),
                        "state" -> Seq("NSW")
                    ),
                    Seq("X")
                ),
            ),
            startOfDataset = date("2001-01-01"),
            endOfDataset = date("2027-03-31")
        )))

        val datasetPartition = writeTempSubjects(inputDataset, "json")

        val importer = new JsonSubjectReader(new com.pharmdash.workflow.dp.config.Dataset(
            datasetPartition.getSourcePath,
            Option.empty,
            "/published=2018-09-01/generated=2018-09-02"),
            datasetPartition.getCombinedPath
        )

        val results = importer.doProcess(Map.empty)

        val result = results(Channel.EVENTS).subjects.get

        result.count() // trigger the action

        val expectedSubjectMetadata = new MetadataBuilder()
            .putString("startOfDataset", "2001-01-01")
            .putString("endOfDataset", "2027-03-31")
            .putMetadata("tags", new MetadataBuilder()
                .putMetadata("gender", toMetadata(TagMetadata(true)))
                .build())
            .build()

        val expectedEventsMetadata = new MetadataBuilder()
            .putMetadata("tags", new MetadataBuilder()
                .putMetadata("age", toMetadata(TagMetadata(true)))
                .putMetadata("state", toMetadata(TagMetadata(true)))
                .build())
            .build()

        result.schema(Subject.InfoColumn).metadata shouldEqual expectedSubjectMetadata
        result.schema(Subject.EventsColumn).metadata shouldEqual expectedEventsMetadata

        FileUtils.deleteDirectory(Paths.get(datasetPartition.getSourcePath).toFile)
    }
}
