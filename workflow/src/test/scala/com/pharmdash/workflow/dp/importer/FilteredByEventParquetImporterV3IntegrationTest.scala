package com.pharmdash.workflow.dp.importer

import com.pharmdash.workflow.Results
import com.pharmdash.workflow.common.TechnicalModelEncoders._
import com.pharmdash.workflow.dp.DataProcessor
import com.pharmdash.workflow.dp.config.DatasetPartition
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.{EVENTS, REFERENCE_DATA}
import com.pharmdash.workflow.model.SubjectDistinctEventCodesMetadata.{Classifications, Enabled}
import com.pharmdash.workflow.model.{RefData, Subject}
import com.pharmdash.workflow.test.EventBuilder.{diagnosis, dispensing, procedure}
import com.pharmdash.workflow.test.EventTestUtils.date
import com.pharmdash.workflow.test.{AbstractDataProcessorTestCase, SubjectBuilder}
import com.prospection.arch2.model.Category.{Dispensing, Mortality, Pathology}
import com.prospection.arch2.model.FieldConstants
import com.prospection.arch2.util.WarehouseConfiguration
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.exception.ContextedRuntimeException
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.functions.{col, explode}
import org.apache.spark.sql.types.MetadataBuilder
import org.junit.runner.RunWith
import org.scalatest.Assertion
import org.scalatestplus.junit.JUnitRunner

import java.nio.file.Paths

@RunWith(classOf[JUnitRunner])
class FilteredByEventParquetImporterV3IntegrationTest extends AbstractDataProcessorTestCase with WarehouseConfiguration with ImporterTestUtil {

    private val ClassificationMdvItem = "MDV Item"
    private val ClassificationMdvDiagnosis = "MDV Diagnosis"
    private val ClassificationMdvProcedure = "MDV Procedure"

    test("should filter by a single event code") {
        setupTestAndCleanup(filterProcessor => {
            val codes = spark.createDataset(Array(
                RefData("drugB00", "event.code", Seq("620006542"), ClassificationMdvItem),
                RefData("drugB01www", "event.code", Seq("620006542"), ClassificationMdvItem),
                RefData("drugB20", "event.tags.somethingElse", Seq("AS1ZZXC541"), ClassificationMdvItem)
            ))

            val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

            distinctEventCodes(result) should contain theSameElementsAs Seq("620006542")
            distinctEventCategories(result) should contain theSameElementsAs Seq(Dispensing)

            result(EVENTS).dataset.get.count() shouldBe 1
        })
    }

    test("should filter by a single event code and keep all events") {
        setupTestAndCleanup(filterProcessor => {
            val codes = spark.createDataset(Array(
                RefData("drugB00", "event.code", Seq("620006542"), ClassificationMdvItem),
                RefData("drugB001", "event.code", Seq("620006542"), ClassificationMdvItem),
                RefData("drugB20", "event.tags.somethingElse", Seq("AS1ZZXC541"), ClassificationMdvItem)
            ))

            val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

            distinctEventCodes(result) should contain theSameElementsAs Seq("620006542", "B002")
            distinctEventCategories(result) should contain theSameElementsAs Seq(Dispensing, Pathology)

            result(EVENTS).dataset.get.count() shouldBe 1
        }, includeAll = true)
    }

    test("should filter by multiple event codes") {
        setupTestAndCleanup(filterProcessor => {
            val codes = spark.createDataset(Array(
                RefData("drugB00", "event.code", Seq("620006542"), ClassificationMdvItem),
                RefData("drugB001", "event.code", Seq("620006542"), ClassificationMdvItem),
                RefData("drugB20", "event.code", Seq("3B070000000000000"), ClassificationMdvItem),
                RefData(FieldConstants.DeathCode, "event.code", Seq(FieldConstants.DeathCode), ClassificationMdvItem)
            ))

            val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

            distinctEventCodes(result) should contain theSameElementsAs Seq("3B070000000000000", "620006542", FieldConstants.DeathCode)
            distinctEventCategories(result) should contain theSameElementsAs Seq(Dispensing, Pathology, Mortality)

            result(EVENTS).dataset.get.count() shouldBe 2
        })
    }

    test("should return unique subjects for duplicate events") {
        setupTestAndCleanup(filterProcessor => {
            val codes = spark.createDataset(Array(
                RefData("drugB00", "event.code", Seq("A009"), ClassificationMdvItem),
                RefData("drugB001", "event.code", Seq("A009"), ClassificationMdvItem)
            ))

            val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

            distinctEventCodes(result) should contain theSameElementsAs Seq("A009")
            distinctEventCategories(result) should contain theSameElementsAs Seq(Dispensing)

            val subjects = result(EVENTS).dataset.get.as[Subject]
            subjects.count() shouldBe 1
            subjects.flatMap(subject => subject.events).count() shouldBe 2
        })
    }

    test("should be able to filter subjects using distinctEventCodes column") {
        val dataset = createSubjectsWithFakeMetadataAndEnableSubjectDistinctEventCodes(
            SubjectBuilder.subject(10043789)
                .withDistinctEventCodesByClassification(Map(ClassificationMdvItem -> Set("A009", "A010")))
                .having(
                    dispensing(1)
                        on date("2015-08-04")
                        ofCode "A009"
                        classifiedAs ClassificationMdvItem,
                    dispensing(2)
                        on date("2016-02-28")
                        ofCode "A010"
                        classifiedAs ClassificationMdvItem,
                    dispensing(3)
                        on date("2017-01-01")
                        ofCode "A010"
                        classifiedAs ClassificationMdvItem
                ),
            SubjectBuilder.subject(10043790)
                .withDistinctEventCodesByClassification(Map(ClassificationMdvItem -> Set("A009", "A011")))
                .having(
                    dispensing(1)
                        on date("2015-08-04")
                        ofCode "A009"
                        classifiedAs ClassificationMdvItem,
                    dispensing(2)
                        on date("2016-02-28")
                        ofCode "A011"
                        classifiedAs ClassificationMdvItem,
                    dispensing(3)
                        on date("2017-01-01")
                        ofCode "A011"
                        classifiedAs ClassificationMdvItem
                ),
            SubjectBuilder.subject(10043791)
                .withDistinctEventCodesByClassification(Map(ClassificationMdvItem -> Set("A011", "A012")))
                .having(
                    dispensing(1)
                        on date("2015-08-04")
                        ofCode "A011"
                        classifiedAs ClassificationMdvItem,
                    dispensing(2)
                        on date("2016-02-28")
                        ofCode "A012"
                        classifiedAs ClassificationMdvItem,
                    dispensing(3)
                        on date("2017-01-01")
                        ofCode "A012"
                        classifiedAs ClassificationMdvItem
                )
        )
        setupTestAndCleanup(
            filterProcessor => {
                val codes = spark.createDataset(Array(
                    RefData("drugB000", "event.code", Seq("A009"), ClassificationMdvItem),
                    RefData("drugB001", "event.code", Seq("A010"), ClassificationMdvItem)
                ))

                val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

                val subjects = result(EVENTS).subjects.get.collect()
                subjects.length shouldBe 2
                subjects.map(_.id).toSet should equal(Set("10043789", "10043790"))
                subjects.find(_.id == "10043789").get.events.map(_.code) should equal(Seq("A009", "A010", "A010"))
                subjects.find(_.id == "10043789").get.events.map(_.groups) should equal(Seq(Seq("drugB000"), Seq("drugB001"), Seq("drugB001")))
                subjects.find(_.id == "10043790").get.events.map(_.code) should equal(Seq("A009"))
                subjects.find(_.id == "10043790").get.events.map(_.groups) should equal(Seq(Seq("drugB000")))
            },
            subjects = dataset
        )
    }

    test("should filter subjects using distinctEventCodes column and able to keep all events") {
        val dataset = createSubjectsWithFakeMetadataAndEnableSubjectDistinctEventCodes(
            SubjectBuilder.subject(10043789)
                .withDistinctEventCodesByClassification(Map(ClassificationMdvItem -> Set("A009", "A010")))
                .having(
                    dispensing(1)
                        on date("2015-08-04")
                        ofCode "A009"
                        classifiedAs ClassificationMdvItem,
                    dispensing(2)
                        on date("2016-02-28")
                        ofCode "A010"
                        classifiedAs ClassificationMdvItem,
                    dispensing(3)
                        on date("2017-01-01")
                        ofCode "A010"
                        classifiedAs ClassificationMdvItem
                ),
            SubjectBuilder.subject(10043790)
                .withDistinctEventCodesByClassification(Map(ClassificationMdvItem -> Set("A009", "A011")))
                .having(
                    dispensing(1)
                        on date("2015-08-04")
                        ofCode "A009"
                        classifiedAs ClassificationMdvItem,
                    dispensing(2)
                        on date("2016-02-28")
                        ofCode "A011"
                        classifiedAs ClassificationMdvItem,
                    dispensing(3)
                        on date("2017-01-01")
                        ofCode "A011"
                        classifiedAs ClassificationMdvItem
                ),
            SubjectBuilder.subject(10043791)
                .withDistinctEventCodesByClassification(Map(ClassificationMdvItem -> Set("A011", "A012")))
                .having(
                    dispensing(1)
                        on date("2015-08-04")
                        ofCode "A011"
                        classifiedAs ClassificationMdvItem,
                    dispensing(2)
                        on date("2016-02-28")
                        ofCode "A012"
                        classifiedAs ClassificationMdvItem,
                    dispensing(3)
                        on date("2017-01-01")
                        ofCode "A012"
                        classifiedAs ClassificationMdvItem
                )
        )
        setupTestAndCleanup(
            filterProcessor => {
                val codes = spark.createDataset(Array(
                    RefData("drugB000", "event.code", Seq("A009"), ClassificationMdvItem),
                    RefData("drugB001", "event.code", Seq("A010"), ClassificationMdvItem)
                ))

                val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

                val subjects = result(EVENTS).subjects.get.collect()
                subjects.length shouldBe 2
                subjects.map(_.id).toSet should equal(Set("10043789", "10043790"))
                subjects.find(_.id == "10043789").get.events.map(_.code) should equal(Seq("A009", "A010", "A010"))
                subjects.find(_.id == "10043789").get.events.map(_.groups) should equal(Seq(Seq("drugB000"), Seq("drugB001"), Seq("drugB001")))
                subjects.find(_.id == "10043790").get.events.map(_.code) should equal(Seq("A009", "A011", "A011"))
                subjects.find(_.id == "10043790").get.events.map(_.groups) should equal(Seq(Seq("drugB000"), Seq.empty[String], Seq.empty[String]))
            },
            subjects = dataset,
            includeAll = true
        )
    }

    test("should be able to filter out a subject with an event with the duplicate code from a different classification using distinctEventCodes") {
        val matchingClassification = ClassificationMdvProcedure

        val dataset = createSubjectsWithFakeMetadataAndEnableSubjectDistinctEventCodes(
            SubjectBuilder.subject(10043789)
                .withDistinctEventCodesByClassification(Map(
                    matchingClassification -> Set("A009")
                ))
                .having(
                    procedure(1)
                        on date("2015-08-04")
                        ofCode "A009"
                        classifiedAs matchingClassification
                )
        )
        setupTestAndCleanup(
            filterProcessor => {
                val codes = spark.createDataset(Array(
                    RefData("drugB000", "event.code", Seq("A009"), ClassificationMdvDiagnosis) // note that the classification of the subject's event is different
                ))

                val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

                val subjects = result(EVENTS).subjects.get.collect()
                subjects shouldBe empty
            },
            subjects = dataset
        )
    }

    test("should be able to filter events by comparing classification in the ref data with the classifications in distinctEventCodes") {
        val matchingItemCode = "10000"
        val matchingClassification = ClassificationMdvProcedure

        val dataset = createSubjectsWithFakeMetadataAndEnableSubjectDistinctEventCodes(
            SubjectBuilder.subject(1)
                .withDistinctEventCodesByClassification(Map(
                    ClassificationMdvDiagnosis -> Set("A009", matchingItemCode), // assume 20000 is procedure code but data
                    matchingClassification -> Set(matchingItemCode, "20000")
                ))
                .having(
                    diagnosis(1)
                        on date("2015-08-04")
                        ofCode "A009"
                        classifiedAs ClassificationMdvDiagnosis,
                    diagnosis(2)
                        on date("2015-08-04")
                        ofCode matchingItemCode
                        classifiedAs ClassificationMdvDiagnosis,

                    procedure(3)
                        on date("2015-08-04")
                        ofCode matchingItemCode
                        classifiedAs matchingClassification,
                    procedure(4)
                        on date("2015-08-04")
                        ofCode "20000"
                        classifiedAs matchingClassification
                )
        )
        setupTestAndCleanup(
            filterProcessor => {
                val codes = spark.createDataset(Array(
                    RefData("drugGroup", "event.code", Seq(matchingItemCode), matchingClassification)
                ))

                val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

                val subjects = result(EVENTS).subjects.get.collect()
                subjects should have length 1

                val events = subjects.head.events
                events should have length 1
                events.head.id shouldBe 3
            },
            subjects = dataset
        )
    }

    test("should be able to skip group enrichment") {
        val dataset = createSubjectsWithFakeMetadata(
            SubjectBuilder.subject(10043789)
                .withDistinctEventCodesByClassification(Map(ClassificationMdvItem -> Set("A009", "A010")))
                .having(
                    dispensing(1)
                        on date("2015-08-04")
                        ofCode "A009"
                        classifiedAs ClassificationMdvItem,
                    dispensing(2)
                        on date("2016-02-28")
                        ofCode "A010"
                        classifiedAs ClassificationMdvItem
                )
        )
        setupTestAndCleanup(
            filterProcessor => {
                val codes = spark.createDataset(Array(
                    RefData("drugB000", "event.code", Seq("A009"), ClassificationMdvItem),
                    RefData("drugB001", "event.code", Seq("A010"), ClassificationMdvItem)
                ))

                val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

                val subjects = result(EVENTS).subjects.get.collect()
                subjects(0).events.map(_.groups) should equal(Seq(Seq.empty[String], Seq.empty[String]))
            },
            subjects = dataset,
            skipGroupEnrichment = true
        )
    }

    test("should filter subjects and events by classifications in addition to the codes. In V2, subjects and events were filtered by codes only, whrereas in V3, subjects and events are filtered by classification-code pairs") {
        val matchingItemCode = "itemCode"
        val matchingClassification = ClassificationMdvProcedure

        val dataset = createSubjectsWithFakeMetadata(
            SubjectBuilder.subject(1)
                .having(
                    dispensing(1)
                        on date("2015-08-04")
                        ofCode matchingItemCode
                        classifiedAs ClassificationMdvDiagnosis,
                    dispensing(2)
                        on date("2016-02-28")
                        ofCode matchingItemCode
                        classifiedAs matchingClassification
                ),
            SubjectBuilder.subject(2)
                .having(
                    dispensing(3)
                        on date("2015-08-04")
                        ofCode matchingItemCode
                        classifiedAs ClassificationMdvDiagnosis
                )
        )
        setupTestAndCleanup(
            filterProcessor => {
                val codes = spark.createDataset(Array(
                    RefData("drugGroup", "event.code", Seq(matchingItemCode), matchingClassification)
                ))

                val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

                val subjects = result(EVENTS).subjects.get.collect()
                subjects should have length 1

                val events = subjects.head.events
                events should have length 1
                events.head.id shouldBe 2
            },
            subjects = dataset,
            skipGroupEnrichment = true
        )
    }

    test("Importing obsolete distinctEventCodes-enabled dataset will throw an error so admin users can the current dataset with the latest dataset") {
        import spark.implicits._

        val df = createSubjectsWithFakeMetadata(
            SubjectBuilder.subject(10043789)
                .having(
                    dispensing(1)
                        on date("2015-08-04")
                        ofCode "A009"
                        classifiedAs ClassificationMdvItem
                )
        )
            .map(s => ObsoleteSubjectSchemeWithDistinctEventCodes(
                s.id,
                s.info,
                s.events,
                Set("a")
            ))
            .withColumn("distinctEventCodes", col("distinctEventCodes").as("", new MetadataBuilder()
                .putBoolean(Enabled, value = true)
                .putStringArray(Classifications, Array.empty)
                .build()))
            .toDF()

        val tempDataPartition = writeDataframeWithMetadata(df)

        val processor = createProcessor(tempDataPartition, includeAll = false, skipGroupEnrichment = false)

        val caught = intercept[ContextedRuntimeException] {
            val result = processor.doProcess(Map((REFERENCE_DATA, Results(Some(spark.createDataset(Array(
                RefData("drugB000", "event.code", Seq("A009"), ClassificationMdvItem)
            )))))))

            result(EVENTS).subjects.get.collect()
        }

        caught.getMessage should include("Use the latest dataset. Your dataset is not compatible with the current Dataset Import node.")

        FileUtils.deleteDirectory(Paths.get(tempDataPartition.getSourcePath).toFile)
    }


    private def setupTestAndCleanup(
                                       test: DataProcessor => Assertion,
                                       subjects: Dataset[Subject] = sampleSubjects,
                                       includeAll: Boolean = false,
                                       skipGroupEnrichment: Boolean = false
                                   ) = {
        val dataset = writeTempSubjects(subjects)
        val filterProcessor = createProcessor(dataset, includeAll, skipGroupEnrichment)

        test.apply(filterProcessor)

        // DON'T FORGET TO CLEAN UP.
        FileUtils.deleteDirectory(Paths.get(dataset.getSourcePath).toFile)
    }

    def distinctEventCodes(result: Map[String, Results]): Array[String] = {
        import spark.implicits._
        result(EVENTS).subjects.get
            .select(explode($"events") as EVENTS).select("events.code")
            .distinct()
            .orderBy("events.code")
            .collect()
            .map(r => r(0).asInstanceOf[String])
    }

    def distinctEventCategories(result: Map[String, Results]): Array[String] = {
        import spark.implicits._
        result(EVENTS).subjects.get
            .select(explode($"events") as EVENTS).select("events.category")
            .distinct()
            .orderBy("events.category")
            .collect()
            .map(r => r(0).asInstanceOf[String])
    }

    def createProcessor(
                           dataset: DatasetPartition,
                           includeAll: Boolean,
                           skipGroupEnrichment: Boolean
                       ): DataProcessor =
        new FilteredByEventParquetImporterV3(dataset, includeAll, skipGroupEnrichment)
}
