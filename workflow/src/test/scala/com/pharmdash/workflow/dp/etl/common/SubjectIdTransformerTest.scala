package com.pharmdash.workflow.dp.etl.common

import com.pharmdash.workflow.Results
import com.pharmdash.workflow.common.TechnicalModelEncoders.recordEncoder
import com.pharmdash.workflow.dp.config.DatasetPartition
import com.pharmdash.workflow.dp.etl.common.SubjectIdTransformer.subjectIdMappingEncoder
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.EVENTS
import com.pharmdash.workflow.model.{Subject, TagMetadata}
import com.pharmdash.workflow.test.SubjectBuilder._
import com.pharmdash.workflow.test.{AbstractDataProcessorTestCase, TempFiles}
import org.apache.commons.lang3.exception.ContextedRuntimeException
import org.apache.spark.sql.Dataset
import org.junit.runner.RunWith
import org.scalatest.prop.TableDrivenPropertyChecks._
import org.scalatest.prop.TableFor1
import org.scalatestplus.junit.JUnitRunner

import java.nio.file.Path
import scala.language.postfixOps

@RunWith(classOf[JUnitRunner])
class SubjectIdTransformerTest extends AbstractDataProcessorTestCase with TempFiles {

    val SubjectTag = "original_patient_id"
    val MappingFilename = "mapping"
    val OutputPath = "output"

    test("should generate all new subject ids when there are no existing subject id mapping") {
        withTemp(path => {
            mkDir(path.toString + "/" + MappingFilename)
            mkDir(path.toString + "/" + OutputPath)

            val inputSubjects = createSubjectsWithFakeMetadata(
                subject("a")
                    build,
                subject("b")
                    build,
                subject("c")
                    build
            )

            val outputSubjects = process(path, inputSubjects)

            val outputSubjectIdMappings = readSubjectIdMappings(path)
            assert_outputSubjectIdMappings_contains_unique_ids_should_contain_required_entries(
                outputSubjectIdMappings, requiredOriginalSubjectIds = Seq("a", "b", "c")
            )
            assert_subjects_should_match_outputSubjectIdMappings(outputSubjects, outputSubjectIdMappings)
        })
    }

    test("should use existing subject ids and generate new subject ids when unnecessary") {
        withTemp(path => {
            val inputSubjectIdMappings = Seq(
                SubjectIdMapping("a", 2)
            )
            writeParquets(spark.createDataset(inputSubjectIdMappings).toDF(), MappingFilename, path)

            val inputSubjects = createSubjectsWithFakeMetadata(
                subject("a")
                    build,
                subject("b")
                    build,
                subject("c")
                    build
            )

            val outputSubjects = process(path, inputSubjects)

            val subjectIdMappings = readSubjectIdMappings(path)
            assert_outputSubjectIdMappings_contains_unique_ids_should_contain_required_entries(
                subjectIdMappings, requiredOriginalSubjectIds = Seq("a", "b", "c"), inputSubjectIdMappings
            )
            assert_subjects_should_match_outputSubjectIdMappings(outputSubjects, subjectIdMappings)
        })
    }

    test("should strictly add to existing subject id mapping file and not modify any existing entries") {
        withTemp(path => {

            val inputSubjectIdMappings = Seq(
                SubjectIdMapping("d", 2)
            )
            writeParquets(spark.createDataset(inputSubjectIdMappings).toDF(), MappingFilename, path)

            val inputSubjects = createSubjectsWithFakeMetadata(
                subject("a")
                    build,
                subject("b")
                    build,
                subject("c")
                    build
            )

            val outputSubjects = process(path, inputSubjects)

            val outputSubjectIdMappings = readSubjectIdMappings(path)
            assert_outputSubjectIdMappings_contains_unique_ids_should_contain_required_entries(
                outputSubjectIdMappings, requiredOriginalSubjectIds = Seq("a", "b", "c", "d"), inputSubjectIdMappings
            )
            assert_subjects_should_match_outputSubjectIdMappings(outputSubjects, outputSubjectIdMappings)
        })
    }

    test("should keep metadata") {
        withTemp(path => {
            mkDir(path.toString + "/" + MappingFilename)
            mkDir(path.toString + "/" + OutputPath)

            val inputSubjects = createSubjectsWithFakeMetadata(
                subject("a")
                    build,
                subject("b")
                    build,
                subject("c")
                    build
            )

            val outputSubjects = process(path, inputSubjects)

            // assert output subjects have metadata
            outputSubjects.schema(Subject.InfoColumn).metadata.contains(TagMetadata.Tags) shouldBe true
            outputSubjects.schema(Subject.EventsColumn).metadata.contains(TagMetadata.Tags) shouldBe true
        })
    }

    val invalidMappingFiles: TableFor1[Seq[SubjectIdMapping]] = Table(
        ("inputSubjectIdMappings"),
        (Seq(
            SubjectIdMapping("a", 1),
            SubjectIdMapping("b", 1)
        )),
        (Seq(
            SubjectIdMapping("a", 1),
            SubjectIdMapping("a", 1)
        )),
        (Seq(
            SubjectIdMapping("a", 1),
            SubjectIdMapping("a", 2)
        ))
    )

    forAll(invalidMappingFiles) { (inputSubjectIdMappings: Seq[SubjectIdMapping]) =>
        test("should validate existing subject id mapping file to check for strict 1 to 1 relationship: " + inputSubjectIdMappings) {
            withTemp(path => {
                writeParquets(spark.createDataset(inputSubjectIdMappings).toDF(), MappingFilename, path)

                val inputSubjects = createSubjectsWithFakeMetadata(
                    subject("a")
                        build,
                    subject("b")
                        build,
                    subject("c")
                        build
                )

                assertThrows[ContextedRuntimeException] {
                    process(path, inputSubjects)
                }
            })
        }
    }


    private def process(subjectIdMappingPath: Path, subjects: Dataset[Subject]): Dataset[Subject] = {
        val resultsPathPath = subjectIdMappingPath.toString + "/" + OutputPath + "/published=published/generated=generated"
        val datasetPartition = new DatasetPartition(sourcePath = subjectIdMappingPath.toString + "/" + OutputPath, published = "published", generated = "generated")
        val results = new SubjectIdTransformer(datasetPartition, subjectIdMappingPath.toString + "/" + MappingFilename, SubjectTag).process(Map(EVENTS -> Results(Some(subjects))))
        assertTrue(results.isEmpty)
        context.spark.read.parquet(resultsPathPath).as[Subject]
    }

    private def readSubjectIdMappings(subjectIdMappingPath: Path) = {
        context.spark
            .read
            .parquet(subjectIdMappingPath.toString + "/" + MappingFilename)
            .as[SubjectIdMapping]
            .collect()
    }

    private def assert_outputSubjectIdMappings_contains_unique_ids_should_contain_required_entries(
        outputSubjectIdMappings: Seq[SubjectIdMapping], requiredOriginalSubjectIds: Seq[String], existingSubjectIdMappings: Seq[SubjectIdMapping] = Seq.empty
    ) = {
        outputSubjectIdMappings.map(_.originalSubjectId) should contain theSameElementsAs requiredOriginalSubjectIds

        // existing subject id mappings should not change
        outputSubjectIdMappings should contain allElementsOf existingSubjectIdMappings

        // generated subject ids are all unique
        outputSubjectIdMappings.map(_.newSubjectId).distinct.size shouldBe outputSubjectIdMappings.size
    }

    private def assert_subjects_should_match_outputSubjectIdMappings(subjects: Dataset[Subject], subjectIdMappings: Seq[SubjectIdMapping]) = {
        val subjectIdMappingsFromDataset = subjects.collect().map(subject => SubjectIdMapping(subject.info.getSingleTagValue(SubjectTag).get, subject.id.toLong))
        subjectIdMappings should contain allElementsOf subjectIdMappingsFromDataset
    }
}
