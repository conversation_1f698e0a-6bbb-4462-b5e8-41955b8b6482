package com.pharmdash.workflow.dp.report

import com.pharmdash.workflow._
import com.pharmdash.workflow.dp.ExportType
import com.pharmdash.workflow.dp.config.MusicDiagramGroupConfigV4
import com.pharmdash.workflow.dp.config.MusicDiagramGroupConfigV4.{CountOfEvents, MaxValueOfEvents}
import com.pharmdash.workflow.dp.javadto.ViolationDTO
import com.pharmdash.workflow.dp.meta.processorannotations.Channel
import com.pharmdash.workflow.model.{Event, Subject}
import com.pharmdash.workflow.test.EventTestUtils.{date, event, subject}
import com.prospection.arch2.model.FieldConstants
import org.apache.spark.sql.{DataFrame, Dataset}
import org.junit.runner.RunWith
import org.mockito.Mockito.{when, withSettings}
import org.scalamock.scalatest.MockFactory
import org.scalatestplus.junit.JUnitRunner

import scala.collection.mutable.ArrayBuffer

@RunWith(classOf[JUnitRunner])
class MusicDiagramReportV4Test extends BaseReportTest with MockFactory {

    private val validationContext = mock[ValidationContext]

    override def setupContext(): Unit = {
        super.setupContext()
        when(validationContext.nodeId).thenReturn(1)
    }

    test("should ignore empty subjects") {
        val testDataRDD: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject("1", "M", 1900,
                ArrayBuffer(), tags = Map("ethnicGroup" -> Seq("martian", "earthling"), "age" -> Seq("1"))
            )
        )

        val exporter = createReport()
        val result = exporter.transformSubjects(testDataRDD)

        result.collect() should be(empty)
    }

    test("should summarize events into monthly values") {
        val testDataRDD: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject("1", "M", 1900,
                ArrayBuffer(
                    Event(1, "Metformin code", date = date("2020-01-01"), groups = Seq("Metformin")),
                    Event(2, "Metformin code", date = date("2020-01-18"), groups = Seq("Metformin")),
                    Event(3, "Metformin code", date = date("2020-05-01"), groups = Seq("Metformin")),
                    Event(4, "Blood Pressure code", date = date("2020-01-05"), value = Some(40D), groups = Seq("Blood Pressure")),
                    Event(5, "Blood Pressure code", date = date("2020-01-31"), value = Some(80D), groups = Seq("Blood Pressure")),
                    Event(6, "Blood Pressure code", date = date("2020-04-01"), value = Some(60D), groups = Seq("Blood Pressure"))
                ), tags = Map("ethnicGroup" -> Seq("martian", "earthling"), "age" -> Seq("1"))
            )
        )

        val exporter = createReport()
        val result = exporter.transformSubjects(testDataRDD)

        val expectedResult = spark.read.schema(result.schema).json(
            spark.sparkContext.parallelize(Seq(
                """
                  |{
                  |    "_subjectId": "1",
                  |    "_marketInitiationDate": "2020-01",
                  |    "music": [
                  |       {
                  |         "event": "Metformin",
                  |         "eventYearMonth": "2020-01",
                  |         "labelledValue": 2
                  |       },
                  |       {
                  |         "event": "Metformin",
                  |         "eventYearMonth": "2020-05",
                  |         "labelledValue": 1
                  |       },
                  |       {
                  |         "event": "Blood Pressure",
                  |         "eventYearMonth": "2020-01",
                  |         "labelledValue": 80
                  |       },
                  |       {
                  |         "event": "Blood Pressure",
                  |         "eventYearMonth": "2020-04",
                  |         "labelledValue": 60
                  |       }
                  |    ],
                  |    "ethnicGroup": ["earthling", "martian"],
                  |    "age": [1]
                  |}
        """.stripMargin
            ))
        )
        result.collect() should contain theSameElementsAs expectedResult.collect()
    }

    test("should calculate the market init month correctly") {
        val testDataRDD: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject("1", "M", 1900,
                ArrayBuffer(
                    Event(101, "Metformin code", date = date("2020-01-01"), groups = Seq("Metformin")),
                    Event(102, "Blood Pressure code", date = date("2020-03-05"), value = Some(40D), groups = Seq("Blood Pressure"))
                )
            ),
            subject("2", "M", 1900,
                ArrayBuffer(
                    Event(201, "Blood Pressure code", date = date("2020-02-01"), groups = Seq("Blood Pressure"))
                )
            ),
            subject("3", "M", 1900,
                ArrayBuffer(
                    Event(201, "Some other code", date = date("2020-05-01"), groups = Seq("Some other drug we are not interested in")),
                    Event(201, "Metformin code", date = date("2020-06-01"), groups = Seq("Blood Pressure"))
                )
            )
        )

        val exporter = createReport()
        val result = exporter.transformSubjects(testDataRDD)

        val subjectArray = result.collect()

        subjectArray(0).getAs[String](FieldConstants.subjectColumn) should equal("1")
        subjectArray(0).getAs[String]("_marketInitiationDate") should equal("2020-01")

        subjectArray(1).getAs[String](FieldConstants.subjectColumn) should equal("2")
        subjectArray(1).getAs[String]("_marketInitiationDate") should equal("2020-02")

        subjectArray(2).getAs[String](FieldConstants.subjectColumn) should equal("3")
        subjectArray(2).getAs[String]("_marketInitiationDate") should equal("2020-06")
    }

    test("should use groups in groupConfigs as consumedItemGroups") {
        createReport().getConsumedItemGroups() should equal(Seq("Metformin", "Blood Pressure"))
    }

    test("should have empty producedItemGroups") {
        createReport().getProducedItemGroups() should equal(Seq.empty)
    }

    test("should return validation error when multiple configs include the same group") {
        val groupConfigs = List(
            new MusicDiagramGroupConfigV4(Array("Metformin", "Blood Pressure"), CountOfEvents),
            new MusicDiagramGroupConfigV4(Array("Metformin", "Blood Pressure"), MaxValueOfEvents)
        )
        val report = createReport(groupConfigs)

        val result = report.validate()(validationContext)

        result.size shouldBe 1
        result.head.getNodeId shouldBe 1
        result.head.getType shouldBe ViolationDTO.ERROR
        result.head.getMessage should include("In node 1, one group should not be included in multiple sections. Duplicate group(s): Metformin, Blood Pressure")
    }

    def mock[T <: AnyRef](implicit manifest: Manifest[T]): T = super.mock[T](withSettings().serializable())

    private def createEventWithTag(id: Int, code: String): Event = {
        event(id, code = code, "category",
            date("2006-01-05"),
            tags = Map(),
            Seq("A", "D")
        )
    }

    def createSubjects(): Dataset[Subject] = {
        createSubjectsWithFakeMetadata(
            subject("1", "M", 1980,
                ArrayBuffer(
                    createEventWithTag(101, "A1")
                ),
                tags = Map("age" -> Seq("20")),
                endOfData = date("2019-01-01")),
            subject("2", "M", 1980,
                ArrayBuffer(
                    createEventWithTag(102, "A2"),
                    createEventWithTag(103, "A3"),
                    createEventWithTag(104, "A4")
                ),
                tags = Map("age" -> Seq("20")),
                endOfData = date("2019-01-01")),
            subject("4", "M", 1980,
                ArrayBuffer(
                    createEventWithTag(105, "A4")
                ),
                tags = Map("age" -> Seq("20")),
                endOfData = date("2019-01-01"))
        )
    }


    def createReport(groupConfigs: List[MusicDiagramGroupConfigV4] = List(
        new MusicDiagramGroupConfigV4(Array("Metformin"), CountOfEvents),
        new MusicDiagramGroupConfigV4(Array("Blood Pressure"), MaxValueOfEvents)
    ), subjectLabel: String = "patient") = new MusicDiagramReportV4(subjectLabel, groupConfigs,
        List(new ExportTag("ethnicGroup"), new ExportTag("age", ExportType.LONG)))
}
