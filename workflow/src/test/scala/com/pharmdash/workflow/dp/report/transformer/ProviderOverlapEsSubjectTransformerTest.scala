package com.pharmdash.workflow.dp.report.transformer

import com.pharmdash.workflow.dp.PotProcessor.PotTag
import com.pharmdash.workflow.dp.report.ExportTag
import com.pharmdash.workflow.dp.report.PatientsOnTreatmentReport.DontUseDaysSupplyForOnTreatment
import com.pharmdash.workflow.model.Subject
import com.pharmdash.workflow.test.AbstractDataProcessorTestCase
import com.pharmdash.workflow.test.EventTestUtils.{date, event, subject}
import org.apache.spark.sql.{Dataset, Row}
import org.junit.runner.RunWith
import org.scalatestplus.junit.JUnitRunner

import scala.collection.mutable.ArrayBuffer

@RunWith(classOf[JUnitRunner])
class ProviderOverlapEsSubjectTransformerTest extends AbstractDataProcessorTestCase {

    test("should index minimum subject and event data") {
        val testDataRDD: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject("1", "M", 1980,
                ArrayBuffer(
                    event(101, "A1", "category",
                        date("2006-01-05"),
                        Map(
                            PotTag -> Seq("a"),
                            "prescriber" -> Seq("p1"),
                            "journeySequence" -> Seq("1"),
                            "CALYEAR" -> Seq("2020"),
                            "comedsbyyr" -> Seq("a+b")
                        ),
                        Seq("a", "b")
                    )
                ),
                tags = Map("ethnicGroup" -> Seq("martian", "earthling"), "age" -> Seq("1")),
                endOfData = date("2019-01-01"))
        )

        val exporter = createMapper()

        val result = exporter.transform(testDataRDD)

        val expectedResult = spark.read.schema(result.schema).json(
            spark.sparkContext.parallelize(Seq(
                """
                  |{
                  |  "_subjectId": "1",
                  |  "overlaps": [
                  |    {
                  |      "event": "a",
                  |      "comedsbyyrAtEvent": [
                  |        "a+b"
                  |      ],
                  |      "CALYEARAtEvent": ["2020"]
                  |    }
                  |  ]
                  |}
                """.stripMargin)
            )
        )

        result.collect() should contain theSameElementsAs expectedResult.collect()
    }

    test("should index with appropriate tags") {

        val testDataRDD: Dataset[Subject] = createSubjectsWithFakeMetadata(
            subject("1", "M", 1980,
                ArrayBuffer(
                    event(101, "A1", "category",
                        date("2006-01-05"),
                        Map(
                            PotTag -> Seq("a"),
                            "prescriber" -> Seq("p1"),
                            "journeySequence" -> Seq("1"),
                            "CALYEAR" -> Seq("2020"),
                            "comedsbyyr" -> Seq("a+b")
                        ),
                        Seq("a", "b")
                    ),
                    event(102, "B1", "category",
                        date("2006-01-10"),
                        Map(
                            PotTag -> Seq("b"),
                            "CALYEAR" -> Seq("2021"),
                            "comedsbyyr" -> Seq("b+e")
                        ),
                        Seq("b", "e")
                    ),
                    event(103, "C1", "category",
                        date("2006-01-10"),
                        Map(
                            PotTag -> Seq("c"),
                            "prescriber" -> Seq("p1"),
                            "journeySequence" -> Seq("1"),
                            "CALYEAR" -> Seq("2022"),
                            "comedsbyyr" -> Seq("c+f")
                        ),
                        Seq("c", "f")
                    )
                ),
                tags = Map("ethnicGroup" -> Seq("martian", "earthling"), "age" -> Seq("1")),
                endOfData = date("2019-01-01"))
        )

        val exporter = createMapper()

        val result = exporter.transform(testDataRDD)

        val expectedResult = spark.read.schema(result.schema).json(
            spark.sparkContext.parallelize(Seq(
                """
                  |{
                  |  "_subjectId": "1",
                  |  "overlaps": [
                  |    {
                  |      "event": "a",
                  |      "comedsbyyrAtEvent": [
                  |        "a+b"
                  |      ],
                  |      "CALYEARAtEvent": ["2020"]
                  |    },
                  |    {
                  |      "event": "b",
                  |      "comedsbyyrAtEvent": [
                  |        "b+e"
                  |      ],
                  |      "CALYEARAtEvent": ["2021"]
                  |    },
                  |    {
                  |      "event": "c",
                  |      "comedsbyyrAtEvent": [
                  |        "c+f"
                  |      ],
                  |      "CALYEARAtEvent": ["2022"]
                  |    }
                  |  ]
                  |}
                """.stripMargin)
            )
        )

        // "should contain theSameElementsAs" does check without regard for order, but only at the supplied
        // collection level. Our unordered elements are nested, so we need to pull them out first, then perform
        // an "all elements match regardless of order" on that nested collection...
        val overlaps = result.head().getAs[Seq[Row]]("overlaps")
        overlaps should contain theSameElementsAs expectedResult.head().getAs[Seq[Row]]("overlaps")
    }

    private def createMapper() = {
        ProviderOverlapEsSubjectTransformer(
            Seq.empty[ExportTag],
            Seq(new ExportTag("CALYEAR"), new ExportTag("comedsbyyr"))
        )
    }
}
