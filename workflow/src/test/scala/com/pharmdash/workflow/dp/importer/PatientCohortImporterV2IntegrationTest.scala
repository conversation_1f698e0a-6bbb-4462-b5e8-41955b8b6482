package com.pharmdash.workflow.dp.importer

import com.pharmdash.workflow.Results
import com.pharmdash.workflow.common.TechnicalModelEncoders.refEncoder
import com.pharmdash.workflow.dp.config.CohortDefinition
import com.pharmdash.workflow.dp.meta.processorannotations.Channel.{EVENTS, REFERENCE_DATA}
import com.pharmdash.workflow.dp.{DataProcessor, PatientCohortType}
import com.pharmdash.workflow.model.RefData
import com.pharmdash.workflow.model.SubjectDistinctEventCodesMetadata.{Classifications, Enabled}
import com.pharmdash.workflow.test.EventBuilder.dispensing
import com.pharmdash.workflow.test.EventTestUtils.{DefaultMdvClassification, date}
import com.pharmdash.workflow.test.{AbstractDataProcessorTestCase, SubjectBuilder}
import com.prospection.arch2.util.WarehouseConfiguration
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.exception.ContextedRuntimeException
import org.apache.spark.sql.SaveMode
import org.apache.spark.sql.functions.col
import org.apache.spark.sql.types.MetadataBuilder
import org.junit.runner.RunWith
import org.scalatest.Assertion
import org.scalatestplus.junit.JUnitRunner

import java.nio.file.Paths

@RunWith(classOf[JUnitRunner])
class PatientCohortImporterV2IntegrationTest extends AbstractDataProcessorTestCase with WarehouseConfiguration with ImporterTestUtil {

    private val classification = "Forian Item"

    test("should filter subjects by cohort definition") {
        val dataset = writeTempSubjects(sampleSubjects)
        val cohortDefinition: CohortDefinition = new CohortDefinition(Some(1), "testCohortDefinition", dataset, PatientCohortType.CUSTOM)
        val subjectIdPath: String = writeSubjectIds(Array("1", "2"), cohortDefinition)

        setupTestAndCleanup(filterProcessor => {
            val codes = spark.createDataset(Array(
                RefData("drug1", "event.code", Seq("620006542"), DefaultMdvClassification),
                RefData("drug2", "event.code", Seq("B003"), DefaultMdvClassification)
            ))

            val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

            val subjects = result(EVENTS).subjects.get.collect()
            subjects.length shouldBe 2
            subjects.map(_.id).toSet should equal(Set("1", "2"))

        }, cohortDefinition = cohortDefinition, subjectIdPath = subjectIdPath)
    }

    test("Should throw an exception if cohort definition id is null") {
        val dataset = writeTempSubjects(sampleSubjects)
        val cohortDefinition: CohortDefinition = new CohortDefinition(Option.empty, "testCohortDefinition", dataset, PatientCohortType.CUSTOM)

        setupTestAndCleanup(filterProcessor => {
            val codes = spark.createDataset(Array(
                RefData("drug1", "event.code", Seq("620006542"), classification),
                RefData("drug2", "event.code", Seq("B003"), classification)
            ))

            val caught = intercept[Throwable] {
                val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))
                result(EVENTS).subjects.get.collect()
            }

            caught should be(a[ContextedRuntimeException])
        }, cohortDefinition = cohortDefinition)
    }

    test("Should throw an exception if subject list file is empty") {
        val dataset = writeTempSubjects(sampleSubjects)
        val cohortDefinition: CohortDefinition = new CohortDefinition(Some(2), "testCohortDefinition", dataset, PatientCohortType.CUSTOM)
        val subjectIdPath: String = writeSubjectIds(Array.empty, cohortDefinition)

        setupTestAndCleanup(filterProcessor => {
            val codes = spark.createDataset(Array(
                RefData("drug1", "event.code", Seq("620006542"), classification),
                RefData("drug2", "event.code", Seq("B003"), classification)
            ))

            val caught = intercept[Throwable] {
                val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))
                result(EVENTS).subjects.get.collect()
            }

            caught should be(a[ContextedRuntimeException])
        }, cohortDefinition = cohortDefinition, subjectIdPath = subjectIdPath)
    }

    test("Should throw an exception if cohort definition id does not link to a file") {
        val dataset = writeTempSubjects(sampleSubjects)
        val cohortDefinition: CohortDefinition = new CohortDefinition(Some(3), "testCohortDefinition", dataset, PatientCohortType.CUSTOM)

        setupTestAndCleanup(filterProcessor => {
            val codes = spark.createDataset(Array(
                RefData("drug1", "event.code", Seq("620006542"), classification),
                RefData("drug2", "event.code", Seq("B003"), classification)
            ))

            val caught = intercept[Throwable] {
                val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))
                result(EVENTS).subjects.get.collect()
            }

            caught should be(a[ContextedRuntimeException])
        }, cohortDefinition = cohortDefinition)
    }

    test("should be able to filter subjects using distinctEventCodes column") {
        val dataset = createSubjectsWithFakeMetadataAndEnableSubjectDistinctEventCodes(
            SubjectBuilder.subject(1)
                .withDistinctEventCodesByClassification(Map(classification -> Set("A009", "A010")))
                .having(
                    dispensing(1)
                        on date("2015-08-04")
                        ofCode "A009"
                        classifiedAs classification,
                    dispensing(2)
                        on date("2016-02-28")
                        ofCode "A010"
                        classifiedAs classification,
                    dispensing(3)
                        on date("2017-01-01")
                        ofCode "A010"
                        classifiedAs classification
                ),
            SubjectBuilder.subject(2)
                .withDistinctEventCodesByClassification(Map(classification -> Set("A009", "A011")))
                .having(
                    dispensing(1)
                        on date("2015-08-04")
                        ofCode "A009"
                        classifiedAs classification,
                    dispensing(2)
                        on date("2016-02-28")
                        ofCode "A011"
                        classifiedAs classification,
                    dispensing(3)
                        on date("2017-01-01")
                        ofCode "A011"
                        classifiedAs classification
                ),
            SubjectBuilder.subject(3)
                .withDistinctEventCodesByClassification(Map(classification -> Set("A011", "A012")))
                .having(
                    dispensing(1)
                        on date("2015-08-04")
                        ofCode "A011"
                        classifiedAs classification,
                    dispensing(2)
                        on date("2016-02-28")
                        ofCode "A012"
                        classifiedAs classification,
                    dispensing(3)
                        on date("2017-01-01")
                        ofCode "A012"
                        classifiedAs classification
                )
        )

        val cohortDefinition: CohortDefinition = new CohortDefinition(Some(4), "testCohortDefinition", writeTempSubjects(dataset), PatientCohortType.CUSTOM)
        val subjectIdPath: String = writeSubjectIds(Array("1", "2", "3"), cohortDefinition)

        setupTestAndCleanup(
            filterProcessor => {
                val codes = spark.createDataset(Array(
                    RefData("drugB000", "event.code", Seq("A009"), classification),
                    RefData("drugB001", "event.code", Seq("A010"), classification)
                ))

                val result = filterProcessor.doProcess(Map((REFERENCE_DATA, Results(Some(codes)))))

                val subjects = result(EVENTS).subjects.get.collect()
                subjects.length shouldBe 2
                subjects.map(_.id).toSet should equal(Set("1", "2"))
                subjects.find(_.id == "1").get.events.map(_.code) should equal(Seq("A009", "A010", "A010"))
                subjects.find(_.id == "1").get.events.map(_.groups) should equal(Seq(Seq("drugB000"), Seq("drugB001"), Seq("drugB001")))
                subjects.find(_.id == "2").get.events.map(_.code) should equal(Seq("A009"))
                subjects.find(_.id == "2").get.events.map(_.groups) should equal(Seq(Seq("drugB000")))
            },
            cohortDefinition = cohortDefinition, subjectIdPath = subjectIdPath
        )
    }

    test("Importing obsolete distinctEventCodes-enabled dataset will throw an error so admin users can the current dataset with the latest dataset") {
        import spark.implicits._

        val df = createSubjectsWithFakeMetadata(
            SubjectBuilder.subject(1)
                .having(
                    dispensing(1)
                        on date("2015-08-04")
                        ofCode "A009"
                        classifiedAs classification
                )
        )
            .map(s => ObsoleteSubjectSchemeWithDistinctEventCodes(
                s.id,
                s.info,
                s.events,
                Set("a")
            ))
            .withColumn("distinctEventCodes", col("distinctEventCodes").as("", new MetadataBuilder()
                .putBoolean(Enabled, value = true)
                .putStringArray(Classifications, Array.empty)
                .build()))
            .toDF()

        val tempDataPartition = writeDataframeWithMetadata(df)

        val cohortDefinition: CohortDefinition = new CohortDefinition(Some(5), "testCohortDefinition", tempDataPartition, PatientCohortType.CUSTOM)
        val subjectIdPath: String = writeSubjectIds(Array("1"), cohortDefinition)

        val processor = createProcessor(cohortDefinition, includeAll = false, skipGroupEnrichment = false)

        val caught = intercept[ContextedRuntimeException] {
            val result = processor.doProcess(Map((REFERENCE_DATA, Results(Some(spark.createDataset(Array(
                RefData("drugB000", "event.code", Seq("A009"), classification)
            )))))))

            result(EVENTS).subjects.get.collect()
        }

        caught.getMessage should include("Use the latest dataset. Your dataset is not compatible with the current Patient Cohort Importer node.")

        FileUtils.deleteDirectory(Paths.get(tempDataPartition.getSourcePath).toFile)
        FileUtils.deleteDirectory(Paths.get(subjectIdPath).toFile)
    }

    // FIXME: this causes a race condition with other tests that use the same path. Use dependency injection to control input path.
    private def setupTestAndCleanup(
                                       test: DataProcessor => Assertion,
                                       includeAll: Boolean = false,
                                       skipGroupEnrichment: Boolean = false,
                                       cohortDefinition: CohortDefinition,
                                       subjectIdPath: String = null
                                   ) = {
        val filterProcessor = createProcessor(cohortDefinition, includeAll, skipGroupEnrichment)

        test.apply(filterProcessor)

        // DON'T FORGET TO CLEAN UP.
        FileUtils.deleteDirectory(Paths.get(cohortDefinition.getDatasetPartition.getSourcePath).toFile)
        if (subjectIdPath != null) {
            FileUtils.deleteDirectory(Paths.get(subjectIdPath).toFile)
        }
    }

    private def createProcessor(
                                   cohortDefinition: CohortDefinition,
                                   includeAll: Boolean,
                                   skipGroupEnrichment: Boolean
                               ): DataProcessor =
        new PatientCohortImporterV2(cohortDefinition, includeAll, skipGroupEnrichment)

    private def writeSubjectIds(subjectIds: Array[String], cohortDefinition: CohortDefinition) = {
        import spark.implicits._
        val warehouseBasePath = context.applicationConfig.warehouseBasePath
        val target = s"$warehouseBasePath/cohorts/custom/subject_id_list/${cohortDefinition.getId.get}"
        subjectIds.toSeq.toDF().write.mode(SaveMode.Overwrite).csv(target)
        target
    }

}
