PAR1  $���/   <      A   B (,�罷   L      NSW   VIC :>�ݙQ   p      Sydney	   Melbourne ,0���i   T      2000   3000 \^냰�   .d      A address line 1<B address line 1 ^b���   /�      A address line 2c   B address line 2 $(���   D      X1   X2 $(���   D      X1   X2 $(�տ�   D      X11 Name  ѱϬ   4   �K  �B  AB  NSWVIC  	MelbourneSydney  20003000  A address line 1B address line 1  A address line 2cB address line 2  X1X2  X1X2  X11 NameX11 Name �B  �K    P   XZ   �j   �\   ��   ��   �V   �V   �V   �N   �Hspark_schema %prescriberId% L   %state% L   %city% L   %zip% L   %address1% L   %address2% L   %specialityCode% L   %prescriberSpecialty% L   %prescriberDetailSpecialty% L   %
modifiedAt%Ll   �&5 prescriberIdLP&<6 (BA     ��" &X5 stateVZ&X<6 (VICNSW     ��* &�5 cityfj&�<6 (Sydney	Melbourne     ��< &�5 zipX\&�<6 (30002000     ��. &�5 address1��&�<6 (B address line 1A address line 1     ��	^ &�5 address2��&�<6 (B address line 2A address line 2c     ��	` &�5 specialityCodeRV&�<6 (X2X1     �
�
& &�5 prescriberSpecialtyRV&�<6 (X2X1     �
�
& &�5 prescriberDetailSpecialtyRV&�<X11 NameX11 Name(X11 NameX11 Name     �
�> &�5 
modifiedAtJN&�<�K  �B   (�K  �B       �
�. �&�  ,org.apache.spark.version3.3.1 )org.apache.spark.sql.parquet.row.metadata�{"type":"struct","fields":[{"name":"prescriberId","type":"string","nullable":true,"metadata":{}},{"name":"state","type":"string","nullable":true,"metadata":{}},{"name":"city","type":"string","nullable":true,"metadata":{}},{"name":"zip","type":"string","nullable":true,"metadata":{}},{"name":"address1","type":"string","nullable":true,"metadata":{}},{"name":"address2","type":"string","nullable":true,"metadata":{}},{"name":"specialityCode","type":"string","nullable":true,"metadata":{}},{"name":"prescriberSpecialty","type":"string","nullable":true,"metadata":{}},{"name":"prescriberDetailSpecialty","type":"string","nullable":true,"metadata":{}},{"name":"modifiedAt","type":"date","nullable":true,"metadata":{}}]} Jparquet-mr version 1.12.2 (build 77e30c8093386ec52c3cfa6c34b7ef3321322c94)�                     �  PAR1