PAR1 ��ɠ   ,      1C ����
   
$   l   ����   (      M1C1C  l  l    MM  J   RF   �H   LHspark_schema %FORIAN_PATIENT_ID% L   %
BIRTH_YEAR %GENDER_CODE% L   <&5 FORIAN_PATIENT_IDFJ&<1C1C (1C1C     ��& &R5 
BIRTH_YEARBF&R<l  l   (l  l       ��. &�5 GENDER_CODEDH&�<MM (MM     ��" �&�  ,org.apache.spark.version3.3.1 )org.apache.spark.sql.parquet.row.metadata�{"type":"struct","fields":[{"name":"FORIAN_PATIENT_ID","type":"string","nullable":true,"metadata":{}},{"name":"BIRTH_YEAR","type":"integer","nullable":true,"metadata":{}},{"name":"GENDER_CODE","type":"string","nullable":true,"metadata":{}}]} Jparquet-mr version 1.12.2 (build 77e30c8093386ec52c3cfa6c34b7ef3321322c94)<       �  PAR1