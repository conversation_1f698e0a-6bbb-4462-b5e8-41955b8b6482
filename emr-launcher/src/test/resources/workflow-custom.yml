config:
  properties: {}
  refDataManagementVersion: V2
  refDataPath: s3a://pd-au-int-ref-data-v2/item-groups/published/20220606T000612Z/item-to-item-group/part-00000-7444e80b-d7bc-4bb0-9966-009f0d7116dd-c000.csv
  refDataVersionId: 20220606T000612Z
  territoryFilePath: 19a30080-526d-f543-ce15-44dd16223936/3026842/3026871
description: '#3026842 (ian-dummy)'
dryRun: false
id: 3026871
links:
- {id: 0, sinkChannel: Events, sinkNodeId: 5, sourceChannel: Events, sourceNodeId: 4}
nodes:
- id: 4
  name: NZ MOH Claims Reader
  properties:
    format: json
    value: |-
      {
        "startOfDataset": "2006-01-01",
        "endOfDataset": "2022-02-01",
        "bucket": "s3a://pd-us-int-common/user-uploads/ianlutz/data/nz/moh/raw/"
      }
  resource: com.pharmdash.workflow.dp.etl.nz.moh.MohClaimReader
- id: 5
  name: Parquet Patient Writer
  properties:
    format: json
    value: |-
      {
        "datasetPartition": {
          "sourcePath": "s3a://pd-us-int-common/user-uploads/ianlutz/data/nz/moh/dummy/",
          "id": 19,
          "published": "2022-09-15",
          "generated": "2022-09-15"
        }
      }
  resource: com.pharmdash.workflow.dp.exporter.ParquetExporterV3
