#!/usr/bin/env bash

set -vxe #echo on

set -o pipefail

# use command line argument overrides for some variables, only there
# for backward compatibility:

[ -z "$1" ] || ENV_NAME="$1"
[ -z "$2" ] || COUNTRY="$2"
[ -z "$3" ] || AWS_ACCOUNT_ID="$3"
[ -z "$4" ] || TAG_VERSION="$4"

# The LEGACY_PREFIX variable should be derived from terraform output,
# however that would introduce additional complications, and we want
# to remove legacy, not add legacy, if at all possible.  This variable
# determines a role used to update a emr crawler in the data lake
# account.

LEGACY_PREFIX="pd-deve"
[ "${ENV_NAME}" == "prd" ] && LEGACY_PREFIX="pd-prod"
[ "${ENV_NAME}" == "uat" ] && LEGACY_PREFIX="pd-uat"
[ "${ENV_NAME}" == "stg" ] && LEGACY_PREFIX="pd-stag"
[ "${ENV_NAME}" == "int" ] && LEGACY_PREFIX="pd-inte"
[ "${ENV_NAME}" == "dev" ] && LEGACY_PREFIX="pd-deve"
[ "${ENV_NAME}" == "dr"  ] && LEGACY_PREFIX="pd-prod"

if [ -z "${ENV_NAME}" ]
then
    echo "error: please specify ENV_NAME on the command line"
    exit 1
fi

if [ -z "${COUNTRY}" ]
then
    echo "error: please specify COUNTRY on the command line"
    exit 1
fi

if [ -z "${AWS_ACCOUNT_ID}" ]
then
    echo "error: please specify AWS_ACCOUNT_ID on the command line"
    exit 1
fi

if [ -z "${TAG_VERSION}" ]
then
    echo "error: please specify TAG_VERSION on the command line"
    exit 1
fi

set_aws_region() {
    if [ "$1" == "au" ]
    then
        AWS_REGION="ap-southeast-2"
    fi

    if [ "$1" == "us" ]
    then
        AWS_REGION="us-west-2"
    fi

    if [ "$1" == "jp" ]
    then
        AWS_REGION="ap-northeast-1"
    fi
}

[ -z "${AWS_REGION}"    ] && set_aws_region "$COUNTRY"

[ -z "${AWS_ROLE_NAME}" ] && AWS_ROLE_NAME="terraform-role"

roleARN="arn:aws:iam::${AWS_ACCOUNT_ID}:role/${AWS_ROLE_NAME}"

#Path of emr jar on S3
EMR_LAUNCHER_S3_KEY=s3://pd-${COUNTRY}-${ENV_NAME}-common/workflow/emr-launcher-artifact/${TAG_VERSION}/


# Fetch the dependencies jar and update the configuration:

dependenciesJar="./jar/emr-launcher-1.0-SNAPSHOT-all.jar"

[ -f ${dependenciesJar} ] || exit 1

# We modify the application.conf in the dependencies jar so that it
# contains the correct application environment:

rm -f application.conf

unzip ${dependenciesJar} application.conf

# we do the env variable 'substitution' manually here, as emr does
# not allow us to inject environment variables:

# we do not supply ES_HOST, as it is not needed.

sed -i application.conf                                \
    -e "s/application.env=dev/application.env=v2-aws/" \
    -e "s/\${?ENV_NAME}/${ENV_NAME}/"                  \
    -e "s/\${?COUNTRY}/${COUNTRY}/"                    \
    -e "s/\${?AWS_REGION}/${AWS_REGION}/"              \
    -e "s/\${?AWS_ACCOUNT_ID}/${AWS_ACCOUNT_ID}/"      \
    -e "s/\${?LEGACY_PREFIX}/${LEGACY_PREFIX}/"        \
    -e "s/\${?TAG_VERSION}/${TAG_VERSION}/"

zip -u ${dependenciesJar} application.conf

# Copy artifacts to S3

(
  set +x +v

  # assume the right role to perform the copy:
  eval "$(aws sts assume-role --role-arn ${roleARN} --role-session-name infra-build --duration-seconds 900 --output text |
          grep CREDENTIALS |
          ( read dummy AccessKeyId expiration SecretAccessKey SessionToken
            echo export AWS_ACCESS_KEY_ID=$AccessKeyId
            echo export AWS_SECRET_ACCESS_KEY=$SecretAccessKey
            echo export AWS_SESSION_TOKEN=$SessionToken
          ))"

  set -x -v

  aws s3 cp ./jar/emr-launcher-1.0-SNAPSHOT-all.jar ${EMR_LAUNCHER_S3_KEY}

)
